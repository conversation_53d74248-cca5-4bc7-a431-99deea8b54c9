package ${packageName}.system.service.${businessName}.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
    #if($table.sub)
    import java.util.ArrayList;
    import com.ruoyi.common.utils.StringUtils;
    import org.springframework.transaction.annotation.Transactional;
    import ${packageName}.system.entity.${businessName}.${subClassName};
    #end
import ${packageName}.system.mapper.${businessName}.${ClassName}Mapper;
import ${packageName}.system.entity.${businessName}.${ClassName}Entity;
import ${packageName}.system.service.${businessName}.${ClassName}Service;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ${ClassName}ServiceImpl implements ${ClassName}Service {
    @Autowired
    private ${ClassName}Mapper ${className}Mapper;

    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    @Override
    public ${ClassName}Entity select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField})
    {
        return ${className}Mapper.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
    }

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}Entity> select${ClassName}List(${ClassName}Entity ${className})
    {
        return ${className}Mapper.select${ClassName}List(${className});
    }

    /**
     * 新增${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int insert${ClassName}(${ClassName}Entity ${className})
    {

        #if($table.sub)
            int rows = ${className}Mapper.insert${ClassName}(${className});
            insert${subClassName}(${className});
            return rows;
        #else
            return ${className}Mapper.insert${ClassName}(${className});
        #end
    }

    /**
     * 修改${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int update${ClassName}(${ClassName}Entity ${className})
    {
        #if($table.sub)
                ${className}Mapper.delete${subClassName}By${subTableFkClassName}(${className}.get${pkColumn.capJavaField}());
            insert${subClassName}(${className});
        #end
        return ${className}Mapper.update${ClassName}(${className});
    }

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        #if($table.sub)
                ${className}Mapper.delete${subClassName}By${subTableFkClassName}s(${pkColumn.javaField}s);
        #end
        return ${className}Mapper.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s);
    }

    /**
     * 删除${functionName}信息
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField})
    {
        #if($table.sub)
                ${className}Mapper.delete${subClassName}By${subTableFkClassName}(${pkColumn.javaField});
        #end
        return ${className}Mapper.delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
    }
    #if($table.sub)

        /**
         * 新增${subTable.functionName}信息
         *
         * @param ${className} ${functionName}对象
         */
        public void insert${subClassName}(${ClassName}Entity ${className})
        {
            List<${subClassName}Entity> ${subclassName}EntityList = ${className}.get${subClassName}List();
            ${pkColumn.javaType} ${pkColumn.javaField} = ${className}.get${pkColumn.capJavaField}();
            if (StringUtils.isNotNull(${subclassName}List))
            {
                List<${subClassName}Entity> list = new ArrayList<${subClassName}Entity>();
                for (${subClassName}Entity ${subclassName} : ${subclassName}List)
                {
                    ${subclassName}.set${subTableFkClassName}(${pkColumn.javaField});
                    list.add(${subclassName});
                }
                if (list.size() > 0)
                {
                        ${className}Mapper.batch${subClassName}(list);
                }
            }
        }
    #end
}
