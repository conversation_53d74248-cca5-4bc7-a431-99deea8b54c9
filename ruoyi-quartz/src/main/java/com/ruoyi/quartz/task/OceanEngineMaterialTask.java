package com.ruoyi.quartz.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.base.Stopwatch;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.material.AgtDataMaterialSelfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @author: keboom
 * @date: 2024/7/16
 */
@Slf4j
@Component("OceanEngineMaterialTask")
public class OceanEngineMaterialTask {

    @Autowired
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private AgtDataMaterialSelfService agtDataMaterialSelfService;


    /**
     * 拉取有账户有消耗的素材列表
     * 三分钟一次
     */
    public void syncCostMaterial() {
        Stopwatch stopwatch = Stopwatch.createStarted();
        RedisLock lock = redisAtomicClient.getLock(RedisKeyFactory.K045.join("SyncMaterial"), 60 * 10);
        if (lock == null) {
            log.warn("syncCostMaterial获取锁失败");
            return;
        }
        try {
            // 如果在进行全量的时候，可能会花费很长时间。这是上一个全量还没跑完，新的定时任务又来了，这时主要是巨量平台的接口频率有限制
            DateTime now = DateUtil.date();
            log.info("syncCostMaterial 开始时间：{}", now.toString());
            String date = DateUtil.formatDate(now);
            agtDataMaterialSelfService.syncMaterial(date, date);
        } catch (Exception e) {
            log.error("syncCostMaterial 异常", e);
        } finally {
            stopwatch.stop();
            lock.unlock();
            log.info("syncCostMaterial ，结束时间：{} 耗时：{}", System.currentTimeMillis(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
    }

    /**
     * 拉取昨天的数据
     */
    public void syncAllYesterdayMaterial() {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            DateTime time = DateUtil.yesterday();
            log.info("syncAllYesterdayMaterial 开始时间：{}", time.toString());
            String date = DateUtil.formatDate(time);
            agtDataMaterialSelfService.syncMaterial(date, date);
        } catch (Exception e) {
            log.error("syncAllYesterdayMaterial", e);
        } finally {
            stopwatch.stop();
            log.info("syncAllYesterdayMaterial，结束时间：{} 耗时：{}", System.currentTimeMillis(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
    }

    /**
     * 同步指定日期的素材数据
     *
     * @param dateStr 开始时间 yyyy-MM-dd
     * @param offset    偏移量 最小值为 -30
     */
    public void syncDateMaterial(String dateStr, Integer offset) {
        if (!DateUtils.isValidDate(dateStr, DateUtils.YYYY_MM_DD) || offset < -30) {
            log.error("syncDateMaterial参数错误 startDate:{} offset:{}", dateStr, offset);
            return;
        }
        for (int i = offset; i <=0 ; i++) {
            Stopwatch stopwatch = Stopwatch.createStarted();
            try {
                DateTime time = DateUtil.offsetDay(DateUtil.parse(dateStr), i);
                String date = DateUtil.formatDate(time);
                log.info("syncDateMaterial 开始同步素材数据 开始时间：{} 同步的日期 {} ", System.currentTimeMillis(), date);
                agtDataMaterialSelfService.syncMaterial(date, date);
            } catch (Exception e) {
                log.error("syncDateMaterial", e);
            } finally {
                stopwatch.stop();
                log.info("syncDateMaterial，结束时间：{} 耗时：{}", System.currentTimeMillis(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
            }
        }

    }
}
