package com.ruoyi.quartz.task;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.shortplay.stat.ShortplayGdtStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 删除广点通监测数据
 * <AUTHOR>
 * @date 2024/10/14 11:50
 */

@Slf4j
@Service
public class DeleteGdtStatDataTask {
    @Autowired
    private ShortplayGdtStatService shortplayGdtStatService;
    @Scheduled(cron = "0 0 2 * * ? ")
    public void exec(){
        //每日删除7天前的数据，以免数据表过大
        shortplayGdtStatService.deleteGdtStatByDate(DateUtils.addDays(new Date(),-7));
    }
}
