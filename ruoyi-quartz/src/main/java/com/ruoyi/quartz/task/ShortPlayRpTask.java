package com.ruoyi.quartz.task;

import com.ruoyi.system.service.shortplay.rp.ShortplayRpBalanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: keboom
 * @date: 2025/5/12
 */
@Slf4j
@Component("ShortPlayRpTask")
public class ShortPlayRpTask {

    @Autowired
    private ShortplayRpBalanceService shortplayRpBalanceService;

    /**
     * 每3个自然日，余额清零
     */
    public void clearBalance() {
        // queried last_refresh three days ago
        int days = 3;

        List<Long> selectDaysAgoIds = shortplayRpBalanceService.selectDaysAgo(days);

        if (CollectionUtils.isEmpty(selectDaysAgoIds)) {
            return;
        }

        // clear balance and update last_refresh
        shortplayRpBalanceService.clearBalanceUpdateLastRefresh(selectDaysAgoIds);

    }

}
