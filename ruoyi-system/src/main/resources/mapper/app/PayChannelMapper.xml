<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.app.PayChannelMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.entity.app.PayChannelEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="company" column="company" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="state" column="state" jdbcType="TINYINT"/>
            <result property="applicant" column="applicant" jdbcType="VARCHAR"/>
            <result property="mchAppId" column="mch_app_id" jdbcType="VARCHAR"/>
            <result property="alipayPublicKey" column="alipay_public_key" jdbcType="VARCHAR"/>
            <result property="appPublicKey" column="app_public_key" jdbcType="VARCHAR"/>
            <result property="appPrivateKey" column="app_private_key" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,account,
        company,type,state,
        applicant,mch_app_id,alipay_public_key,
        app_public_key,app_private_key,remark,
        operator,gmt_create,gmt_modified
    </sql>
    <insert id="insert">
        insert into tb_pay_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name!=null">name,</if>
            <if test="account!=null">account,</if>
            <if test="company!=null">company,</if>
            <if test="type!=null">type,</if>
            <if test="state!=null">state,</if>
            <if test="applicant!=null">applicant,</if>
            <if test="mchAppId!=null">mch_app_id,</if>
            <if test="alipayPublicKey!=null">alipay_public_key,</if>
            <if test="appPublicKey!=null">app_public_key,</if>
            <if test="appPrivateKey!=null">app_private_key,</if>
            <if test="remark!=null">remark,</if>
            <if test="operator!=null">operator,</if>
        </trim>
        <trim prefix="values (" suffixOverrides="," suffix=")">
            <if test="name!=null">#{name},</if>
            <if test="account!=null">#{account},</if>
            <if test="company!=null">#{company},</if>
            <if test="type!=null">#{type},</if>
            <if test="state!=null">#{state},</if>
            <if test="applicant!=null">#{applicant},</if>
            <if test="mchAppId!=null">#{mchAppId},</if>
            <if test="alipayPublicKey!=null">#{alipayPublicKey},</if>
            <if test="appPublicKey!=null">#{appPublicKey},</if>
            <if test="appPrivateKey!=null">#{appPrivateKey},</if>
            <if test="remark!=null">#{remark},</if>
            <if test="operator!=null">#{operator},</if>
        </trim>
    </insert>
    <update id="updateById">
        update tb_pay_channel
        <trim prefix="SET" suffixOverrides=",">
            <if test="name!=null">name=#{name},</if>
            <if test="account!=null">account=#{account},</if>
            <if test="company!=null">company=#{company},</if>
            <if test="type!=null">type=#{type},</if>
            <if test="state!=null">state=#{state},</if>
            <if test="applicant!=null">applicant=#{applicant},</if>
            <if test="remark!=null">remark=#{remark},</if>
            <if test="operator!=null">operator=#{operator},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="changePayChannelState">
        update tb_pay_channel set state=#{state} where id =#{id}
    </update>
    <select id="selectByReq" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_pay_channel
        <where>
            <if test="company!=null">company like concat('%',#{company},'%')</if>
            <if test="ids!=null and ids.size()>0">
                and id in
                <foreach collection="ids" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="type!=null">and type=#{type}</if>
            <if test="state!=null">and state=#{state}</if>
        </where>
    </select>
    <select id="selectById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_pay_channel where id =#{id}
    </select>
    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_pay_channel where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>
    <select id="selectByUniqueAttr" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_pay_channel
        <where>
            <if test="type!=null">
                type=#{type}
            </if>
            <if test="name!=null or mchAppId!=null">
                and (
                <if test="name!=null">
                    name=#{name}
                </if>
                <if test="mchAppId!=null">
                    or mch_app_id=#{mchAppId}
                </if>
                )
            </if>
        </where>
    </select>
    <select id="selectByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_pay_channel where type in
        <foreach collection="list" item="type" separator="," close=")" open="(">
            #{type}
        </foreach>
    </select>
    <select id="getPayChannelByMchAppIds"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_pay_channel
        where mch_app_id in
        <foreach collection="mchAppIds" item="mchAppId" separator="," open="(" close=")">
            #{mchAppId}
        </foreach>
    </select>
</mapper>
