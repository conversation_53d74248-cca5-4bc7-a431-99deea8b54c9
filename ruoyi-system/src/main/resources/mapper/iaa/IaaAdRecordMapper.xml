<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.iaa.IaaAdRecordMapper">
    
    <resultMap type="IaaAdRecord" id="IaaAdRecordResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="userId"    column="user_id"    />
        <result property="platform"    column="platform"    />
        <result property="appId"    column="app_id"    />
        <result property="clickId"    column="click_id"    />
        <result property="promotionId"    column="promotion_id"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectIaaAdRecordVo">
        select id, cur_date, user_id, platform, app_id, click_id,promotion_id, gmt_create, gmt_modified from tb_iaa_ad_record
    </sql>

    <select id="selectIaaAdRecordList" parameterType="IaaAdRecord" resultMap="IaaAdRecordResult">
        <include refid="selectIaaAdRecordVo"/>
        <where>  
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="clickId != null  and clickId != ''"> and click_id = #{clickId}</if>
            <if test="promotionId != null  and promotionId != ''"> and promotion_id = #{promotionId}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>
    
    <select id="selectIaaAdRecordById" parameterType="String" resultMap="IaaAdRecordResult">
        <include refid="selectIaaAdRecordVo"/>
        where id = #{id}
    </select>
    <select id="countByUserIdAndDate" resultType="java.lang.Integer">
        select count(1) from tb_iaa_ad_record
        <where>
            <if test="userId != null">
                user_id = #{userId}
            </if>
            <if test="promotionId != null"> and promotion_id = #{promotionId}</if>
            <if test="startTime != null"> and gmt_create &gt;= #{startTime}</if>
            <if test="endTime != null"> and gmt_create &lt; #{endTime}</if>
        </where>
    </select>

    <insert id="insertIaaAdRecord" parameterType="IaaAdRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tb_iaa_ad_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="platform != null and platform != ''">platform,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="clickId != null and clickId != ''">click_id,</if>
            <if test="promotionId != null and promotionId != ''">promotion_id,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="platform != null and platform != ''">#{platform},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="clickId != null and clickId != ''">#{clickId},</if>
            <if test="promotionId != null and promotionId != ''">#{promotionId},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>

    <update id="updateIaaAdRecord" parameterType="IaaAdRecord">
        update tb_iaa_ad_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="platform != null and platform != ''">platform = #{platform},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="clickId != null and clickId != ''">click_id = #{clickId},</if>
            <if test="promotionId != null and promotionId != ''">promotion_id = #{promotionId},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIaaAdRecordById" parameterType="String">
        delete from tb_iaa_ad_record where id = #{id}
    </delete>

    <delete id="deleteIaaAdRecordByIds" parameterType="String">
        delete from tb_iaa_ad_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>