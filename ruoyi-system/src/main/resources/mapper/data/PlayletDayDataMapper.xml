<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.data.PlayletDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.data.PlayletDayDataEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="channelId" column="channel_id"/>
        <result property="platform" column="platform"/>
        <result property="loginPv" column="login_pv"/>
        <result property="loginUv" column="login_uv"/>
        <result property="register" column="register"/>
        <result property="rechargePv" column="recharge_pv"/>
        <result property="rechargeUv" column="recharge_uv"/>
        <result property="rechargeAmount" column="recharge_amount"/>
        <result property="watchEpisodePv" column="watch_episode_pv"/>
        <result property="watchEpisodeUv" column="watch_episode_uv"/>
        <result property="unlockEpisodePv" column="unlock_episode_pv"/>
        <result property="unlockEpisodeUv" column="unlock_episode_uv"/>
        <result property="consumePv" column="consume_pv"/>
        <result property="consumeUv" column="consume_uv"/>
        <result property="consumeCoin" column="consume_coin"/>
        <result property="refundPv" column="refund_pv"/>
        <result property="refundUv" column="refund_uv"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="advertCount" column="advert_count"/>
        <result property="onlineUserCount" column="online_user_count"/>
        <result property="advertCountUv" column="advert_count_uv"/>
        <result property="advertExposureCount" column="advert_exposure_count"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            cur_date,
            channel_id,
            platform,
            login_pv,
            login_uv,
            register,
            recharge_pv,
            recharge_uv,
            recharge_amount,
            watch_episode_pv,
            watch_episode_uv,
            unlock_episode_pv,
            unlock_episode_uv,
            consume_pv,
            consume_uv,
            consume_coin,
            refund_pv,
            refund_uv,
            refund_amount,
            online_user_count,
            advert_count_uv,
            advert_exposure_count,
            gmt_create,
            gmt_modified
    </sql>

    <select id="statisticsByDate" parameterType="com.ruoyi.system.bo.playlet.DayStatisticsBo" resultType="com.ruoyi.system.entity.data.PlayletDayDataEntity">
        select cur_date               as curDate,
               sum(register)          as register,
               sum(login_pv)          as loginPv,
               sum(login_uv)          as loginUv,
               sum(recharge_amount)   as rechargeAmount,
               sum(recharge_uv)       as rechargeUv,
               sum(recharge_pv)       as rechargePv,
               sum(consume_coin)      as consumeCoin,
               sum(watch_episode_pv)  as watchEpisodePv,
               sum(watch_episode_uv)  as watchEpisodeUv,
               sum(unlock_episode_pv) as unlockEpisodePv,
               sum(refund_pv) as refundPv,
               sum(advert_count) as advertCount,
               sum(online_user_count) as onLineUserCount,
               sum(advert_count_uv) as advertCountUv,
               sum(advert_exposure_count) as advertExposureCount,
                sum(coin_sum) as coinSum
        from tb_playlet_day_data
        <where>
            <if test="req.lastCurDate!=null">
                and cur_date &lt; #{req.lastCurDate}
            </if>
            <if test="req.startDate != null and req.endDate != null">
                and cur_date &gt;= #{req.startDate} and cur_date &lt;= #{req.endDate}
            </if>
            <if test="channelId != null">
                and channel_id = #{channelId}
            </if>
            <if test="req.platforms != null and req.platforms.size > 0">
                and platform in
                <foreach collection="req.platforms" close=")" separator="," open="(" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        group by cur_date
        order by cur_date desc
        <if test="req.pageSize!=null">
            limit #{req.pageSize}
        </if>
    </select>
    <select id="statisticsSum" parameterType="com.ruoyi.system.bo.playlet.DayStatisticsBo" resultType="com.ruoyi.system.entity.data.PlayletDayDataEntity">
        select sum(register)          as register,
               sum(login_pv)          as loginPv,
               sum(login_uv)          as loginUv,
               sum(recharge_amount)   as rechargeAmount,
               sum(recharge_uv)       as rechargeUv,
               sum(recharge_pv)       as rechargePv,
               sum(consume_coin)      as consumeCoin,
               sum(watch_episode_pv)  as watchEpisodePv,
               sum(watch_episode_uv)  as watchEpisodeUv,
               sum(unlock_episode_pv) as unlockEpisodePv,
               sum(refund_pv) as refundPv,
               sum(advert_count) as advertCount,
               sum(online_user_count) as onLineUserCount,
               sum(advert_count_uv) as advertCountUv,
               sum(advert_exposure_count) as advertExposureCount
        from tb_playlet_day_data
        <where>
            <if test="req.startDate != null and req.endDate != null">
                cur_date &gt;= #{req.startDate} and cur_date &lt;= #{req.endDate}
            </if>
            <if test="req.platforms != null and req.platforms.size > 0">
                and platform in
                <foreach collection="req.platforms" close=")" separator="," open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="channelId != null">
                and channel_id = #{channelId}
            </if>
        </where>
    </select>
    <select id="statisticsByDateAndChannel"
            parameterType="com.ruoyi.system.req.playlet.param.DayChannelStatisticsParam"
            resultType="com.ruoyi.system.entity.data.PlayletDayDataEntity">
        select cur_date as curDate,
               channel_id as channelId,
                sum(register)          as register,
               sum(recharge_amount)   as rechargeAmount,
               sum(recharge_uv)       as rechargeUv,
               sum(recharge_pv)       as rechargePv,
               sum(consume_coin)      as consumeCoin,
               sum(refund_pv) as refundPv,
                sum(advert_count) as advertCount,
                sum(online_user_count) as onLineUserCount,
                sum(watch_episode_uv) as watchEpisodeUv,
                sum(watch_episode_pv) as watchEpisodePv,
                sum(advert_exposure_count) as advertExposureCount,
                sum(advert_count_uv) as advertCountUv,
                sum(unlock_episode_pv) as unlockEpisodePv,
                sum(coin_sum) as coinSum
        from tb_playlet_day_data
        <where>
            channel_id != 0
            <if test="startDate != null and endDate != null">
                and cur_date &gt;= #{startDate} and cur_date &lt;= #{endDate}
            </if>
            <if test="lastCurDate!=null">
                and cur_date &lt; #{lastCurDate}
            </if>
            <if test="channelIds != null and channelIds.size > 0">
                and channel_id in
                <foreach collection="channelIds" close=")" separator="," open="(" item="channelId">
                    #{channelId}
                </foreach>
            </if>
        </where>
        group by cur_date,channel_id
        order by cur_date desc ,register desc
    </select>
    <select id="statisticsSumByDateAndChannel"
            parameterType="com.ruoyi.system.req.playlet.param.DayChannelStatisticsParam"
            resultType="com.ruoyi.system.entity.data.PlayletDayDataEntity">
        select
        sum(register)          as register,
        sum(recharge_amount)   as rechargeAmount,
        sum(recharge_uv)       as rechargeUv,
        sum(recharge_pv)       as rechargePv,
        sum(consume_coin)      as consumeCoin,
        sum(refund_pv) as refundPv,
        sum(advert_count) as advertCount,
        sum(online_user_count) as onLineUserCount,
        sum(watch_episode_uv) as watchEpisodeUv,
        sum(watch_episode_pv) as watchEpisodePv,
        sum(unlock_episode_pv) as unlockEpisodePv,
        sum(advert_exposure_count) as advertExposureCount,
        sum(advert_count_uv) as advertCountUv
        from tb_playlet_day_data
        <where>
            channel_id != 0
            <if test="startDate != null and endDate != null">
                and cur_date &gt;= #{startDate} and cur_date &lt;= #{endDate}
            </if>
            <if test="channelIds != null and channelIds.size > 0">
                and channel_id in
                <foreach collection="channelIds" close=")" separator="," open="(" item="channelId">
                    #{channelId}
                </foreach>
            </if>

        </where>
    </select>

</mapper>