<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.user.UserMapper">

    <resultMap type="com.ruoyi.system.entity.user.UserEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="gender" column="gender"/>
        <result property="coinSum" column="coin_sum"/>
        <result property="unionid" column="unionid"/>
        <result property="channelId" column="channel_id"/>
        <result property="channelUserId" column="channel_user_id"/>
        <result property="platformType" column="platform_type"/>
        <result property="vipExpireTime" column="vip_expire_time"/>
        <result property="engineChannel" column="engine_channel"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            nickname,
            avatar,
            gender,
            coin_sum,
            channel_id,
            channel_user_id,
            platform_type,
            vip_expire_time,
            engine_channel,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.user.UserEntity">
        INSERT INTO tb_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nickname != null">
                nickname,
            </if>
            <if test="coinSum != null">
                coin_sum,
            </if>
            <if test="avatar != null">
                avatar,
            </if>
            <if test="gender != null">
                gender
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nickname != null">
                #{nickname},
            </if>
            <if test="coinSum != null">
                #{coinSum},
            </if>
            <if test="avatar != null">
                #{avatar},
            </if>
            <if test="gender != null">
                #{gender}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_user
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.user.UserEntity">
        UPDATE tb_user
        <set>
            <if test="nickname != null">
                nickname = #{nickname},
            </if>
            <if test="coinSum != null">
                coin_sum = #{coinSum},
            </if>
            <if test="avatar != null">
                avatar = #{avatar},
            </if>
            <if test="gender != null">
                gender = #{gender}
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_user
        WHERE id = #{id}
    </select>
    <select id="selectListByReq" resultMap="BaseResultMap"
            parameterType="com.ruoyi.system.req.playlet.user.UserSearchReq">
        select
        <include refid="Base_Column_List"/>
        from tb_user
        <where>
            <if test="nickname != null">
                and nickname like concat('%',#{nickname},'%')
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="startDate != null and endDate != null">
                and gmt_create &gt;= #{startDate} and gmt_create &lt;= #{endDate}
            </if>
            <if test="platformTypes !=null and platformTypes.size > 0">
                and platform_type in
                <foreach collection="platformTypes" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by gmt_create desc
    </select>
    <select id="selectUserIdsByNickname" resultType="java.lang.Long">
        select id
        from tb_user
        where nickname like concat('%', #{nickname}, '%')
    </select>
    <select id="selectListByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_user
        where id in
        <foreach collection="userIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectListByIdOrNameSearch" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_user
        where id = #{search} or nickname like concat('%',#{search},'%')
        limit 100
    </select>
    <select id="selectAppUserListByReq" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_user
        <where>
            <if test="phoneNumber != null">
                and channel_user_id like concat('%',#{phoneNumber},'%')
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="startDate != null and endDate != null">
                and gmt_create &gt;= #{startDate} and gmt_create &lt;= #{endDate}
            </if>
            <if test="platformType!=null">
                and platform_type=#{platformType}
            </if>
            <if test="engineChannel!=null">
                and engine_channel=#{engineChannel}
            </if>
        </where>
        order by gmt_create desc
    </select>
    <select id="selectUserIdsByChannelUserId" resultType="java.lang.Long">
        select id
        from tb_user
        where tb_user.channel_user_id like concat('%', #{channelUserId}, '%')
    </select>
    <select id="selectListPageByParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_user
        <where>
            id &lt; #{lastId}
            <if test="req.phoneNumber != null">
                and channel_user_id like concat('%',#{req.phoneNumber},'%')
            </if>
            <if test="req.id != null">
                and id = #{req.id}
            </if>
            <if test="req.startDate != null and req.endDate != null">
                and gmt_create &gt;= #{req.startDate} and gmt_create &lt;= #{req.endDate}
            </if>
            <if test="req.platformType!=null">
                and platform_type=#{req.platformType}
            </if>
            <if test="req.engineChannel!=null">
                and engine_channel=#{req.engineChannel}
            </if>
        </where>
        order by id desc limit #{limit}
    </select>
    <select id="selectByPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_user where channel_user_id=#{phone} limit 1
    </select>
    <select id="selectListByPhones" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_user where channel_user_id in
        <foreach collection="phones" item="phone" open="(" separator="," close=")">
            #{phone}
        </foreach>
    </select>

</mapper>