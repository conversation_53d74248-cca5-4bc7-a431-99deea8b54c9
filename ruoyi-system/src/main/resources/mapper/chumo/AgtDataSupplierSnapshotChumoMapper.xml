<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.chumo.AgtDataSupplierSnapshotChumoMapper">

    <resultMap type="com.ruoyi.system.entity.chumo.AgtDataSupplierSnapshotChumoEntity" id="AgtDataSupplierSnapshotChumoResult">
            <result property="id"    column="id"    />
            <result property="advertiserId"    column="advertiser_id"    />
            <result property="orderPayType"    column="order_pay_type"    />
            <result property="curDate"    column="cur_date"    />
            <result property="curHour"    column="cur_hour"    />
            <result property="userId"    column="user_id"    />
            <result property="openid"    column="openid"    />
            <result property="outTradeNo"    column="out_trade_no"    />
            <result property="totalFee"    column="total_fee"    />
            <result property="state"    column="state"    />
            <result property="createdAt"    column="created_at"    />
            <result property="updatedAt"    column="updated_at"    />
            <result property="payTime"    column="pay_time"    />
            <result property="callbackTime"    column="callback_time"    />
            <result property="payType"    column="pay_type"    />
            <result property="tradeNo"    column="trade_no"    />
            <result property="description"    column="description"    />
            <result property="cmZdyId"    column="cm_zdy_id"    />
            <result property="type"    column="type"    />
            <result property="num"    column="num"    />
            <result property="episodeId"    column="episode_id"    />
            <result property="productId"    column="product_id"    />
            <result property="payTimes"    column="pay_times"    />
            <result property="videoId"    column="video_id"    />
            <result property="ipAddr"    column="ip_addr"    />
            <result property="appid"    column="appid"    />
            <result property="appType"    column="app_type"    />
            <result property="uploadState"    column="upload_state"    />
            <result property="platformPromotionId"    column="platform_promotion_id"    />
            <result property="promotionId"    column="promotion_id"    />
            <result property="sysUserId"    column="sys_user_id"    />
            <result property="videoName"    column="video_name"    />
            <result property="userCreatedAt"    column="user_created_at"    />
            <result property="gmtCreate"    column="gmt_create"    />
            <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAgtDataSupplierSnapshotChumoVo">
        select id, advertiser_id, order_pay_type, cur_date, cur_hour, user_id, openid, out_trade_no, total_fee, state, created_at, updated_at, pay_time, callback_time, pay_type, trade_no, description, cm_zdy_id, type, num, episode_id, product_id, pay_times, video_id, ip_addr, appid, app_type, upload_state, platform_promotion_id, promotion_id, sys_user_id, video_name, user_created_at, gmt_create, gmt_modified from tb_agt_data_supplier_snapshot_chumo
    </sql>

    <select id="selectAgtDataSupplierSnapshotChumoList" parameterType="com.ruoyi.system.entity.chumo.AgtDataSupplierSnapshotChumoEntity" resultMap="AgtDataSupplierSnapshotChumoResult">
        <include refid="selectAgtDataSupplierSnapshotChumoVo"/>
        <where>
                        <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
                        <if test="orderPayType != null "> and order_pay_type = #{orderPayType}</if>
                        <if test="curDate != null "> and cur_date = #{curDate}</if>
                        <if test="curHour != null "> and cur_hour = #{curHour}</if>
                        <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                        <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
                        <if test="outTradeNo != null  and outTradeNo != ''"> and out_trade_no = #{outTradeNo}</if>
                        <if test="totalFee != null "> and total_fee = #{totalFee}</if>
                        <if test="state != null "> and state = #{state}</if>
                        <if test="createdAt != null "> and created_at = #{createdAt}</if>
                        <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
                        <if test="payTime != null "> and pay_time = #{payTime}</if>
                        <if test="callbackTime != null "> and callback_time = #{callbackTime}</if>
                        <if test="payType != null "> and pay_type = #{payType}</if>
                        <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
                        <if test="description != null  and description != ''"> and description = #{description}</if>
                        <if test="cmZdyId != null  and cmZdyId != ''"> and cm_zdy_id = #{cmZdyId}</if>
                        <if test="type != null "> and type = #{type}</if>
                        <if test="num != null "> and num = #{num}</if>
                        <if test="episodeId != null "> and episode_id = #{episodeId}</if>
                        <if test="productId != null "> and product_id = #{productId}</if>
                        <if test="payTimes != null "> and pay_times = #{payTimes}</if>
                        <if test="videoId != null "> and video_id = #{videoId}</if>
                        <if test="ipAddr != null  and ipAddr != ''"> and ip_addr = #{ipAddr}</if>
                        <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                        <if test="appType != null "> and app_type = #{appType}</if>
                        <if test="uploadState != null "> and upload_state = #{uploadState}</if>
                        <if test="platformPromotionId != null  and platformPromotionId != ''"> and platform_promotion_id = #{platformPromotionId}</if>
                        <if test="promotionId != null  and promotionId != ''"> and promotion_id = #{promotionId}</if>
                        <if test="sysUserId != null "> and sys_user_id = #{sysUserId}</if>
                        <if test="videoName != null  and videoName != ''"> and video_name like concat('%', #{videoName}, '%')</if>
                        <if test="userCreatedAt != null "> and user_created_at = #{userCreatedAt}</if>
                        <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
                        <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAgtDataSupplierSnapshotChumoById" parameterType="Long" resultMap="AgtDataSupplierSnapshotChumoResult">
            <include refid="selectAgtDataSupplierSnapshotChumoVo"/>
            where id = #{id}
    </select>

    <insert id="insertAgtDataSupplierSnapshotChumo" parameterType="com.ruoyi.system.entity.chumo.AgtDataSupplierSnapshotChumoEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_agt_data_supplier_snapshot_chumo
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id,</if>
                    <if test="orderPayType != null">order_pay_type,</if>
                    <if test="curDate != null">cur_date,</if>
                    <if test="curHour != null">cur_hour,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="openid != null">openid,</if>
                    <if test="outTradeNo != null and outTradeNo != ''">out_trade_no,</if>
                    <if test="totalFee != null">total_fee,</if>
                    <if test="state != null">state,</if>
                    <if test="createdAt != null">created_at,</if>
                    <if test="updatedAt != null">updated_at,</if>
                    <if test="payTime != null">pay_time,</if>
                    <if test="callbackTime != null">callback_time,</if>
                    <if test="payType != null">pay_type,</if>
                    <if test="tradeNo != null">trade_no,</if>
                    <if test="description != null">description,</if>
                    <if test="cmZdyId != null">cm_zdy_id,</if>
                    <if test="type != null">type,</if>
                    <if test="num != null">num,</if>
                    <if test="episodeId != null">episode_id,</if>
                    <if test="productId != null">product_id,</if>
                    <if test="payTimes != null">pay_times,</if>
                    <if test="videoId != null">video_id,</if>
                    <if test="ipAddr != null">ip_addr,</if>
                    <if test="appid != null">appid,</if>
                    <if test="appType != null">app_type,</if>
                    <if test="uploadState != null">upload_state,</if>
                    <if test="platformPromotionId != null">platform_promotion_id,</if>
                    <if test="promotionId != null">promotion_id,</if>
                    <if test="sysUserId != null">sys_user_id,</if>
                    <if test="videoName != null">video_name,</if>
                    <if test="userCreatedAt != null">user_created_at,</if>
                    <if test="gmtCreate != null">gmt_create,</if>
                    <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="advertiserId != null">#{advertiserId},</if>
                    <if test="orderPayType != null">#{orderPayType},</if>
                    <if test="curDate != null">#{curDate},</if>
                    <if test="curHour != null">#{curHour},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="openid != null">#{openid},</if>
                    <if test="outTradeNo != null and outTradeNo != ''">#{outTradeNo},</if>
                    <if test="totalFee != null">#{totalFee},</if>
                    <if test="state != null">#{state},</if>
                    <if test="createdAt != null">#{createdAt},</if>
                    <if test="updatedAt != null">#{updatedAt},</if>
                    <if test="payTime != null">#{payTime},</if>
                    <if test="callbackTime != null">#{callbackTime},</if>
                    <if test="payType != null">#{payType},</if>
                    <if test="tradeNo != null">#{tradeNo},</if>
                    <if test="description != null">#{description},</if>
                    <if test="cmZdyId != null">#{cmZdyId},</if>
                    <if test="type != null">#{type},</if>
                    <if test="num != null">#{num},</if>
                    <if test="episodeId != null">#{episodeId},</if>
                    <if test="productId != null">#{productId},</if>
                    <if test="payTimes != null">#{payTimes},</if>
                    <if test="videoId != null">#{videoId},</if>
                    <if test="ipAddr != null">#{ipAddr},</if>
                    <if test="appid != null">#{appid},</if>
                    <if test="appType != null">#{appType},</if>
                    <if test="uploadState != null">#{uploadState},</if>
                    <if test="platformPromotionId != null">#{platformPromotionId},</if>
                    <if test="promotionId != null">#{promotionId},</if>
                    <if test="sysUserId != null">#{sysUserId},</if>
                    <if test="videoName != null">#{videoName},</if>
                    <if test="userCreatedAt != null">#{userCreatedAt},</if>
                    <if test="gmtCreate != null">#{gmtCreate},</if>
                    <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

    <update id="updateAgtDataSupplierSnapshotChumo" parameterType="com.ruoyi.system.entity.chumo.AgtDataSupplierSnapshotChumoEntity">
        update tb_agt_data_supplier_snapshot_chumo
        <trim prefix="SET" suffixOverrides=",">
                    <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
                    <if test="orderPayType != null">order_pay_type = #{orderPayType},</if>
                    <if test="curDate != null">cur_date = #{curDate},</if>
                    <if test="curHour != null">cur_hour = #{curHour},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="openid != null">openid = #{openid},</if>
                    <if test="outTradeNo != null and outTradeNo != ''">out_trade_no = #{outTradeNo},</if>
                    <if test="totalFee != null">total_fee = #{totalFee},</if>
                    <if test="state != null">state = #{state},</if>
                    <if test="createdAt != null">created_at = #{createdAt},</if>
                    <if test="updatedAt != null">updated_at = #{updatedAt},</if>
                    <if test="payTime != null">pay_time = #{payTime},</if>
                    <if test="callbackTime != null">callback_time = #{callbackTime},</if>
                    <if test="payType != null">pay_type = #{payType},</if>
                    <if test="tradeNo != null">trade_no = #{tradeNo},</if>
                    <if test="description != null">description = #{description},</if>
                    <if test="cmZdyId != null">cm_zdy_id = #{cmZdyId},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="num != null">num = #{num},</if>
                    <if test="episodeId != null">episode_id = #{episodeId},</if>
                    <if test="productId != null">product_id = #{productId},</if>
                    <if test="payTimes != null">pay_times = #{payTimes},</if>
                    <if test="videoId != null">video_id = #{videoId},</if>
                    <if test="ipAddr != null">ip_addr = #{ipAddr},</if>
                    <if test="appid != null">appid = #{appid},</if>
                    <if test="appType != null">app_type = #{appType},</if>
                    <if test="uploadState != null">upload_state = #{uploadState},</if>
                    <if test="platformPromotionId != null">platform_promotion_id = #{platformPromotionId},</if>
                    <if test="promotionId != null">promotion_id = #{promotionId},</if>
                    <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
                    <if test="videoName != null">video_name = #{videoName},</if>
                    <if test="userCreatedAt != null">user_created_at = #{userCreatedAt},</if>
                    <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
                    <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgtDataSupplierSnapshotChumoById" parameterType="Long">
        delete from tb_agt_data_supplier_snapshot_chumo where id = #{id}
    </delete>

    <delete id="deleteAgtDataSupplierSnapshotChumoByIds" parameterType="String">
        delete from tb_agt_data_supplier_snapshot_chumo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>