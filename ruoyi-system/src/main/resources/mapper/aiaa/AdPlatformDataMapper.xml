<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.aiaa.AdPlatformDataMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.entity.aiaa.AdPlatformDataEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="ip" column="ip" jdbcType="VARCHAR"/>
        <result property="userAgent" column="user_agent" jdbcType="VARCHAR"/>
        <result property="platformType" column="platform_type" jdbcType="INTEGER"/>
        <result property="nadKey" column="nad_key" jdbcType="VARCHAR"/>
        <result property="oaidMd5" column="oaid_md5" jdbcType="VARCHAR"/>
        <result property="imeiMd5" column="imei_md5" jdbcType="VARCHAR"/>
        <result property="idfaMd5" column="idfa_md5" jdbcType="VARCHAR"/>
        <result property="channel" column="channel" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ip,user_agent,platform_type,
        nad_key,oaid_md5,imei_md5,
        idfa_md5, `channel`, gmt_create, gmt_modified
    </sql>
    <insert id="insert">
        insert into tb_aiaa_ad_platform_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entity.ip != null">
                ip,
            </if>
            <if test="entity.userAgent != null">
                user_agent,
            </if>
            <if test="entity.platformType != null">
                platform_type,
            </if>
            <if test="entity.nadKey != null">
                nad_key,
            </if>
            <if test="entity.oaidMd5 != null">
                oaid_md5,
            </if>
            <if test="entity.imeiMd5 != null">
                imei_md5,
            </if>
            <if test="entity.idfaMd5 != null">
                idfa_md5,
            </if>
            <if test="entity.channel != null">
                `channel`,
            </if>
            <if test="entity.gmtCreate != null">
                gmt_create,
            </if>
            <if test="entity.gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entity.ip != null">
                #{entity.ip},
            </if>
            <if test="entity.userAgent != null">
                #{entity.userAgent},
            </if>
            <if test="entity.platformType != null">
               #{entity.platformType},
            </if>
            <if test="entity.nadKey != null">
                #{entity.nadKey},
            </if>
            <if test="entity.oaidMd5 != null">
               #{entity.oaidMd5},
            </if>
            <if test="entity.imeiMd5 != null">
               #{entity.imeiMd5},
            </if>
            <if test="entity.idfaMd5 != null">
                #{entity.idfaMd5},
            </if>
            <if test="entity.channel != null">
                #{entity.channel},
            </if>
            <if test="entity.gmtCreate != null">
                #{entity.gmtCreate},
            </if>
            <if test="entity.gmtModified != null">
                #{entity.gmtModified}
            </if>
        </trim>
    </insert>

    <select id="selectByIp"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_aiaa_ad_platform_data
        where ip=#{ip} and platform_type=#{platformType} order by id ;
    </select>

    <select id="selectByIpUa"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_aiaa_ad_platform_data where ip=#{ip} and user_agent=#{ua}
        order by id desc limit 1
    </select>

</mapper>
