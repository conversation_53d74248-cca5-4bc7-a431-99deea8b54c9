<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.dyiaa.TbDouyinIaaConversionConfigMapper">

    <resultMap type="TbDouyinIaaConversionConfig" id="TbDouyinIaaConversionConfigResult">
        <result property="id"    column="id"    />
        <result property="configId"    column="config_id"    />
        <result property="rebackTime"    column="reback_time"    />
        <result property="arpu"    column="arpu"    />
        <result property="ecpmPrice"    column="ecpm_price"    />
        <result property="ecpmTimes"    column="ecpm_times"    />
        <result property="fixedCost"    column="fixed_cost"    />
        <result property="optimizationGoal"    column="optimization_goal"    />
        <result property="ipu"    column="ipu"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectTbDouyinIaaConversionConfigVo">
        select id, config_id, reback_time, arpu, ecpm_price, ecpm_times, optimization_goal, fixed_cost, ipu,gmt_create, gmt_modified from tb_douyin_iaa_conversion_config
    </sql>

    <select id="selectTbDouyinIaaConversionConfigList" parameterType="TbDouyinIaaConversionConfig" resultMap="TbDouyinIaaConversionConfigResult">
        <include refid="selectTbDouyinIaaConversionConfigVo"/>
        <where>
            <if test="configId != null "> and config_id = #{configId}</if>
            <if test="rebackTime != null "> and reback_time = #{rebackTime}</if>
            <if test="arpu != null "> and arpu = #{arpu}</if>
            <if test="ecpmPrice != null "> and ecpm_price = #{ecpmPrice}</if>
            <if test="ecpmTimes != null "> and ecpm_times = #{ecpmTimes}</if>
            <if test="optimizationGoal != null"> and optimization_goal = #{optimizationGoal}</if>
        </where>
    </select>

    <select id="selectFixedCostConfigList" resultMap="TbDouyinIaaConversionConfigResult">
        <include refid="selectTbDouyinIaaConversionConfigVo"/>
        <where>
            fixed_cost > 0
        </where>
    </select>

    <select id="selectByConfigId" parameterType="String" resultMap="TbDouyinIaaConversionConfigResult">
        <include refid="selectTbDouyinIaaConversionConfigVo"/>
        where config_id = #{configId}
    </select>

    <insert id="insertTbDouyinIaaConversionConfig" parameterType="TbDouyinIaaConversionConfig" useGeneratedKeys="true" keyProperty="id">
        insert into tb_douyin_iaa_conversion_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configId != null">config_id,</if>
            <if test="rebackTime != null">reback_time,</if>
            <if test="arpu != null">arpu,</if>
            <if test="ecpmPrice != null">ecpm_price,</if>
            <if test="ecpmTimes != null">ecpm_times,</if>
            <if test="optimizationGoal != null">optimization_goal,</if>
            <if test="fixedCost != null">fixed_cost,</if>
            <if test="ipu != null">ipu,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configId != null">#{configId},</if>
            <if test="rebackTime != null">#{rebackTime},</if>
            <if test="arpu != null">#{arpu},</if>
            <if test="ecpmPrice != null">#{ecpmPrice},</if>
            <if test="ecpmTimes != null">#{ecpmTimes},</if>
            <if test="optimizationGoal != null">#{optimizationGoal},</if>
            <if test="fixedCost != null">#{fixedCost},</if>
            <if test="ipu != null">#{ipu},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
            reback_time = #{rebackTime},
            arpu = #{arpu},
            ecpm_price = #{ecpmPrice},
            ecpm_times = #{ecpmTimes},
            fixed_cost = #{fixedCost},
            optimization_goal = #{optimizationGoal},
            ipu = #{ipu},
            gmt_modified = now()
    </insert>

    <update id="updateTbDouyinIaaConversionConfig" parameterType="TbDouyinIaaConversionConfig">
        update tb_douyin_iaa_conversion_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configId != null">config_id = #{configId},</if>
            <if test="rebackTime != null">reback_time = #{rebackTime},</if>
            <if test="arpu != null">arpu = #{arpu},</if>
            <if test="ecpmPrice != null">ecpm_price = #{ecpmPrice},</if>
            <if test="ecpmTimes != null">ecpm_times = #{ecpmTimes},</if>
            <if test="optimizationGoal != null">optimization_goal = #{optimizationGoal},</if>
            <if test="fixedCost != null">fixed_cost = #{fixedCost},</if>
            <if test="gmtCreate != null">gmt_create = #{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>
