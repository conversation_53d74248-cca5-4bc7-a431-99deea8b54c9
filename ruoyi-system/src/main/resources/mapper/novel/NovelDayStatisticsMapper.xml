<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.novel.NovelDayStatisticsMapper">

    <resultMap type="com.ruoyi.system.entity.novel.NovelDayStatisticsEntity" id="NovelDayStatisticsResult">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="appId" column="app_id"/>
        <result property="newUserCount" column="new_user_count"/>
        <result property="watchUserCount" column="watch_user_count"/>
        <result property="watchChapterSum" column="watch_chapter_sum"/>
        <result property="adExposure" column="ad_exposure"/>
        <result property="adUnlockPv" column="ad_unlock_pv"/>
        <result property="adUnlockUv" column="ad_unlock_uv"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectNovelDayStatisticsVo">
        select id,
               cur_date,
               app_id,
               new_user_count,
               watch_user_count,
               watch_chapter_sum,
               ad_exposure,
               ad_unlock_pv,
               ad_unlock_uv,
               gmt_create,
               gmt_modified
        from tb_novel_day_statistics
    </sql>

    <select id="selectNovelDayStatisticsList" parameterType="com.ruoyi.system.req.novel.manage.NovelDayStatisticsReq"
            resultMap="NovelDayStatisticsResult">
        select
        <if test="dimension != null and dimension.size() > 0">
            <foreach collection="dimension" item="column" separator="," close=",">
                ${column}
            </foreach>
        </if>
        sum(new_user_count) as new_user_count, sum(watch_user_count) as watch_user_count, sum(watch_chapter_sum) as watch_chapter_sum,
        sum(ad_exposure) as ad_exposure, sum(ad_unlock_pv) as ad_unlock_pv, sum(ad_unlock_uv) as ad_unlock_uv
        from tb_novel_day_statistics
        <where>
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="appId != null ">and app_id = #{appId}</if>
        </where>
        <if test="dimension != null and dimension.size() > 0">
            GROUP BY
            <foreach collection="dimension" item="column" separator=",">
                ${column}
            </foreach>
        </if>
        <choose>
            <when test="dimension != null and dimension.size() > 0 and dimension.contains('cur_date')">
                order by cur_date desc, ${orderColumn} ${orderType}
            </when>
            <otherwise>
                order by ${orderColumn} ${orderType}
            </otherwise>
        </choose>
    </select>

    <select id="selectNovelDayStatisticsById" parameterType="Long" resultMap="NovelDayStatisticsResult">
        <include refid="selectNovelDayStatisticsVo"/>
        where id = #{id}
    </select>

    <select id="selectByDateAndAppId" resultMap="NovelDayStatisticsResult">
        <include refid="selectNovelDayStatisticsVo"/>
        where cur_date = #{today} and app_id = #{appId}
    </select>

    <insert id="insertNovelDayStatistics" parameterType="com.ruoyi.system.entity.novel.NovelDayStatisticsEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_novel_day_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="appId != null">app_id,</if>
            <if test="newUserCount != null">new_user_count,</if>
            <if test="watchUserCount != null">watch_user_count,</if>
            <if test="watchChapterSum != null">watch_chapter_sum,</if>
            <if test="adExposure != null">ad_exposure,</if>
            <if test="adUnlockPv != null">ad_unlock_pv,</if>
            <if test="adUnlockUv != null">ad_unlock_uv,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="appId != null">#{appId},</if>
            <if test="newUserCount != null">#{newUserCount},</if>
            <if test="watchUserCount != null">#{watchUserCount},</if>
            <if test="watchChapterSum != null">#{watchChapterSum},</if>
            <if test="adExposure != null">#{adExposure},</if>
            <if test="adUnlockPv != null">#{adUnlockPv},</if>
            <if test="adUnlockUv != null">#{adUnlockUv},</if>
        </trim>
    </insert>

    <update id="addNovelStat" parameterType="com.ruoyi.system.req.novel.web.NovelDayStatAddReq">
        update tb_novel_day_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="newUserCountAdd != null">new_user_count =new_user_count+ #{newUserCountAdd},</if>
            <if test="watchUserCountAdd != null">watch_user_count = watch_user_count+#{watchUserCountAdd},</if>
            <if test="watchChapterSumAdd != null">watch_chapter_sum =watch_chapter_sum+ #{watchChapterSumAdd},</if>
            <if test="adExposureAdd != null">ad_exposure =ad_exposure+ #{adExposureAdd},</if>
            <if test="adUnlockPvAdd != null">ad_unlock_pv = ad_unlock_pv+#{adUnlockPvAdd},</if>
            <if test="adUnlockUvAdd != null">ad_unlock_uv = ad_unlock_uv+#{adUnlockUvAdd},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateNovelDayStatistics" parameterType="com.ruoyi.system.entity.novel.NovelDayStatisticsEntity">
        update tb_novel_day_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="newUserCount != null">new_user_count = #{newUserCount},</if>
            <if test="watchUserCount != null">watch_user_count = #{watchUserCount},</if>
            <if test="watchChapterSum != null">watch_chapter_sum = #{watchChapterSum},</if>
            <if test="adExposure != null">ad_exposure = #{adExposure},</if>
            <if test="adUnlockPv != null">ad_unlock_pv = #{adUnlockPv},</if>
            <if test="adUnlockUv != null">ad_unlock_uv = #{adUnlockUv},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNovelDayStatisticsById" parameterType="Long">
        delete
        from tb_novel_day_statistics
        where id = #{id}
    </delete>

    <delete id="deleteNovelDayStatisticsByIds" parameterType="String">
        delete from tb_novel_day_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
