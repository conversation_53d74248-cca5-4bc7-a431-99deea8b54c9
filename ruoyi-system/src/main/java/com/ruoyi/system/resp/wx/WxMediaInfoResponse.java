package com.ruoyi.system.resp.wx;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 媒资信息返回结果
 * <AUTHOR>
 * @date 2023/7/14 17:04
 */
@Data
public class WxMediaInfoResponse implements Serializable {
    private static final long serialVersionUID = 8943880865667202011L;
    /**
     * 视频id
     */
    @SerializedName("media_id")
    private Long mediaId;

    /**
     * 审核信息
     */
    @SerializedName("audit_detail")
    private WxMediaAuditDetailResponse auditDetail;


    @NoArgsConstructor
    @Data
    public static class WxMediaAuditDetailResponse implements Serializable {
        private static final long serialVersionUID = 4800231037574085155L;

        /**
         * 审核状态
         * @see cn.com.nuohe.playlet.manager.common.enums.WxMediaAuditStatusEnum
         */
        @SerializedName("status")
        private Integer status;
        /**
         * 审核备注
         */
        @SerializedName("reason")
        private String reason;
        /**
         * 审核证据截图id列表
         */
        @SerializedName("evidence_material_id_list")
        private List<String> evidenceMaterialIdList;

    }


}
