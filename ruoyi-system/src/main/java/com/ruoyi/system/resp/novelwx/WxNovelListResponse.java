package com.ruoyi.system.resp.novelwx;

import cn.binarywang.wx.miniapp.bean.WxMaBaseResponse;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 微信小说列表结果
 * <AUTHOR>
 * @date 2023/7/14 16:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WxNovelListResponse extends WxMaBaseResponse implements Serializable {
    private static final long serialVersionUID = 6316722894253795138L;
    /**
     * 作品总数
     */
    @SerializedName("total_cnt")
    private Integer totalCnt;

    /**
     * last_id
     */
    @SerializedName("last_id")
    private Integer lastId;

    /**
     * 作品信息列表
     */
    @SerializedName("book_list")
    private List<WxBookResponse> bookList;

    @Data
    public class WxBookResponse implements Serializable{
        private static final long serialVersionUID = 1787023365656185648L;
        /**
         * 作品id
         */
        @SerializedName("book_id")
        private String bookId;
        /**
         * 作品名
         */
        private String title;
        /**
         * 作品简介
         */
        private String intro;
        /**
         * 封面图
         */
        @SerializedName("cover_url")
        private String coverImage;
        /**
         * 作者名
         */
        @SerializedName("author")
        private String author;
        /**
         * 一级类型id
         */
        @SerializedName("first_category_id")
        private Integer firstCategoryId;
        /**
         * 二级类型id
         */
        @SerializedName("second_category_id")
        private Integer secondCategory;
        /**
         * 三级类型id
         */
        @SerializedName("third_category_id")
        private Integer thirdCategory;
        /**
         * 完结状态 1连载 2完结
         */
        @SerializedName("complete_status")
        private Integer completeStatus;
        /**
         * 章节数量
         */
        @SerializedName("chapter_cnt")
        private Integer chapterNum;
        /**
         * 章节排序方式 1追加 2seq递增
         */
        @SerializedName("chapter_order_method")
        private Integer chapterOrderMethod;
    }
}
