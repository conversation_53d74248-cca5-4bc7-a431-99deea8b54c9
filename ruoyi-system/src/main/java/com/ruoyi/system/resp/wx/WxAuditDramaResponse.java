package com.ruoyi.system.resp.wx;

import cn.binarywang.wx.miniapp.bean.WxMaBaseResponse;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 微信提审结果
 * <AUTHOR>
 * @date 2023/7/14 16:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WxAuditDramaResponse extends WxMaBaseResponse implements Serializable {

    private static final long serialVersionUID = 2055498783942913541L;

    /**
     * 剧目id
     */
    @SerializedName("drama_id")
    private Long dramaId;
}
