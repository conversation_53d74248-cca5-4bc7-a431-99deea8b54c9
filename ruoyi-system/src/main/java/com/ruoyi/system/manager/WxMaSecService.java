package com.ruoyi.system.manager;

import com.ruoyi.system.req.playlet.wx.WxMaSecAuditDramaRequest;
import com.ruoyi.system.req.playlet.wx.WxMaSecPullUploadRequest;
import com.ruoyi.system.resp.wx.WxDramaInfoResponse;
import com.ruoyi.system.resp.wx.WxListMediaResponse;
import com.ruoyi.system.resp.wx.WxTaskResponse;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * 微信短剧媒资管理
 * <AUTHOR>
 * @date 2023/7/13 17:20
 */
public interface WxMaSecService {
    /**
     * 短剧拉取上传任务
     * @param request
     * @return
     * @throws WxErrorException
     */
    Long pullUpload(WxMaSecPullUploadRequest request) throws WxErrorException;

    /**
     * 查询任务状态
     */
    WxTaskResponse.WxTaskInfoResponse getTask(Long taskId) throws WxErrorException;

    /**
     * 查询媒资列表
     */
    WxListMediaResponse listMedia(Long dramaId) throws WxErrorException;

    String getMedia(Long mediaId) throws WxErrorException;
    /**
     * 剧目提审
     */
    Long auditDrama(WxMaSecAuditDramaRequest request) throws WxErrorException;

    /**
     * 获取剧目信息
     */
    WxDramaInfoResponse getDrama(Long dramaId) throws WxErrorException;

}
