package com.ruoyi.system.manager.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.ruoyi.system.manager.WxMaSecService;
import com.ruoyi.system.req.playlet.wx.WxMaSecAuditDramaRequest;
import com.ruoyi.system.req.playlet.wx.WxMaSecDramaInfoRequest;
import com.ruoyi.system.req.playlet.wx.WxMaSecGetTaskRequest;
import com.ruoyi.system.req.playlet.wx.WxMaSecListMediaRequest;
import com.ruoyi.system.req.playlet.wx.WxMaSecMediaInfoRequest;
import com.ruoyi.system.req.playlet.wx.WxMaSecPullUploadRequest;
import com.ruoyi.system.resp.wx.WxAuditDramaResponse;
import com.ruoyi.system.resp.wx.WxDramaInfoResponse;
import com.ruoyi.system.resp.wx.WxGetDramaInfoResponse;
import com.ruoyi.system.resp.wx.WxListMediaResponse;
import com.ruoyi.system.resp.wx.WxPullUploadResponse;
import com.ruoyi.system.resp.wx.WxTaskResponse;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.json.WxGsonBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 短剧媒资管理
 * <AUTHOR>
 * @date 2023/7/13 17:27
 */
@Service
public class WxMaSecServiceImpl implements WxMaSecService {
    //拉取上传url
    public static String PULL_UPLOAD_URL = "https://api.weixin.qq.com/wxa/sec/vod/pullupload";
    //查询任务信息
    public static String GET_TASK_URL = "https://api.weixin.qq.com/wxa/sec/vod/gettask";
    /**
     * 获取媒资列表
     */
    public static String LIST_MEDIA_URL = "https://api.weixin.qq.com/wxa/sec/vod/listmedia";
    /**
     * 剧目提审
     */
    public static String AUDIT_DRAMA_URL = "https://api.weixin.qq.com/wxa/sec/vod/auditdrama";
    /**
     * 剧目详情
     */
    public static String GET_DRAMA_URL = "https://api.weixin.qq.com/wxa/sec/vod/getdrama";
    @Autowired
    private WxMaService service;
    @Override
    public Long pullUpload(WxMaSecPullUploadRequest request) throws WxErrorException {
        String response = this.service.post(PULL_UPLOAD_URL, request);
        return WxGsonBuilder.create().fromJson(response, WxPullUploadResponse.class).getTaskId();
    }

    @Override
    public WxTaskResponse.WxTaskInfoResponse getTask(Long taskId) throws WxErrorException {
        WxMaSecGetTaskRequest request = new WxMaSecGetTaskRequest();
        request.setTaskId(taskId);
        String response = this.service.post(GET_TASK_URL, request);
        return WxGsonBuilder.create().fromJson(response, WxTaskResponse.class).getTaskInfo();
    }

    @Override
    public WxListMediaResponse listMedia(Long dramaId) throws WxErrorException {
        WxMaSecListMediaRequest request = new WxMaSecListMediaRequest();
        request.setDramaId(dramaId);
        String response = this.service.post(LIST_MEDIA_URL, request);
        return WxGsonBuilder.create().fromJson(response, WxListMediaResponse.class);
    }

    @Override
    public String getMedia(Long mediaId) throws WxErrorException {
        WxMaSecMediaInfoRequest request = new WxMaSecMediaInfoRequest();
        request.setMediaId(mediaId);
        String response = this.service.post("https://api.weixin.qq.com/wxa/sec/vod/getmedia", request);
        return response;
    }

    @Override
    public Long auditDrama(WxMaSecAuditDramaRequest request) throws WxErrorException {
        String response = this.service.post(AUDIT_DRAMA_URL, request);
        return WxGsonBuilder.create().fromJson(response, WxAuditDramaResponse.class).getDramaId();
    }

    @Override
    public WxDramaInfoResponse getDrama(Long dramaId) throws WxErrorException {
        WxMaSecDramaInfoRequest request = new WxMaSecDramaInfoRequest();
        request.setDramaId(dramaId);
        String response = this.service.post(GET_DRAMA_URL, request);
        return WxGsonBuilder.create().fromJson(response, WxGetDramaInfoResponse.class).getDramaInfo();
    }
}
