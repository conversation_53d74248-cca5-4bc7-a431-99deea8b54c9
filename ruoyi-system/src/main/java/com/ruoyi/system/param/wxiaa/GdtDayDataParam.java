package com.ruoyi.system.param.wxiaa;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 广点通iaa日数据参数对象
 *
 * <AUTHOR>
 * @date 2024/9/10 19:26
 */
@Data
public class GdtDayDataParam implements Serializable {
    private static final long serialVersionUID = -6234453497646196542L;

    /**
     * cur_date, advertisement_id, operator, miniapp_name, video_name
     */
    private List<String> dimension;

    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;

    /** 广告账户id */
    private String advertisementId;

//    /** 投手 */
//    private String operator;

    /** 小程序应用名称 */
    private List<String> miniappNames;

    /** 短剧名 */
    private String videoName;
    /**
     * 投手
     */
    private List<String> operators;

    /**
     * 组织ID
     */
    private Long accountId;

    /**
     * 组织ID列表
     */
    private List<Long> accountIds;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否升序
     */
    private Boolean isAsc;

    /**
     * 排序方式
     */
    private String orderType;
}
