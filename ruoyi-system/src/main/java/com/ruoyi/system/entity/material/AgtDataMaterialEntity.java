package com.ruoyi.system.entity.material;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import com.ruoyi.common.annotation.Excel;

/**
 * 巨量视频素材对象 tb_agt_data_material
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_agt_data_material")
public class AgtDataMaterialEntity implements Serializable{
    private static final long serialVersionUID = 1L;

            /** 主键 id */
    private Long id;

            /** 素材 id，视频素材 */
                @Excel(name = "素材 id，视频素材")
    private String materialId;

            /** 广告账户 id */
                @Excel(name = "广告账户 id")
    private String advertiserId;

            /** 广告账户名 */
                @Excel(name = "广告账户名")
    private String advertiserName;

            /** 分销平台 */
                @Excel(name = "分销平台")
    private String supplierNameShort;

                @Excel(name = "视频封面图片 url")
    private String imgUrl;

            /** 竖版视频、横版视频 */
                @Excel(name = "竖版视频、横版视频")
    private String imageMode;

            /** 其他抖小，微小面板 */
                @Excel(name = "其他抖小，微小面板")
    private String adOthers;

            /** 平台:1.抖小,2.微小 */
                @Excel(name = "平台:1.抖小,2.微小")
    private Long platformDyWx;

            /** 投手名，昵称简写 */
                @Excel(name = "投手名，昵称简写")
    private String userNickname;

            /** 真实姓名 */
                @Excel(name = "真实姓名")
    private String userRealname;

            /** 素材名称 */
                @Excel(name = "素材名称")
    private String adPlatformMaterialName;

            /** 剪辑师名称 */
                @Excel(name = "剪辑师名称")
    private String videoEditor;

            /** 短剧名称 */
                @Excel(name = "短剧名称")
    private String playletName;

            /** 240709，这种格式 */
                @Excel(name = "240709，这种格式")
    private String materialCreateTime;

            /** 原片 或者 二创 */
                @Excel(name = "原片 或者 二创")
    private String videoCreationType;

            /** 序号 */
                @Excel(name = "序号")
    private String serialNo;

            /**  */
                @JsonFormat(pattern = "yyyy-MM-dd")
                @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

            /**  */
                @JsonFormat(pattern = "yyyy-MM-dd")
                @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;


}
