package com.ruoyi.system.entity.coupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 短剧小程序看剧权益券对象 tb_playlet_coupon
 * 
 * <AUTHOR>
 * @date 2024-04-22
 */
@Data
@Accessors(chain = true)
public class PlayletCouponEntity implements Serializable{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 发货状态,1发货成功 也只会有1 */
    @Excel(name = "发货状态,1发货成功")
    private Long deliverStatus;

    /** 核销时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "核销时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date useTime;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiredTime;

    /** 核销类型，0未使用,1主动使用,2被动使用,3过期 */
    @Excel(name = "核销类型，0未使用")
    private Integer useStatus;

    /** orderId */
    @Excel(name = "orderId")
    private String orderId;

    /** 核销剧id */
    @Excel(name = "核销剧id")
    private String videoId;

    /** 核销剧名 */
    @Excel(name = "核销剧名")
    private String videoName;
    /** nickname */
    @Excel(name = "nickname")
    private String nickname;
    /** 用户id */
    @Excel(name = "用户id")
    private String userId;
    /** openId */
    @Excel(name = "openId")
    private String openId;
    /** 产品标识 */
    @Excel(name = "产品标识")
    private String productNo;
    /** 产品分类 */
    @Excel(name = "产品分类")
    private String useProductType;

    @Excel(name = "平台类型")
    private Integer platformType;

    @Excel(name = "应用名称")
    private String appName;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;

}