package com.ruoyi.system.entity.account;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

import com.ruoyi.common.annotation.Excel;

/**
 * 广点通主体对象 tb_gdt_account
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_gdt_account")
public class GdtAccountEntity implements Serializable {

    private static final long serialVersionUID = 8536797657163031067L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 组织id
     */
    @Excel(name = "组织id")
    private Long accountId;

    /**
     * 主体名
     */
    @Excel(name = "主体名")
    private String accountName;

    @Excel(name = "返点")
    private Double rebate;

    /**
     * 应用appid
     */
    @Excel(name = "应用appid")
    private Long appId;

    /**
     * 广点通token
     */
    @Excel(name = "广点通token")
    private String accessToken;

    /**
     * 刷新token
     */
    @Excel(name = "刷新token")
    private String refreshToken;

    /**
     * refresh token expries
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "refresh token expries", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refreshTokenExp;

    /**
     * access token expires
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "access token expires", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiresIn;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;
}
