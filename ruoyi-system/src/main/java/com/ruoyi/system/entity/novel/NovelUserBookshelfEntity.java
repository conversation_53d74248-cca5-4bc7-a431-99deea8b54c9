package com.ruoyi.system.entity.novel;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import com.ruoyi.common.annotation.Excel;
import lombok.experimental.Accessors;

/**
 * 小说用户书架对象 tb_novel_user_bookshelf
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_novel_user_bookshelf")
@Accessors(chain = true)
public class NovelUserBookshelfEntity implements Serializable{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 小说id */
    @Excel(name = "小说id")
    private Long novelId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;


}
