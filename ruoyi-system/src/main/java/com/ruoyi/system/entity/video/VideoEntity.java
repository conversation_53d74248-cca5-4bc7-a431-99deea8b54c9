package com.ruoyi.system.entity.video;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频表
 *
 * <AUTHOR>
 * @date 2023-7-4 14:47:48
 */
@Data
public class VideoEntity implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 视频组id
     */
    private Long videoGroupId;
    /**
     * wx视频id
     */
    private Long mediaId;

    /**
     * 视频标题
     */
    private String title;

    /**
     * 视频URL地址
     */
    private String videoUrl;

    /**
     * 价格
     */
    private Integer price;

    /**
     * 集数
     */
    private Integer episode;
    /**
     * 微信后台上传状态
     */
    private Integer wxUploadStatus;
    /**
     * 微信审核状态
     */
    private Integer wxAuditStatus;
    /**
     * 上传失败原因
     */
    private String uploadErrorMsg;
    /**
     * 审核失败原因
     */
    private String auditErrorMsg;


    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 视频时长
     */
    private Integer duration;

    /**
     * 腾讯音乐同步状态:0.未同步,1.同步中,2.同步成功,3.同步失败
     */
    private Integer tmeStatus;

    /**
     * 腾讯音乐同步失败原因
     */
    private String tmeReason;
}

