package com.ruoyi.system.entity.bingwu;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import com.ruoyi.common.annotation.Excel;

/**
 * 炳午快照对象 tb_agt_data_supplier_snapshot_bingwu
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_agt_data_supplier_snapshot_bingwu")
public class AgtDataSupplierSnapshotBingwuEntity implements Serializable{
    private static final long serialVersionUID = 1L;

            /** 主键 id */
    private Long id;

            /** 广告账户 id */
                @Excel(name = "广告账户 id")
    private String advertiserId;

            /**  */
                @JsonFormat(pattern = "yyyy-MM-dd")
                @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

            /**  */
                @Excel(name = "")
    private Long curHour;

            /** appid */
                @Excel(name = "appid")
    private String appId;

            /** 应用名称 */
                @Excel(name = "应用名称")
    private String appName;

            /**  */
                @Excel(name = "")
    private String subAppId;

            /** 子账号名称 */
                @Excel(name = "子账号名称")
    private String subAppName;

            /** 用户微信openid */
                @Excel(name = "用户微信openid")
    private String openId;

            /** 用户ID */
                @Excel(name = "用户ID")
    private String userCode;

            /** 推广ID */
                @Excel(name = "推广ID")
    private String promotionId;

            /** 例如：1805883690302467-炳午 */
                @Excel(name = "例如：1805883690302467-炳午")
    private String promotionName;

            /** 用户推广染色时间 */
                @JsonFormat(pattern = "yyyy-MM-dd")
                @Excel(name = "用户推广染色时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date promotionTime;

            /** 用户注册时间 */
                @JsonFormat(pattern = "yyyy-MM-dd")
                @Excel(name = "用户注册时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date registerTime;

            /** 订单ID */
                @Excel(name = "订单ID")
    private String orderNo;

            /**  */
                @Excel(name = "")
    private Long payAppType;

            /**  */
                @Excel(name = "")
    private String description;

            /** 支付状态（1：未支付，2：支付成功，4已退款） */
                @Excel(name = "支付状态", readConverterExp = "1=：未支付，2：支付成功，4已退款")
    private Long payStatus;

            /**  */
                @Excel(name = "")
    private Long state;

            /** 订单金额 */
                @Excel(name = "订单金额")
    private BigDecimal amount;

            /** 充值项ID */
                @Excel(name = "充值项ID")
    private Long sourceId;

            /**  */
                @Excel(name = "")
    private String source;

            /** 是否VIP */
                @Excel(name = "是否VIP")
    private Long isVip;

            /** 短剧ID */
                @Excel(name = "短剧ID")
    private Long dramaId;

            /** 剧集序号，第X集 */
                @Excel(name = "剧集序号，第X集")
    private Long epNo;

            /** 订单支付成功时间 */
                @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @Excel(name = "订单支付成功时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date successTime;

            /** 订单创建时间 */
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Excel(name = "订单创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
                private Date createTime;

            /** 短剧名称 */
                @Excel(name = "短剧名称")
    private String dramaName;

            /** ios, android */
                @Excel(name = "ios, android")
    private String system;

            /**  */
                @JsonFormat(pattern = "yyyy-MM-dd")
                @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

            /**  */
                @JsonFormat(pattern = "yyyy-MM-dd")
                @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gtmModified;


}
