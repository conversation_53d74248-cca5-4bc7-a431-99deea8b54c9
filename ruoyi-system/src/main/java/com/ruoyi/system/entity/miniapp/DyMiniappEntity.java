package com.ruoyi.system.entity.miniapp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 抖音小程序配置表
 *
 * <AUTHOR>
 * @date 2024-2-5 13:56:40
 */
@Data
public class DyMiniappEntity implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 小程序名称
     */
    private String title;

    /**
     * 抖音小程序appid
     */
    private String appId;

    /**
     * clientKey
     */
    private String clientKey;

    /**
     * clientSecret
     */
    private String clientSecret;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

}

