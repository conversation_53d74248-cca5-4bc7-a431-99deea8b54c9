package com.ruoyi.system.entity.juliangad;

/**
 * <AUTHOR>
 */
public class AgtAccountAdvertiserConstant {

    /**
     * 分割符号：武汉浩启光乐-jxf-美光-乘龙-微小小额4
     */
    public static String SPLIT = "-";
    public static int SIZE = 5;

    /**
     * 校验账户名称格式是否正确
     * 账户名称类似格式：武汉浩启光乐-jxf-美光-乘龙-微小小额4
     *
     * @param advertiserName 账户名称
     * @return 格式是否正确
     */
    public static boolean checkAdvertiserNameFormat(String advertiserName) {
        if (null == advertiserName) {
            return false;
        }
        String[] split = advertiserName.split("-");
        if (split.length < 5 || (!split[4].contains("抖") && !split[4].contains("微"))) {
            // 不合规的占大部分，所以不打印了，太多了。
//            log.error("广告主名称格式不正确,advertiserName:{}", advertiserName);
            return false;
        }
        return true;
    }

    /**
     * 账号名称命名规范解析入库
     * 账户名称类似格式：武汉硕展拓鑫信-yyp-友和-斩神1-微小小额
     *
     * @param entity
     * @param advertiserName
     */
    public static void parseAdvertiserName(AgtAccountJuliangAdvertiserEntity entity, String advertiserName) {
        if (null == advertiserName) {
            return;
        }
        String[] split = advertiserName.split(SPLIT);
        entity.setPlatformDyWx(0L);
        entity.setUserNickname("");
        entity.setSupplierNameShort("");
        entity.setPlayletName("");
        entity.setAdOthers("");

        // 1.长度需要5个横线，分割成6个词
        if (split.length < AgtAccountAdvertiserConstant.SIZE) {
            return;
        }

        // 2.需要包含抖音或微信
        String adOthers = split[4];
        long platformDyWx = 0L;
        if (adOthers.contains("抖")) {
            platformDyWx = 1L;
        }
        if (adOthers.contains("微")) {
            platformDyWx = 2L;
        }
        if (platformDyWx == 0) {
            return;
        }

        entity.setPlatformDyWx(platformDyWx);
        entity.setUserNickname(split[1]);
        // 校验是否录入库中
        String supplierNameShort = split[2];
        entity.setSupplierNameShort(supplierNameShort);
        entity.setPlayletName(split[3]);
        entity.setAdOthers(split[4]);
    }

}
