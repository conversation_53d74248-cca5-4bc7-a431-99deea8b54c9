package com.ruoyi.system.entity.novel;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import com.ruoyi.common.annotation.Excel;

/**
 * 小说渠道推广对象 tb_novel_channel_promotion
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_novel_channel_promotion")
public class NovelChannelPromotionEntity implements Serializable{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 推广链接 */
    @Excel(name = "推广链接")
    private String promotionUrl;

    /** 渠道号 */
    @Excel(name = "渠道号")
    private String channel;

    /** 小说id */
    @Excel(name = "小说id")
    private Long novelId;

    /** 微信小说id */
    private String wxBookId;

    /**
     * 抖音小说id
     */
    private String dyBookId;

    /** 章节序号 */
    @Excel(name = "章节序号")
    private Integer chapterSeq;

    /** 推广名称 */
    @Excel(name = "推广名称")
    private String channelName;

    /** 媒体渠道
     * @see com.ruoyi.system.constant.enums.NovelAppChannelTypeEnum
     * */
    @Excel(name = "媒体渠道")
    private String appChannel;

    /** 卡点章节 */
    private Integer lockChapterSeq;

    /** 推广类型,1小程序,2H5 */
    private Integer type;
    /**
     * 一次广告解锁的章节数
     */
    private Integer adUnlockSeq;

    /**
     * IAA回传配置
     */
    private String iaaConfig;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;
}
