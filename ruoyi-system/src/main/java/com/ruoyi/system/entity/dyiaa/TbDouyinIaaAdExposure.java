package com.ruoyi.system.entity.dyiaa;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 抖音IAA广告视频播放对象 tb_douyin_iaa_ad_exposure
 * 
 * <AUTHOR>
 * @date 2024-03-22
 */
public class TbDouyinIaaAdExposure extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private String id;

    /** 记录唯一标识 */
    @Excel(name = "记录唯一标识")
    private String keyId;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 用户OpenID */
    @Excel(name = "用户OpenID")
    private String openId;

    /** clickId */
    @Excel(name = "clickId")
    private String clickId;

    /** 广告视频播放时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "广告视频播放时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date eventTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setKeyId(String keyId) 
    {
        this.keyId = keyId;
    }

    public String getKeyId() 
    {
        return keyId;
    }
    public void setCurDate(Date curDate) 
    {
        this.curDate = curDate;
    }

    public Date getCurDate() 
    {
        return curDate;
    }
    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }
    public void setOpenId(String openId) 
    {
        this.openId = openId;
    }

    public String getOpenId() 
    {
        return openId;
    }
    public void setClickId(String clickId) 
    {
        this.clickId = clickId;
    }

    public String getClickId() 
    {
        return clickId;
    }
    public void setEventTime(Date eventTime) 
    {
        this.eventTime = eventTime;
    }

    public Date getEventTime() 
    {
        return eventTime;
    }
    public void setGmtCreate(Date gmtCreate) 
    {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtCreate() 
    {
        return gmtCreate;
    }
    public void setGmtModified(Date gmtModified) 
    {
        this.gmtModified = gmtModified;
    }

    public Date getGmtModified() 
    {
        return gmtModified;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("keyId", getKeyId())
            .append("curDate", getCurDate())
            .append("userId", getUserId())
            .append("openId", getOpenId())
            .append("clickId", getClickId())
            .append("eventTime", getEventTime())
            .append("gmtCreate", getGmtCreate())
            .append("gmtModified", getGmtModified())
            .toString();
    }
}
