package com.ruoyi.system.entity.douyin;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 抖音优惠券领取核销对象 tb_douyin_coupon
 * 
 * <AUTHOR>
 * @date 2024-04-12
 */
public class DouyinCoupon extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private String id;

    /** 平台券id */
    @Excel(name = "平台券id")
    private String couponId;

    /** 应用appid */
    @Excel(name = "应用appid")
    private String appId;

    /** openid */
    @Excel(name = "openid")
    private String openId;

    /** 券状态,1待使用,2已使用,3已失效 */
    @Excel(name = "券状态,1待使用,2已使用,3已失效")
    private Integer couponStatus;

    /** 领券时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "领券时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiveTime;

    /** 外部券模版编号 */
    @Excel(name = "外部券模版编号")
    private String merchantMetaNo;

    /** 券有效期开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "券有效期开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validBeginTime;

    /** 券有效期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "券有效期结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validEndTime;

    /** 发券人的小程序openid */
    @Excel(name = "发券人的小程序openid")
    private String talentOpenId;

    /** 发券人抖音号 */
    @Excel(name = "发券人抖音号")
    private String talentAccount;

    /** 领券用户unionid */
    @Excel(name = "领券用户unionid")
    private String unionId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setCouponId(String couponId) 
    {
        this.couponId = couponId;
    }

    public String getCouponId() 
    {
        return couponId;
    }
    public void setAppId(String appId) 
    {
        this.appId = appId;
    }

    public String getAppId() 
    {
        return appId;
    }
    public void setOpenId(String openId) 
    {
        this.openId = openId;
    }

    public String getOpenId() 
    {
        return openId;
    }
    public void setCouponStatus(Integer couponStatus) 
    {
        this.couponStatus = couponStatus;
    }

    public Integer getCouponStatus() 
    {
        return couponStatus;
    }
    public void setReceiveTime(Date receiveTime) 
    {
        this.receiveTime = receiveTime;
    }

    public Date getReceiveTime() 
    {
        return receiveTime;
    }
    public void setMerchantMetaNo(String merchantMetaNo) 
    {
        this.merchantMetaNo = merchantMetaNo;
    }

    public String getMerchantMetaNo() 
    {
        return merchantMetaNo;
    }
    public void setValidBeginTime(Date validBeginTime) 
    {
        this.validBeginTime = validBeginTime;
    }

    public Date getValidBeginTime() 
    {
        return validBeginTime;
    }
    public void setValidEndTime(Date validEndTime) 
    {
        this.validEndTime = validEndTime;
    }

    public Date getValidEndTime() 
    {
        return validEndTime;
    }
    public void setTalentOpenId(String talentOpenId) 
    {
        this.talentOpenId = talentOpenId;
    }

    public String getTalentOpenId() 
    {
        return talentOpenId;
    }
    public void setTalentAccount(String talentAccount) 
    {
        this.talentAccount = talentAccount;
    }

    public String getTalentAccount() 
    {
        return talentAccount;
    }
    public void setUnionId(String unionId) 
    {
        this.unionId = unionId;
    }

    public String getUnionId() 
    {
        return unionId;
    }
    public void setGmtCreate(Date gmtCreate) 
    {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtCreate() 
    {
        return gmtCreate;
    }
    public void setGmtModified(Date gmtModified) 
    {
        this.gmtModified = gmtModified;
    }

    public Date getGmtModified() 
    {
        return gmtModified;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("couponId", getCouponId())
            .append("appId", getAppId())
            .append("openId", getOpenId())
            .append("couponStatus", getCouponStatus())
            .append("receiveTime", getReceiveTime())
            .append("merchantMetaNo", getMerchantMetaNo())
            .append("validBeginTime", getValidBeginTime())
            .append("validEndTime", getValidEndTime())
            .append("talentOpenId", getTalentOpenId())
            .append("talentAccount", getTalentAccount())
            .append("unionId", getUnionId())
            .append("gmtCreate", getGmtCreate())
            .append("gmtModified", getGmtModified())
            .toString();
    }
}
