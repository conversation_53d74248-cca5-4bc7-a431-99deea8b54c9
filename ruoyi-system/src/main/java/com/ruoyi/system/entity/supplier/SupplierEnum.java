package com.ruoyi.system.entity.supplier;

public enum SupplierEnum {
    YOUHE("友和"),
    LIANFAN("连番"),
    CHUMO("触摸"),
    BINGWU("炳午"),
    MEIGUANG("美光"),
    FANQIE("番茄"),
    DIANZHONG("点众"),
    NUOHE("诺禾"),
    LIUYI("六翼");
    private String name;

    SupplierEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static SupplierEnum getSupplierEnum(String name) {
        for (SupplierEnum supplierEnum : SupplierEnum.values()) {
            if (supplierEnum.getName().equals(name)) {
                return supplierEnum;
            }
        }
        return null;
    }
}
