package com.ruoyi.system.entity.video;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频剧集表
 *
 * <AUTHOR>
 * @date 2023-7-4 14:45:34
 */
@Data
public class VideoGroupEntity implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 微信剧目id
     */
    private Long dramaId;

    /**
     * 视频标题
     */
    private String title;

    /**
     * ssp客户id
     */
    private Long sspAccountId;

    /**
     * 封面
     */
    private String img;

    /**
     * gif封面
     */
    private String gifImg;
    /**
     * 横屏封面
     */
    private String landscapeImg;

    /**
     * 视频状态，0下架,1上架
     */
    private Integer status;

    /**
     * 分类id
     */
    private Long classifyId;

    /**
     * 是否热门
     */
    private Integer ishot;

    /**
     * 点赞
     */
    private Integer give;

    /**
     * 转发
     */
    private Integer forward;

    /**
     * 浏览量
     */
    private Integer views;

    /**
     * 共多少集
     */
    private Integer episodeSum;
    /**
     * 更新集数
     */
    private Integer updateEpisode;

    /**
     * 免费集数
     */
    private Integer startChargeEpisode;

    /**
     * 剧情介绍
     */
    private String story;

    /**
     * 每集价格
     */
    private Integer price;
    /**
     * 制作人
     */
    private String producer;
    /**
     * 授权材料图片地址
     */
    private String material;
    /**
     * 剧目备案号
     */
    private String registrationNumber;
    /**
     * 网络剧片发行许可证编号
     */
    private String publishLicense;

    /**
     * 网络剧片发行许可证图片
     */
    private String publishLicenseImg;

    /**
     * 微信审核状态
     * @see cn.com.nuohe.playlet.manager.common.enums.WxAuditStatusEnum
     */
    private Integer wxAuditStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 审核失败原因
     */
    private String auditErrorMsg;

    /**
     * 内容平台 ,位运算
     * @see cn.com.nuohe.playlet.manager.common.enums.PlayletPlatformEnum
     */
    private Integer platform;

    /**
     * 演员信息 jsonarray
     */
    private String actorList;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 推荐语
     */
    private String recommend;
    /**
     * 平均单集时长，分钟
     */
    private Integer duration;

    /**
     * 导演列表
     */
    private String directors;

    /**
     * 制作人列表
     */
    private String producers;
    /**
     * 制作成本
     */
    private Integer productionCost;

    /**
     * 抖音成本比例配置图
     */
    private String costDistributionUri;
    /**
     * 微信成本比例配置图
     */
    private String costCommitmentLetterMaterialUri;

    /**
     * 上映时间
     */
    private Date publishTime;

    /**
     * 标签
     */
    private String tags;

    /**
     * 腾讯音乐分类
     */
    private Integer tmeCategory;

    /**
     * 腾讯音乐同步状态:0.未同步,1.同步中,2.同步成功,3.同步失败
     */
    private Integer tmeStatus;

    /**
     * 腾讯音乐同步失败原因
     */
    private String tmeReason;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}
