package com.ruoyi.system.entity.aiaa;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 行政区划代码对象 tb_aiaa_area
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiaaAreaEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 行政区划代码
     */
    private Integer areaNum;

    /**
     * 类型:1.省,2.市,3.区/县
     */
    private Integer areaType;

    /**
     * 名称
     */
    private String areaName;

    /**
     * 上级行政区划代码
     */
    private Integer parentNum;

    /**
     * 是否可见
     */
    private Boolean visible;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;



}
