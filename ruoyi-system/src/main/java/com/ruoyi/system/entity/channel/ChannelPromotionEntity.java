package com.ruoyi.system.entity.channel;

import com.ruoyi.common.enums.ChannelPromotionTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道推广表
 *
 * <AUTHOR>
 * @date 2023-7-6 16:21:02
 */
@Data
public class ChannelPromotionEntity implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 推广链接
     */
    private String promotionUrl;

    /**
     * 渠道号
     */
    private String channel;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 视频短剧id
     */
    private Long videoGroupId;

    /**
     * 视频id
     */
    private Long videoId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 推广类型
     * @see ChannelPromotionTypeEnum
     */
    private Integer type;

    /**
     * 描述
     */
    private String description;

    /**
     * 抽奖链接
     */
    private String lotteryUrl;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

