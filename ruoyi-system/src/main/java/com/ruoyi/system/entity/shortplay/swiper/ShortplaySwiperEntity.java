package com.ruoyi.system.entity.shortplay.swiper;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

import com.ruoyi.common.annotation.Excel;

/**
 * 短剧轮播图对象 tb_shortplay_swiper
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_shortplay_swiper")
public class ShortplaySwiperEntity implements Serializable {
    private static final long serialVersionUID = -2641455311051841705L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 源表主键id
     */
    private String tbId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 源数据
     */
    @Excel(name = "源数据")
    private String dataJson;

    /**
     * 图片URL
     */
    private String image;

    /**
     * 标题
     */
    private String title;

    /**
     * 电视剧ID
     */
    private String tvId;

    /**
     * 电视剧名称
     */
    private String tvName;

    /**
     * 电视剧图片URL
     */
    private String tvImage;

    /**
     * 应用ID列表
     */
    private String appid;

    /**
     * 应用名称列表
     */
    private String appname;

    /**
     * 添加时间戳
     */
    private Long AddTime;

    /**
     * 添加时间字符串
     */
    private String AddTimeStr;

    /**
     * 排序
     */
    private Integer sort;
}
