package com.ruoyi.system.entity.shortplay.tf;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

import com.ruoyi.common.annotation.Excel;

/**
 * 短剧投放设置对象 tb_shortplay_dy_tfurl
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_shortplay_dy_tfurl")
public class ShortplayDyTfurlEntity implements Serializable {
    private static final long serialVersionUID = -3502025381316719024L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 源表主键id
     */
    @Excel(name = "源表主键id")
    private String tbId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 源数据
     */
    @Excel(name = "源数据")
    private String dataJson;

    /**
     * 首次充值信息JSON数组
     */
    @Excel(name = "首次充值信息JSON数组")
    private String bindFirstRecharge;

    /**
     * 首次充值ID数组
     */
    @Excel(name = "首次充值ID数组")
    private String bindFirstRechargeId;

    /**
     * 再次充值信息JSON数组
     */
    @Excel(name = "再次充值信息JSON数组")
    private String bindAgainRecharge;

    /**
     * 再次充值ID数组
     */
    @Excel(name = "再次充值ID数组")
    private String bindAgainRechargeId;

    /**
     * 广告ID
     */
    @Excel(name = "广告ID")
    private String adId;

    /**
     * 描述
     */
    @Excel(name = "描述")
    private String desc;

    /**
     * 推广链接
     */
    @Excel(name = "推广链接")
    private String tfurl;

    /**
     * 代理ID
     */
    @Excel(name = "代理ID")
    private String agentId;

    /**
     * 代理用户名
     */
    @Excel(name = "代理用户名")
    private String agentUsername;

    /**
     * 代理昵称
     */
    @Excel(name = "代理昵称")
    private String agentNickname;

    /**
     * 中介ID
     */
    @Excel(name = "中介ID")
    private String middlemanId;

    /**
     * 中介用户名
     */
    @Excel(name = "中介用户名")
    private String middlemanUsername;

    /**
     * 中介昵称
     */
    @Excel(name = "中介昵称")
    private String middlemanNickname;

    /**
     * 应用ID
     */
    @Excel(name = "应用ID")
    private String appid;

    /**
     * 应用名称
     */
    @Excel(name = "应用名称")
    private String appname;

    /**
     * 应用密钥
     */
    @Excel(name = "应用密钥")
    private String appsecret;

    /**
     * 电视剧ID
     */
    @Excel(name = "电视剧ID")
    private String tvId;

    /**
     * 电视剧名称
     */
    @Excel(name = "电视剧名称")
    private String tvName;

    /**
     * 系列
     */
    @Excel(name = "系列")
    private Long series;

    /**
     * 关注
     */
    @Excel(name = "关注")
    private Long follow;

    /**
     * 公众号ID
     */
    @Excel(name = "公众号ID")
    private String gzhId;

    /**
     * 路径
     */
    @Excel(name = "路径")
    private String path;

    /**
     * 平台
     */
    @Excel(name = "平台")
    private String platform;

    /**
     * 添加时间戳
     */
    @Excel(name = "添加时间戳")
    private Long AddTime;

    /**
     * 添加时间字符串
     */
    @Excel(name = "添加时间字符串")
    private String AddTimeStr;

    /**
     * 链接
     */
    @Excel(name = "链接")
    private String urlLink;

    /**
     * 链接过期时间戳
     */
    @Excel(name = "链接过期时间戳")
    private Long urlLinkExpireTime;

    /**
     * 小程序路径
     */
    @Excel(name = "小程序路径")
    private String xcxpath;

    /**
     * 是否已计算
     */
    @Excel(name = "是否已计算")
    private Long isComputed;

    private Integer openWxBannerAdvert;

    private Integer openWxInterstitialAdvert;

    private Integer openWxVideoAdvert;

    /**
     * 支付金额(元)
     */
    private BigDecimal payAmount;
}
