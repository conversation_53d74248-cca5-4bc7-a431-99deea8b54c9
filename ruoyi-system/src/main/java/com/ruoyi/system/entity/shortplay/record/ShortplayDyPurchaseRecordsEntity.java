package com.ruoyi.system.entity.shortplay.record;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

import com.ruoyi.common.annotation.Excel;

/**
 * 短剧购买记录对象 tb_shortplay_dy_purchase_records
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_shortplay_dy_purchase_records")
public class ShortplayDyPurchaseRecordsEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 源表主键id
     */
    private String tbId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 添加时间戳
     */
    @Excel(name = "添加时间戳")
    private Long AddTime;

    /**
     * 添加时间年月日时分秒
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "添加时间年月日时分秒", width = 30, dateFormat = "yyyy-MM-dd")
    private Date AddTimeStr;

    /**
     * 小程序appid
     */
    @Excel(name = "小程序appid")
    private String appid;

    /**
     * 小程序名称
     */
    @Excel(name = "小程序名称")
    private String appname;

    /**
     * 代理商id
     */
    @Excel(name = "代理商id")
    private String inviteCode;

    /**
     * 机构id
     */
    @Excel(name = "机构id")
    private String middlemanId;

    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称")
    private String nickname;

    /**
     * 平台，ios | android
     */
    @Excel(name = "平台，ios | android")
    private String osName;

    /**
     * 支付金额数
     */
    @Excel(name = "支付金额数")
    private BigDecimal payAmount;

    /**
     * 第x集
     */
    @Excel(name = "第x集")
    private Integer series;

    /**
     * 剧集id
     */
    @Excel(name = "剧集id")
    private String seriesId;

    /**
     * 投放链接id
     */
    @Excel(name = "投放链接id")
    private String tfid;

    /**
     * 剧id
     */
    @Excel(name = "剧id")
    private String tvId;

    /**
     * 剧名称
     */
    @Excel(name = "剧名称")
    private String tvName;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private String userId;

    /**
     * 抖音openId
     */
    @Excel(name = "抖音openId")
    private String ttOpenid;

}
