package com.ruoyi.system.entity.shortplay.record;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

import com.ruoyi.common.annotation.Excel;

/**
 * 短剧小程序播放记录对象 tb_shortplay_watch_record
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_shortplay_watch_record")
public class ShortplayWatchRecordEntity implements Serializable {
    private static final long serialVersionUID = -275799193354587742L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 短剧ID
     */
    private Long tvId;

    /**
     * 第几集
     */
    private Integer series;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * appId
     */
    private String appId;
}
