package com.ruoyi.system.entity.rank;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

import com.ruoyi.common.annotation.Excel;

/**
 * 短剧热力榜日榜对象 tb_playlet_consume_rank
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_playlet_consume_rank")
public class PlayletConsumeRankEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 剧名
     */
    @Excel(name = "剧名")
    private String playletName;

    /**
     * 标签
     */
    @Excel(name = "标签")
    private String tag;

    /**
     * 关联方
     */
    @Excel(name = "关联方")
    private String relatedParty;

    /**
     * 承制方
     */
    @Excel(name = "承制方")
    private String contractor;

    /**
     * 标识
     */
    private String hint;

    /**
     * 素材数
     */
    @Excel(name = "素材数")
    private Integer materialCount;

    /**
     * 热力值
     */
    @Excel(name = "热力值")
    private Integer consumeNum;

    /**
     * 计划数
     */
    @Excel(name = "计划数")
    private Integer promotionCount;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;
}
