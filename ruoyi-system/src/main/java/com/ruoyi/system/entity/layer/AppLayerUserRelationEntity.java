package com.ruoyi.system.entity.layer;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * app弹窗用户关联表
 *
 * <AUTHOR>
 * @date 2023-10-23 14:08:25
 */
@Data
public class AppLayerUserRelationEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 弹窗配置id
     */
    private Long layerConfigId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 已经展示次数
     */
    private Integer hasShowCount;

    /**
     * 删除状态
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

}

