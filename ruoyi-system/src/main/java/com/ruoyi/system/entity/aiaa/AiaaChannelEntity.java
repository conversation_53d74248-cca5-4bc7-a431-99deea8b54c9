package com.ruoyi.system.entity.aiaa;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @date 2023年7月11日 下午5:44:10
 */
@Data
public class AiaaChannelEntity implements Serializable {

    /**
     * 渠道id
     */
    private Long id;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 应用id穿山甲
     */
    private String appId;

    /**
     * apk版本
     */
    private String apkVersion;

    /**
     * apk下载
     */
    private String apkDownloadUrl;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 是否删除0否，1是
     */
    private Integer isDeleted;

}

