package com.ruoyi.system.entity.shortplay.app;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * app应用配置对象 tb_shortplay_app
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("tb_shortplay_app")
public class ShortplayAppEntity implements Serializable {
    private static final long serialVersionUID = -8816249508133256533L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 源表主键id
     */
    private String tbId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 源数据
     */
    private String dataJson;

    /**
     * 是否开启搜索
     */
    private Integer opensearch;

    /**
     * 充值说明
     */
    private String rechargeInstructions;

    /**
     * 微信广告ID
     */
    private String wxAdAdid;

    /**
     * 微信广告记录数
     */
    private Integer wxAdRecordNum;

    /**
     * 抖音广告ID
     */
    private String ttAdAdid;

    /**
     * 抖音广告记录数
     */
    private String ttAdRecordNum;

    /**
     * 抖音广告开关
     */
    private Integer ttAdSwitch;

    /**
     * 抖音IM ID
     */
    private String ttImId;

    /**
     * 是否启用支付
     */
    private Integer isEnabledPay;

    /**
     * 原始ID
     */
    private String originid;

    /**
     * 二维码URL
     */
    private String qrurl;

    /**
     * 豆币数量
     */
    private Long beannumber;

    /**
     * 应用名称
     */
    private String appname;

    /**
     * 应用密钥
     */
    private String appsecret;

    /**
     * 首页提示开关
     */
    private Integer indexTipsSwitch;

    /**
     * 首页提示文本
     */
    private String indexTipsText;

    /**
     * 企业微信应用ID
     */
    private String workwxappid;

    /**
     * 企业微信URL
     */
    private String workwxurl;

    /**
     * 加密字符串
     */
    private String xspsx;

    /**
     * 添加时间戳
     */
    private Long AddTime;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * iOS支付
     */
    private Integer iospay;

    /**
     * 开启首页弹窗广告
     */
    private Long openIndexTTAdvertModal;

    /**
     * 支付金额
     */
    private BigDecimal payMoney;

    /**
     * 产品ID
     */
    private String pid;

    /**
     * 接收时间戳
     */
    private Long receiveTime;

    /**
     * 提示文本
     */
    private String tipsText;

    /**
     * 添加时间字符串
     */
    private String AddTimeStr;

    /**
     * 微信广告ID配置
     */
    private String wxAdvertIdConfig;

    /**
     * 加密字符串
     */
    private String flhd;

    /**
     * 加密字符串
     */
    private String jjgx;

    /**
     * 推广邮箱
     */
    private String tgemail;

    /**
     * 是否两行显示
     */
    private Integer twoInRow;

    /**
     * 应用平台
     */
    private String appplatform;

    /**
     * 微信广告开关
     */
    private Integer wxAdSwitch;

    /**
     * 关注公众号提示
     */
    private String gzgzhTip;

    /**
     * 公众号提示开关
     */
    private Integer gzhTip;

    /**
     * 公众号提示文本
     */
    private String gzhTipText;

    /**
     * 免费豆币数量
     */
    private Long freeBean;

    /**
     * 签到奖励
     */
    private Long signinReward;

    /**
     * 静态邀请码
     */
    private Integer staticinviteCode;

    /**
     * 提示开关
     */
    private Integer tipsSwitch;

    /**
     * 首页电话弹窗
     */
    private Long indexPhoneModal;

    /**
     * 返回拦截激励图
     */
    private String interceptModalBg;

    /**
     * 返回拦截开奖图
     */
    private String interceptModalRecord;

    /**
     * 支付宝广告ID
     */
    private String aliAdAdid;

    /**
     * 支付宝单次奖励集数
     */
    private Integer aliAdRecordNum;

    /**
     * 支付宝激励广告开关
     */
    private Integer aliAdSwitch;

    /**
     * 抖音第三方小程序appId
     */
    private String componentAppid;

    /**
     * 抖音第三方小程序名称
     */
    private String componentAppname;
}
