package com.ruoyi.system.entity.douyin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 巨量广告每日数据对象 tb_jl_date_data
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
@Data
public class TbJlDateData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private String id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /** 账户id */
    @Excel(name = "账户id")
    private String advertiserId;

    /** 广告id */
    @Excel(name = "广告id")
    private String adId;

    /**
     * 广告名称
     */
    private String adName;

    /** 消耗 */
    @Excel(name = "消耗")
    private Integer statCost;

    /** 转化数 */
    @Excel(name = "转化数")
    private Integer convertCnt;

    /** 转化成本(分) */
    @Excel(name = "转化成本(分)")
    private Integer convertCost;

    /** 展示数 */
    @Excel(name = "展示数")
    private Integer showCnt;

    /** 点击数 */
    @Excel(name = "点击数")
    private Integer clickCnt;

    /** 点击率 */
    @Excel(name = "点击率")
    private Double ctr;

    /**
     * 广告播放次数
     */
    @Excel(name = "广告播放次数")
    private Integer adWatchPv;
    /**
     * 广告播放人数
     */
    @Excel(name = "广告播放人数")
    private Integer adWatchUv;
    /**
     * 首日广告收入分
     */
    @Excel(name = "首日广告收入")
    private Long firstDayAdIncome;
    /**
     * 今日广告收入分
     */
    @Excel(name = "今日广告收入")
    private Long todayAdIncome;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;
}
