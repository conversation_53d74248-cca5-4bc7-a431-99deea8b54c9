package com.ruoyi.system.entity.hotcake;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 爆款小程序小程序对象 tb_hotcake_miniapp
 *
 * <AUTHOR>
 */
@Data
public class HotcakeMiniappEntity implements Serializable {
    private static final long serialVersionUID = 1352185439489480933L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 小程序appSecret
     */
    private String appSecret;

    /**
     * 小程序名称
     */
    private String appName;

    /**
     * 基础配置
     */
    private String basicConfig;

    /**
     * 广告位配置
     */
    private String slotConfig;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
