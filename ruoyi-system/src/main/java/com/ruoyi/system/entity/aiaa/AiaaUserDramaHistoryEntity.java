package com.ruoyi.system.entity.aiaa;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户查看短剧历史
 *
 * <AUTHOR>
 * @date 2023年7月11日 下午5:45:32
 */
@Data
public class AiaaUserDramaHistoryEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 短剧id
     */
    private Long dramaId;

    /**
     * 短剧名
     */
    private String dramaName;

    /**
     * 短剧封面
     */
    private String coverImage;

    /**
     * 短剧总集数
     */
    private Long dramaTotal;

    /**
     * 播放集数
     */
    private Long dramaCurrent;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 是否删除0否，1是
     */
    private Integer isDeleted;

}

