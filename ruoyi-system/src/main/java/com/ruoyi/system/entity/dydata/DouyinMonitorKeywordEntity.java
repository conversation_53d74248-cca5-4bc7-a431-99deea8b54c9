package com.ruoyi.system.entity.dydata;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 抖音监测关键词对象 tb_douyin_monitor_keyword
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
public class DouyinMonitorKeywordEntity implements Serializable {
    private static final long serialVersionUID = 5651583231894711988L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 类型:1.新剧,2.老剧
     */
    private Integer type;

    /**
     * 开始监测日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startMonitorDate;

    /**
     * 结束监测日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endMonitorDate;

    /**
     * 监测状态:0.关闭,1.开启
     */
    private Integer monitorStatus;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}
