package com.ruoyi.system.entity.novel;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import com.ruoyi.common.annotation.Excel;

/**
 * 小说小程序app对象 tb_novel_miniapp
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_novel_miniapp")
public class NovelMiniappEntity implements Serializable{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private String id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appId;

    /** 应用秘钥 */
    @Excel(name = "应用秘钥")
    private String appSecret;
    private String appName;
    /**
     * 广告配置
     */
    private String adConfig;

    private String appConfig;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;


}
