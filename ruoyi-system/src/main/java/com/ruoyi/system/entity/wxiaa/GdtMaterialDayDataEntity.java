package com.ruoyi.system.entity.wxiaa;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

import com.ruoyi.common.annotation.Excel;

/**
 * 广点通IAA素材日数据对象 tb_gdt_material_day_data
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_gdt_material_day_data")
public class GdtMaterialDayDataEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 当前日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "当前日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告账户ID
     */
    private Long advertisementId;

    /**
     * 素材ID(图片ID/视频ID)
     */
    @Excel(name = "素材ID(图片ID/视频ID)")
    private String materialId;

    /**
     * 素材类型:1.图片,2.视频
     */
    @Excel(name = "素材类型:1.图片,2.视频")
    private Integer materialType;

    /**
     * 素材描述
     */
    @Excel(name = "素材描述")
    private String materialDesc;

    /**
     * 剪辑昵称
     */
    @Excel(name = "剪辑昵称")
    private String materialEditor;

    /**
     * 曝光次数
     */
    @Excel(name = "曝光次数")
    private Integer viewCount;

    /**
     * 点击次数
     */
    @Excel(name = "点击次数")
    private Integer validClickCount;

    /**
     * 目标转化量
     */
    @Excel(name = "目标转化量")
    private Integer conversionsCount;

    /**
     * 广告变现人数
     */
    @Excel(name = "广告变现人数")
    private Integer appAdPayingUsers;

    /**
     * 广告变现金额
     */
    @Excel(name = "广告变现金额")
    private Integer adMonetizationAmount;

    /**
     * 花费
     */
    @Excel(name = "花费")
    private Integer cost;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 组织ID(仅用于查询)
     */
    private Long accountId;
}
