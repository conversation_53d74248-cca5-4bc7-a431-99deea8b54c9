package com.ruoyi.system.publisher.eventTrack;

import com.ruoyi.common.enums.eventTrack.TrackEventTypeEnum;
import com.ruoyi.system.bo.eventTrack.TrackDataBO;
import com.ruoyi.system.event.TrackEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一的事件发布工具，提供方便的方法来发布跟踪事件
 */
@Component
public class TrackEventPusher {

    @Autowired
    private ApplicationEventPublisher eventPublisher;
    /**
     * 发布广点通版位数据用户统计事件
     *
     */
    public void pushGdtSiteSetUserStatEvent(String userId, Integer appType) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("userId", userId);
        properties.put("appType", appType);
        pushEvent(TrackEventTypeEnum.GDTSTAT_CALLBACK, properties);
    }

    /**
     * 发布事件
     *
     * @param eventType 事件类型
     * @param properties 事件属性
     */
    public void pushEvent(TrackEventTypeEnum eventType, Map<String, Object> properties) {
        TrackDataBO trackDataBO = new TrackDataBO();
        trackDataBO.setEvent(eventType.name());
        trackDataBO.setTimestamp(System.currentTimeMillis());
        trackDataBO.setProperties(properties);
        
        eventPublisher.publishEvent(new TrackEvent(trackDataBO));
    }
} 