package com.ruoyi.system.config;

import com.alibaba.fastjson.JSON;
import com.ruoyi.system.bo.common.RefreshCacheMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.constant.Constants.REDIS_SUBSCRIBE_CHANNEL;


/**
 * redis 发布者
 * <AUTHOR>
 * @date 2025/1/8 15:08
 */
@Service
public class RedisPublisher {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public void publish(String channel, String message) {
        redisTemplate.convertAndSend(channel, message);
    }

    public void refreshCachePublish(RefreshCacheMessage message) {
        redisTemplate.convertAndSend(REDIS_SUBSCRIBE_CHANNEL, JSON.toJSONString(message));
    }
}