package com.ruoyi.system.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

import static com.ruoyi.common.constant.Constants.REDIS_SUBSCRIBE_CHANNEL;

/**
 * redis 订阅者配置
 * <AUTHOR>
 * @date 2025/1/8 15:13
 */
@Configuration
public class RedisSubscriberConfig {
    @Autowired
    private RefreshCacheRedisSubscriber refreshCacheRedisSubscriber;

    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        container.addMessageListener(messageListenerAdapter(), new PatternTopic(REDIS_SUBSCRIBE_CHANNEL));

        return container;
    }

    @Bean
    public MessageListenerAdapter messageListenerAdapter() {
        return new MessageListenerAdapter(refreshCacheRedisSubscriber);
    }
}
