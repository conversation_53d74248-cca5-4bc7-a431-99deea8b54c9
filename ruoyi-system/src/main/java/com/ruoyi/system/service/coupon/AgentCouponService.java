package com.ruoyi.system.service.coupon;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.entity.coupon.AgentCouponEntity;

/**
 * 短剧代理商权益Service接口
 *
 * <AUTHOR>
 */
public interface AgentCouponService {

    /**
     * 查询短剧代理商权益
     *
     * @param id 短剧代理商权益主键
     * @return 短剧代理商权益
     */
    AgentCouponEntity selectAgentCouponById(Long id);

    /**
     * 查询短剧代理商权益
     *
     * @param couponCode 权益编号
     * @return 短剧代理商权益
     */
    AgentCouponEntity selectByCouponCode(String couponCode);

    /**
     * 查询短剧代理商权益
     *
     * @param couponCodes 权益编号列表
     * @return 短剧代理商权益
     */
    Map<String, AgentCouponEntity> selectMapByCouponCode(List<String> couponCodes);

    /**
     * 查询短剧代理商权益列表
     *
     * @param param 短剧代理商权益
     * @return 短剧代理商权益集合
     */
    List<AgentCouponEntity> selectList(AgentCouponEntity param);

    /**
     * 新增短剧代理商权益
     *
     * @param coupon 短剧代理商权益
     * @return 结果
     */
    int insert(AgentCouponEntity coupon);

    /**
     * 修改短剧代理商权益
     *
     * @param coupon 短剧代理商权益
     * @return 结果
     */
    int update(AgentCouponEntity coupon);
}
