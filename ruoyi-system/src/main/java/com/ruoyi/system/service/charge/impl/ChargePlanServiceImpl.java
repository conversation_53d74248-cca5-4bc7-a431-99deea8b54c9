package com.ruoyi.system.service.charge.impl;

import com.ruoyi.system.entity.charge.ChargePlanEntity;
import com.ruoyi.system.mapper.charge.ChargePlanMapper;
import com.ruoyi.system.service.charge.ChargePlanService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 充值方案表 Service
 *
 * <AUTHOR>
 * @date 2023-7-4 14:48:11
 */
@Service
public class ChargePlanServiceImpl implements ChargePlanService {

    @Autowired
    private ChargePlanMapper chargePlanMapper;

    @Override
    public Boolean insert(ChargePlanEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return chargePlanMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return chargePlanMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(ChargePlanEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return chargePlanMapper.updateById(entity) > 0;
    }

    @Override
    public ChargePlanEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return chargePlanMapper.selectById(id);
    }

    @Override
    public List<ChargePlanEntity> selectAll() {
        return chargePlanMapper.selectAll();
    }

    @Override
    public boolean batchMove(List<ChargePlanEntity> chargePlanEntities) {
        if(CollectionUtils.isEmpty(chargePlanEntities)){
            return false;
        }
        return chargePlanMapper.batchMove(chargePlanEntities) > 0;
    }

    @Override
    public int selectMaxSort() {
        return chargePlanMapper.selectMaxSort();
    }
}
