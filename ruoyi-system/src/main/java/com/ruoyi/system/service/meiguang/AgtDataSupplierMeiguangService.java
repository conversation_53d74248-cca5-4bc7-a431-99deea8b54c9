package com.ruoyi.system.service.meiguang;

import com.ruoyi.system.entity.meiguang.AgtDataSupplierSnapshotMeiguangEntity;

import java.util.Date;
import java.util.List;

/**
 * 美光接口服务
 *
 * <AUTHOR>
 * @date 2024/7/9
 */
public interface AgtDataSupplierMeiguangService {

    /**
     * 同步美光数据
     *
     * @param startTime 支付开始时间(精确到小时，包含)
     * @param endTime 支付结束时间(精确到小时，不包含)
     * @return 新增数量
     */
    int syncData(Date startTime, Date endTime);

    /**
     * 同步美光日数据
     *
     * @param date 日期
     * @return 新增数量
     */
    int syncData(Date date);

    /**
     * 美光接口调用
     *
     * @param startTime 支付开始时间 精确到小时,格式yyyyMMddHH 或yyyyMMddHHmmss（查询范围不能超7天）
     * @param endTime 支付结束时间 精确到小时,格式yyyyMMddHH 或yyyyMMddHHmmss（查询范围不能超7天）
     * @param page 分页页码，从1开始
     * @return 订单列表
     */
    List<AgtDataSupplierSnapshotMeiguangEntity> queryDataByApi(String startTime, String endTime, Integer page);

    List<AgtDataSupplierSnapshotMeiguangEntity> selectIncomeByDay(String start, String end);
}
