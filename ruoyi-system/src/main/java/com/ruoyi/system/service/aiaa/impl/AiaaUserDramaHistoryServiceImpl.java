package com.ruoyi.system.service.aiaa.impl;

import com.ruoyi.system.entity.aiaa.AiaaUserDramaHistoryEntity;
import com.ruoyi.system.mapper.aiaa.AiaaUserDramaHistoryMapper;
import com.ruoyi.system.service.aiaa.AiaaUserDramaHistoryService;
import com.ruoyi.system.req.playlet.aiaa.ListAiaaUserDramaHistoryReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 用户查看短剧历史 Service
 *
 * <AUTHOR>
 * @date 2023年7月11日 下午5:45:32
 */
@Service
public class AiaaUserDramaHistoryServiceImpl implements AiaaUserDramaHistoryService {

    @Autowired
    private AiaaUserDramaHistoryMapper aiaaUserDramaHistoryMapper;

    @Override
    public Boolean insert(AiaaUserDramaHistoryEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return aiaaUserDramaHistoryMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return aiaaUserDramaHistoryMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(AiaaUserDramaHistoryEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return aiaaUserDramaHistoryMapper.updateById(entity) > 0;
    }

    @Override
    public AiaaUserDramaHistoryEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return aiaaUserDramaHistoryMapper.selectById(id);
    }

    @Override
    public List<AiaaUserDramaHistoryEntity> selectList(ListAiaaUserDramaHistoryReq req) {
        return aiaaUserDramaHistoryMapper.selectList(req);
    }
}
