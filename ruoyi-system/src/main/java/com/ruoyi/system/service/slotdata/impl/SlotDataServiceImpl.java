package com.ruoyi.system.service.slotdata.impl;

import com.ruoyi.system.entity.slotdata.SlotDataEntity;
import com.ruoyi.system.mapper.slotdata.SlotDataMapper;
import com.ruoyi.system.req.slot.SlotDataReq;
import com.ruoyi.system.service.slotdata.SlotDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告位地域分流日数据Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SlotDataServiceImpl implements SlotDataService {
    @Autowired
    private SlotDataMapper slotDataMapper;

    /**
     * 查询广告位地域分流日数据
     *
     * @param id 广告位地域分流日数据主键
     * @return 广告位地域分流日数据
     */
    @Override
    public SlotDataEntity selectSlotDataById(Long id)
    {
        return slotDataMapper.selectSlotDataById(id);
    }

    /**
     * 查询广告位地域分流日数据列表
     *
     * @param req@return 广告位地域分流日数据
     */
    @Override
    public List<SlotDataEntity> selectSlotDataList(SlotDataReq req)
    {
        return slotDataMapper.selectSlotDataList(req);
    }

    /**
     * 新增广告位地域分流日数据
     *
     * @param slotData 广告位地域分流日数据
     * @return 结果
     */
    @Override
    public int insertSlotData(SlotDataEntity slotData)
    {

            return slotDataMapper.insertSlotData(slotData);
    }

    /**
     * 修改广告位地域分流日数据
     *
     * @param slotData 广告位地域分流日数据
     * @return 结果
     */
    @Override
    public int updateSlotData(SlotDataEntity slotData)
    {
        return slotDataMapper.updateSlotData(slotData);
    }

    @Override
    public int updateSlotDataAdd(SlotDataEntity slotData) {
        return slotDataMapper.updateSlotDataAdd(slotData);
    }

    /**
     * 批量删除广告位地域分流日数据
     *
     * @param ids 需要删除的广告位地域分流日数据主键
     * @return 结果
     */
    @Override
    public int deleteSlotDataByIds(Long[] ids)
    {
        return slotDataMapper.deleteSlotDataByIds(ids);
    }

    /**
     * 删除广告位地域分流日数据信息
     *
     * @param id 广告位地域分流日数据主键
     * @return 结果
     */
    @Override
    public int deleteSlotDataById(Long id)
    {
        return slotDataMapper.deleteSlotDataById(id);
    }

    @Override
    public int batchInsertOrUpdate(List<SlotDataEntity> list) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        return slotDataMapper.batchInsertOrUpdate(list);
    }
}
