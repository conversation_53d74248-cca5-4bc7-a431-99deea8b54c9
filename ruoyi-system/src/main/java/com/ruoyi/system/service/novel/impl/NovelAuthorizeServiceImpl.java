package com.ruoyi.system.service.novel.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.exception.BizRuntimeException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.novel.NovelEntity;
import com.ruoyi.system.manager.WxMaNovelService;
import com.ruoyi.system.req.novel.wx.WxMaNovelAuthBookRequest;
import com.ruoyi.system.resp.novelwx.WxNovelAuthBookResponse;
import com.ruoyi.system.service.novel.NovelAuthorizeService;
import com.ruoyi.system.service.novel.NovelMiniappService;
import com.ruoyi.system.service.novel.NovelService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小说授权Service接口实现
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Slf4j
@Service
public class NovelAuthorizeServiceImpl implements NovelAuthorizeService {

    @Autowired
    private WxMaNovelService wxMaNovelService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NovelMiniappService novelMiniappService;

    @Autowired
    private NovelService novelService;

    @Override
    public void authToMiniapp(String appId) {
        RLock lock = redissonClient.getLock(RedisKeyFactory.K051.join(appId));
        boolean success = lock.tryLock();
        try {
            if (!success) {
                throw new BizRuntimeException("授权中,请勿重复点击");
            }
            //查询所有小说
            WxMaService wxMaService = novelMiniappService.getNovelWxMaService("wxb2138333c615aec7");
            List<NovelEntity> novels = novelService.selectNovelList(new NovelEntity());
            novels.stream().filter(novel -> StringUtils.isNotBlank(novel.getBookId())).forEach(novel -> {
                authAddBook(appId, wxMaService, novel);
            });
        } catch (Exception e) {
            log.error("小程序小说授权失败,appId:{}", appId, e);
            throw new BizRuntimeException("授权失败");
        } finally {
            if (success) {
                lock.unlock();
            }
        }
    }

    /**
     * 授权小程序
     *
     * @param appId
     * @param wxMaService
     * @param novel
     */
    private void authAddBook(String appId, WxMaService wxMaService, NovelEntity novel) {
        WxMaNovelAuthBookRequest request = new WxMaNovelAuthBookRequest();
        request.setBook_id(novel.getBookId());
        request.setGrantee_appid(appId);
        try {
            WxNovelAuthBookResponse addbookauth = wxMaNovelService.addbookauth(wxMaService, Lists.newArrayList(request));
        } catch (WxErrorException e) {
            log.error("小程序小说批量授权失败，appId:{},bookId:{}", appId, novel.getBookId(),e);
        }
    }
}
