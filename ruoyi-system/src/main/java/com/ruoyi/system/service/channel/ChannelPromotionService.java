package com.ruoyi.system.service.channel;


import com.ruoyi.system.entity.channel.ChannelPromotionEntity;
import com.ruoyi.system.req.playlet.channel.ChannelListReq;

import java.util.List;

/**
 * 渠道推广表 Service
 *
 * <AUTHOR>
 * @date 2023-7-6 16:21:02
 */
public interface ChannelPromotionService {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean insert(ChannelPromotionEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    Boolean updateById(ChannelPromotionEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    ChannelPromotionEntity selectById(Long id);

    /**
     * 获取渠道列表
     */
    List<ChannelPromotionEntity> selectList(ChannelListReq req);

    /**
     * 根据参数获取渠道id列表
     * @param req
     * @return
     */
    List<Long> selectChannelIdsByReq(ChannelListReq req);

    /**
     * 根据id列表查询渠道信息
     * @param ids
     * @return
     */
    List<ChannelPromotionEntity> selectListByIds(List<Long> ids);
}
