package com.ruoyi.system.service.shortplay.data.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.shortplay.ShortplayDyMiniappDataBo;
import com.ruoyi.system.entity.shortplay.data.ShortplayDyMiniappDataEntity;
import com.ruoyi.system.mapper.shortplay.data.ShortplayDyMiniappDataMapper;
import com.ruoyi.system.req.juliang.AgtDataMiniappReq;
import com.ruoyi.system.service.shortplay.data.ShortplayDyMiniappDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 抖音小程序数据Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ShortplayDyMiniappDataServiceImpl implements ShortplayDyMiniappDataService {

    @Autowired
    private ShortplayDyMiniappDataMapper ShortplayDyMiniappDataMapper;

    @Override
    public List<ShortplayDyMiniappDataBo> selectList(AgtDataMiniappReq param) {
        if (null != param.getDimension()) {
            param.setDimension(
                    param.getDimension().stream().map(s -> {
                        if (StringUtils.containsAny(s, "cur_date", "miniapp_id")) {
                            return "d." + s;
                        }
                        return s;
                    }).collect(Collectors.toList())
            );
        }
        if (StringUtils.isBlank(param.getOrderColumn())) {
            param.setOrderColumn("income");
            param.setIsAsc(false);
        }
        param.setOrderColumn(StrUtil.toUnderlineCase(param.getOrderColumn())); // 字段映射，驼峰转下划线
        param.setOrderType(BooleanUtil.isTrue(param.getIsAsc()) ? "asc" : "desc");
        return ShortplayDyMiniappDataMapper.selectList(param);
    }

    @Override
    public int update(ShortplayDyMiniappDataEntity record) {
        if (null == record) {
            return 0;
        }
        Long dataId = getDataId(record);
        if (null == dataId) {
            ShortplayDyMiniappDataMapper.insert(record);
            dataId = getDataId(record);
        }
        record.setId(dataId);
        return ShortplayDyMiniappDataMapper.update(record);
    }

    @Override
    public List<ShortplayDyMiniappDataBo> selectListByUserName(AgtDataMiniappReq param) {
        if (null != param.getDimension()) {
            param.setDimension(
                    param.getDimension().stream().map(s -> {
                        if (StringUtils.containsAny(s, "cur_date", "miniapp_id")) {
                            return "d." + s;
                        }
                        return s;
                    }).collect(Collectors.toList())
            );
        }
        if (StringUtils.isBlank(param.getOrderColumn())) {
            param.setOrderColumn("income");
            param.setIsAsc(false);
        }
        param.setOrderColumn(StrUtil.toUnderlineCase(param.getOrderColumn())); // 字段映射，驼峰转下划线
        param.setOrderType(BooleanUtil.isTrue(param.getIsAsc()) ? "asc" : "desc");
        List<ShortplayDyMiniappDataBo> shortplayDyMiniappDataBos;
        if (param.getDimension().contains("d.cur_date")) {
            shortplayDyMiniappDataBos = ShortplayDyMiniappDataMapper.selectListByUserName(param);
        } else {
            shortplayDyMiniappDataBos = ShortplayDyMiniappDataMapper.selectListByUserNameNoDate(param);
        }
        return shortplayDyMiniappDataBos;
    }

    private Long getDataId(ShortplayDyMiniappDataEntity record) {
        ShortplayDyMiniappDataEntity data = ShortplayDyMiniappDataMapper.selectBy(record.getCurDate(), record.getMiniappId());
        return null != data ? data.getId() : null;
    }
}
