package com.ruoyi.system.service.video.impl;

import com.ruoyi.common.enums.DyAuditStatusEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.video.VideoGroupDyRelateEntity;
import com.ruoyi.system.mapper.video.VideoGroupDyRelateMapper;
import com.ruoyi.system.service.video.VideoGroupDyRelateService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 短剧和抖音小程序关联表 Service
 *
 * <AUTHOR>
 * @date 2024-2-5 14:12:11
 */
@Service
public class VideoGroupDyRelateServiceImpl implements VideoGroupDyRelateService {

    @Autowired
    private VideoGroupDyRelateMapper videoGroupDyRelateMapper;

    @Override
    public Boolean insert(VideoGroupDyRelateEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return videoGroupDyRelateMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return videoGroupDyRelateMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(VideoGroupDyRelateEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return videoGroupDyRelateMapper.updateById(entity) > 0;
    }

    @Override
    public VideoGroupDyRelateEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return videoGroupDyRelateMapper.selectById(id);
    }

    @Override
    public List<VideoGroupDyRelateEntity> selectListByGroupIdAndAppIds(Long videoGroupId, List<String> appIds) {
        if(CollectionUtils.isEmpty(appIds) || NumberUtils.isNullOrLteZero(videoGroupId)){
            return Collections.emptyList();
        }
        return videoGroupDyRelateMapper.selectListByGroupIdAndAppIds(videoGroupId, appIds);
    }

    @Override
    public VideoGroupDyRelateEntity selectByGroupIdAndAppId(Long videoGroupId, String appId) {
        if(NumberUtils.isNullOrLteZero(videoGroupId) || StringUtils.isNull(appId)){
            return null;
        }
        return videoGroupDyRelateMapper.selectByGroupIdAndAppId(videoGroupId, appId);
    }

    @Override
    public boolean batchInsertOrUpdate(List<VideoGroupDyRelateEntity> updateLists) {
        if(CollectionUtils.isEmpty(updateLists)){
            return false;
        }
        return videoGroupDyRelateMapper.batchInsertOrUpdate(updateLists) > 0;
    }

    @Override
    public List<VideoGroupDyRelateEntity> selectDyAllAuditTask() {
        return videoGroupDyRelateMapper.selectDyAllAuditTask(DyAuditStatusEnum.AUDITING.getStatus());
    }

    @Override
    public List<VideoGroupDyRelateEntity> selectListByGroupIds(List<Long> videoGroupIds) {
        if(CollectionUtils.isEmpty(videoGroupIds)){
            return Collections.emptyList();
        }
        return videoGroupDyRelateMapper.selectListByGroupIds(videoGroupIds);
    }

    @Override
    public VideoGroupDyRelateEntity selectByDyAlbumId(Long albumId) {
        if(NumberUtils.isNullOrLteZero(albumId)){
            return null;
        }
        return videoGroupDyRelateMapper.selectByDyAlbumId(albumId);
    }
}
