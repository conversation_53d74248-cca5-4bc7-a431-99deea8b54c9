package com.ruoyi.system.service.coupon;

import com.ruoyi.system.entity.coupon.PlayletCouponEntity;

import java.util.List;

/**
 * 短剧小程序看剧权益券Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-22
 */
public interface PlayletCouponService {
    /**
     * 查询短剧小程序看剧权益券
     * 
     * @param id 短剧小程序看剧权益券主键
     * @return 短剧小程序看剧权益券
     */
    public PlayletCouponEntity selectPlayletCouponById(String id);

    /**
     * 查询短剧小程序看剧权益券列表
     * 
     * @param playletCoupon 短剧小程序看剧权益券
     * @return 短剧小程序看剧权益券集合
     */
    public List<PlayletCouponEntity> selectPlayletCouponList(PlayletCouponEntity playletCoupon);

    /**
     * 新增短剧小程序看剧权益券
     *
     * @param playletCoupon 短剧小程序看剧权益券
     * @return 结果
     */
    public boolean insertPlayletCoupon(PlayletCouponEntity playletCoupon);

    /**
     * 修改短剧小程序看剧权益券
     * 
     * @param playletCoupon 短剧小程序看剧权益券
     * @return 结果
     */
    public int updatePlayletCoupon(PlayletCouponEntity playletCoupon);

    /**
     * 批量删除短剧小程序看剧权益券
     * 
     * @param ids 需要删除的短剧小程序看剧权益券主键集合
     * @return 结果
     */
    public int deletePlayletCouponByIds(String[] ids);

    /**
     * 删除短剧小程序看剧权益券信息
     * 
     * @param id 短剧小程序看剧权益券主键
     * @return 结果
     */
    public int deletePlayletCouponById(String id);

    /**
     * 根据订单id查询券
     * @param orderId
     * @return
     */
    PlayletCouponEntity selectByOrderId(String orderId);

    /**
     * 查询用户是否已经核销解锁该剧
     * @param phone
     * @param videoId
     * @return
     */
    PlayletCouponEntity selectByPhoneAndVideoId(String phone, String videoId);

    /**
     * 查询用户未使用的券
     */
    PlayletCouponEntity selectUnusedListByPhone(String phone);

    /**
     * 查询用户券
     * @param phone
     * @param useStatus
     * @return
     */
    List<PlayletCouponEntity> selectByPhoneAndStatus(String phone, Integer useStatus);

    /**
     * 上报券使用/过期状态
     *
     * @param entity
     * @return
     */
    boolean reportCouponStatus(PlayletCouponEntity entity);

    /**
     * 根据状态和日期查询过期券
     * @param status
     * @return
     */
    List<PlayletCouponEntity> selectExpiredList(Integer status,Long offset);

    /**
     * 批量更新券使用状态
     * @param playletCouponIdList
     * @return
     */
    Boolean batchUpdateUseStatusByIds(List<Long> playletCouponIdList,Integer status);

}