package com.ruoyi.system.service.user;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.entity.user.AgtAccountUserEntity;

/**
 * 短剧分销人员Service接口
 *
 * <AUTHOR>
 */
public interface AgtAccountUserService {
    /**
     * 查询短剧分销人员
     *
     * @param id 短剧分销人员主键
     * @return 短剧分销人员
     */
    public AgtAccountUserEntity selectAgtAccountUserById(String id);

    /**
     * 查询短剧分销人员列表
     *
     * @param agtAccountUser 短剧分销人员
     * @return 短剧分销人员集合
     */
    public List<AgtAccountUserEntity> selectAgtAccountUserList(AgtAccountUserEntity agtAccountUser);

    /**
     * 新增短剧分销人员
     *
     * @param agtAccountUser 短剧分销人员
     * @return 结果
     */
    public int insertAgtAccountUser(AgtAccountUserEntity agtAccountUser);

    /**
     * 修改短剧分销人员
     *
     * @param agtAccountUser 短剧分销人员
     * @return 结果
     */
    public int updateAgtAccountUser(AgtAccountUserEntity agtAccountUser);

    /**
     * 批量删除短剧分销人员
     *
     * @param ids 需要删除的短剧分销人员主键集合
     * @return 结果
     */
    public int deleteAgtAccountUserByIds(String[] ids);

    /**
     * 删除短剧分销人员信息
     *
     * @param id 短剧分销人员主键
     * @return 结果
     */
    public int deleteAgtAccountUserById(String id);

    /**
     * 根据真实姓名查询简称
     * @param nickName
     * @return
     */
    List<String> selectNickNameByRealName(String nickName);

    /**
     * 根据简称查询真实姓名
     * @param nickName
     * @return
     */
    Map<String,String> selectRealNameMapByNickName(List<String> nickName);

}
