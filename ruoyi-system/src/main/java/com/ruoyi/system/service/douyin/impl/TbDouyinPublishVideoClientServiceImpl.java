package com.ruoyi.system.service.douyin.impl;

import com.ruoyi.system.entity.douyin.TbDouyinPublishVideoClient;
import com.ruoyi.system.mapper.douyin.TbDouyinPublishVideoClientMapper;
import com.ruoyi.system.service.douyin.ITbDouyinPublishVideoClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 抖音小程序推广视频撮合中介Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Service
public class TbDouyinPublishVideoClientServiceImpl implements ITbDouyinPublishVideoClientService
{
    @Autowired
    private TbDouyinPublishVideoClientMapper tbDouyinPublishVideoClientMapper;

    @Override
    public Map<String, String> getClientMap() {
        List<TbDouyinPublishVideoClient> list = selectTbDouyinPublishVideoClientList(null);
        return list.stream().collect(Collectors.toMap(TbDouyinPublishVideoClient::getClientKey, TbDouyinPublishVideoClient::getClientName));
    }

    /**
     * 查询抖音小程序推广视频撮合中介
     *
     * @param id 抖音小程序推广视频撮合中介主键
     * @return 抖音小程序推广视频撮合中介
     */
    @Override
    public TbDouyinPublishVideoClient selectTbDouyinPublishVideoClientById(String id)
    {
        return tbDouyinPublishVideoClientMapper.selectTbDouyinPublishVideoClientById(id);
    }

    /**
     * 查询抖音小程序推广视频撮合中介列表
     *
     * @param tbDouyinPublishVideoClient 抖音小程序推广视频撮合中介
     * @return 抖音小程序推广视频撮合中介
     */
    @Override
    public List<TbDouyinPublishVideoClient> selectTbDouyinPublishVideoClientList(TbDouyinPublishVideoClient tbDouyinPublishVideoClient)
    {
        return tbDouyinPublishVideoClientMapper.selectTbDouyinPublishVideoClientList(tbDouyinPublishVideoClient);
    }

    /**
     * 新增抖音小程序推广视频撮合中介
     *
     * @param tbDouyinPublishVideoClient 抖音小程序推广视频撮合中介
     * @return 结果
     */
    @Override
    public int insertTbDouyinPublishVideoClient(TbDouyinPublishVideoClient tbDouyinPublishVideoClient)
    {
        return tbDouyinPublishVideoClientMapper.insertTbDouyinPublishVideoClient(tbDouyinPublishVideoClient);
    }

    /**
     * 修改抖音小程序推广视频撮合中介
     *
     * @param tbDouyinPublishVideoClient 抖音小程序推广视频撮合中介
     * @return 结果
     */
    @Override
    public int updateTbDouyinPublishVideoClient(TbDouyinPublishVideoClient tbDouyinPublishVideoClient)
    {
        return tbDouyinPublishVideoClientMapper.updateTbDouyinPublishVideoClient(tbDouyinPublishVideoClient);
    }

    /**
     * 批量删除抖音小程序推广视频撮合中介
     *
     * @param ids 需要删除的抖音小程序推广视频撮合中介主键
     * @return 结果
     */
    @Override
    public int deleteTbDouyinPublishVideoClientByIds(String[] ids)
    {
        return tbDouyinPublishVideoClientMapper.deleteTbDouyinPublishVideoClientByIds(ids);
    }

    /**
     * 删除抖音小程序推广视频撮合中介信息
     *
     * @param id 抖音小程序推广视频撮合中介主键
     * @return 结果
     */
    @Override
    public int deleteTbDouyinPublishVideoClientById(String id)
    {
        return tbDouyinPublishVideoClientMapper.deleteTbDouyinPublishVideoClientById(id);
    }
}
