package com.ruoyi.system.service.data.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.playlet.DayStatisticsBo;
import com.ruoyi.system.entity.data.PlayletDayDataEntity;
import com.ruoyi.system.mapper.data.PlayletDayDataMapper;
import com.ruoyi.system.service.data.PlayletDayDataService;
import com.ruoyi.system.req.playlet.param.DayChannelStatisticsParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 短剧日数据表 Service
 *
 * <AUTHOR>
 * @date 2023-8-2 15:24:47
 */
@Service
public class PlayletDayDataServiceImpl implements PlayletDayDataService {

    @Autowired
    private PlayletDayDataMapper playletDayDataMapper;

    @Override
    public List<PlayletDayDataEntity> statisticsByDate(DayStatisticsBo dayStatisticsBo) {
        return playletDayDataMapper.statisticsByDate(dayStatisticsBo,null);
    }

    @Override
    public List<PlayletDayDataEntity> statisticsPlatformByDate(DayStatisticsBo dayStatisticsBo) {
        return playletDayDataMapper.statisticsByDate(dayStatisticsBo,0L);//channelId = 0代表自然流量，不来源任何渠道
    }

    @Override
    public PlayletDayDataEntity statisticsSum(DayStatisticsBo dayStatisticsBo) {
        if(Objects.nonNull(dayStatisticsBo.getEndDate())){
            dayStatisticsBo.setEndDate(DateUtil.endOfDay(dayStatisticsBo.getEndDate()));
        }
        return playletDayDataMapper.statisticsSum( dayStatisticsBo,null);
    }

    @Override
    public PlayletDayDataEntity statisticsPlatformSum(DayStatisticsBo dayStatisticsBo) {
        if(Objects.nonNull(dayStatisticsBo.getEndDate())){
            dayStatisticsBo.setEndDate(DateUtil.endOfDay(dayStatisticsBo.getEndDate()));
        }
        return playletDayDataMapper.statisticsSum(dayStatisticsBo,0L);//channelId = 0代表自然流量，不来源任何渠道
    }

    @Override
    public List<PlayletDayDataEntity> statisticsByDateAndChannel(DayChannelStatisticsParam param) {
        if(Objects.nonNull(param.getEndDate())){
            param.setEndDate(DateUtil.endOfDay(param.getEndDate()));
        }
        return playletDayDataMapper.statisticsByDateAndChannel(param);
    }

    @Override
    public PlayletDayDataEntity statisticsSumByDateAndChannel(DayChannelStatisticsParam param) {
        if(Objects.nonNull(param.getEndDate())){
            param.setEndDate(DateUtil.endOfDay(param.getEndDate()));
        }
        return playletDayDataMapper.statisticsSumByDateAndChannel(param);
    }
}