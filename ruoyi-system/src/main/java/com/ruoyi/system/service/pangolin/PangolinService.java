package com.ruoyi.system.service.pangolin;


import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.system.req.pangolin.PangolinAuditCodeReq;
import com.ruoyi.system.req.pangolin.PangolinGetQrCodeReq;
import com.ruoyi.system.req.pangolin.PangolinReleaseCodeReq;
import com.ruoyi.system.req.pangolin.PangolinUploadCodeReq;

/**
 * 抖音第三方小程序接口Service
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public interface PangolinService {

    /**
     * 缓存component_ticket
     *
     * @param componentAppId 第三方小程序应用 appid
     * @param ticket 推送的 component_ticket
     */
    void cacheTicket(String componentAppId, String ticket);

    /**
     * 获取component_ticket
     *
     * @param componentAppId 第三方小程序应用 appid
     * @return 缓存的component_ticket
     */
    String getTicket(String componentAppId);

    /**
     * 获取第三方小程序接口调用凭据V2
     *
     * @param componentAppId 第三方小程序应用 appid
     * @return component_access_token
     */
    String getComponentAccessToken(String componentAppId);

    /**
     * 找回授权码V2
     *
     * @param authorizationAppId 授权小程序应用 appid
     * @return authorization_code
     */
    String getAuthorizationCode(String authorizationAppId);

    /**
     * 获取授权小程序接口调用凭据V2
     *
     * @param authorizationAppId 授权小程序应用 appid
     * @return authorizer_access_token
     */
    String getAuthorizerAccessToken(String authorizationAppId);

    /**
     * 提交代码
     *
     * @param req 参数
     * @return 提交结果
     */
    JSONObject uploadCode(PangolinUploadCodeReq req);

    /**
     * 提审代码
     *
     * @param req 参数
     * @return 提审结果
     */
    JSONObject auditCode(PangolinAuditCodeReq req);

    /**
     * 发布代码
     *
     * @param req 参数
     * @return 发布结果
     */
    JSONObject releaseCode(PangolinReleaseCodeReq req);

    /**
     * 获取二维码
     *
     * @param req 参数
     * @return 提审结果
     */
    JSONObject getQrCode(PangolinGetQrCodeReq req);
}
