package com.ruoyi.system.service.stat.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.constant.enums.StatTypeEnum;
import com.ruoyi.system.entity.dyiaa.TbDouyinIaaConversionConfig;
import com.ruoyi.system.entity.dyiaa.TbDouyinIaaUser;
import com.ruoyi.system.service.dyiaa.DouyinIaaEcpmService;
import com.ruoyi.system.service.dyiaa.DouyinIaaUserConfigSyncService;
import com.ruoyi.system.service.dyiaa.ITbDouyinIaaUserService;
import com.ruoyi.system.entity.iaa.IaaAdRecord;
import com.ruoyi.system.service.iaa.IIaaAdRecordService;
import com.ruoyi.system.req.StatPointReq;
import com.ruoyi.system.entity.stat.InnerLog;
import com.ruoyi.system.service.stat.IInnerLogService;
import com.ruoyi.system.service.stat.StatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 埋点服务
 * <AUTHOR>
 * @date 2024/4/9 15:27
 */
@Slf4j
@Service
public class StatServiceImpl implements StatService {

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IInnerLogService innerLogService;
    @Autowired
    private ITbDouyinIaaUserService tbDouyinIaaUserService;
    @Autowired
    private DouyinIaaUserConfigSyncService douyinIaaUserConfigSyncService;
    @Autowired
    private DouyinIaaEcpmService douylinIaaEcpmService;
    @Autowired
    private IIaaAdRecordService iaaAdRecordService;
    @Override
    public void point(StatPointReq req) {
        GlobalThreadPool.statExecutorService.execute(() -> {
            log.info("埋点记录,req:{}", req);

            //查询用户信息
            TbDouyinIaaUser tbDouyinIaaUser = tbDouyinIaaUserService.selectByUserId(req.getUserId());
            if(Objects.isNull(tbDouyinIaaUser)){
                //主动去拉取一次
                douyinIaaUserConfigSyncService.batchUpdateUserByUserIds(Lists.newArrayList(req.getUserId()));
                tbDouyinIaaUser = tbDouyinIaaUserService.selectByUserId(req.getUserId());
                if(Objects.isNull(tbDouyinIaaUser)){
                    log.warn("埋点记录异常，用户信息查询失败,req:{}", req);
                    return;
                }
            }
            //增加看完广告记录
            addIaaAdRecord(req,tbDouyinIaaUser);

            //新增明细日志
            InnerLog log = new InnerLog();
            log.setType(req.getType());
            log.setCurDate(new Date());
            log.setUserId(req.getUserId());
            log.setLogJson(JSONObject.toJSONString(tbDouyinIaaUser));
            innerLogService.insertInnerLog(log);

            //数据上报
            checkAndReport(req,tbDouyinIaaUser);
        });
    }

    /**
     * 增加看完广告的记录
     * @param req
     * @param user
     */
    private void addIaaAdRecord(StatPointReq req,TbDouyinIaaUser user) {
        if(Objects.isNull(user) || Objects.isNull(req) || !Objects.equals(req.getType(), StatTypeEnum.WATCH_VIDEO_AD.getType())){
            return;
        }
        IaaAdRecord record = new IaaAdRecord();
        record.setAppId(user.getAppId());
        record.setClickId(req.getClickId());
        record.setUserId(req.getUserId());
        record.setPlatform(user.getPlatform());
        record.setPromotionId(user.getPromotionId());
        iaaAdRecordService.insertIaaAdRecord(record);
    }

    private void checkAndReport(StatPointReq req,TbDouyinIaaUser user) {
        if(!SpringEnvironmentUtils.isProd()){
            return;
        }
        if(Objects.isNull(user) || Objects.isNull(req) || StringUtils.isBlank(user.getConfigId())){
            return;
        }
        // 回传开关判断
        String promotionId = user.getPromotionId();
        if (redisCache.hasKey(CacheConstants.DOUYIN_IAA + "switch:conversation:off:" + promotionId)) {
            return;
        }

        // 查询上报配置
        TbDouyinIaaConversionConfig config = douylinIaaEcpmService.getConversationConfig(user.getConfigId());
        if (null == config || null == config.getConfigId() || null == config.getRebackTime()) {
            return;
        }
        String reportRedisKey = CacheConstants.DOUYIN_IAA + "ipu:conversation:" + Md5Utils.hash(user.getUserId() + user.getClickId() + config.getConfigId());
        // 判断是否已经上报过
        if (redisCache.hasKey(reportRedisKey)) {
            return;
        }
        Date now = new Date();
        Date today = DateUtil.beginOfDay(now);
        Date yesterday = DateUtil.offsetDay(now, -1);
        Date tomorrow = DateUtil.offsetDay(today, 1);

        int count = Objects.equals(config.getRebackTime(),1)?
                iaaAdRecordService.countByUserIdAndDate(user.getUserId(), user.getPromotionId(), yesterday,now):
                iaaAdRecordService.countByUserIdAndDate(user.getUserId(), user.getPromotionId(), today,tomorrow);
        if(count >= config.getIpu()){
            reportGuangDianTong(req, user,reportRedisKey);
        }

    }

    /**
     * 上报广点通
     * @param req
     * @param user
     */
    private void reportGuangDianTong(StatPointReq req,TbDouyinIaaUser user,String reportRedisKey) {

        if(!Objects.equals(user.getPlatform(),"weixin")){
            return;
        }
        JSONArray actions = new JSONArray();
        JSONObject action = new JSONObject();
        action.put("action_time", new Date().getTime() / 1000);
        JSONObject userId = new JSONObject();
        userId.put("wechat_openid", user.getOpenId());
        userId.put("wechat_app_id",user.getAppId());
        action.put("user_id", userId);

        JSONObject trace = new JSONObject();
        trace.put("click_id",req.getClickId());
        action.put("trace", trace);
        action.put("action_type", "CUSTOM");
        action.put("custom_action", "UV_CORE_ACTION");
        actions.add(action);

        String resp = HttpUtil.post(req.getCallback_url(), actions.toJSONString());
        log.info("广点通上报结果,req:{},resp:{}",req,resp);
        redisCache.setCacheObject(reportRedisKey,1,1, TimeUnit.DAYS);
    }
}
