package com.ruoyi.system.service.nuohe.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.entity.fundStat.SupplierIncomeItem;
import com.ruoyi.system.entity.nuohe.AgtDataSupplierSnapshotNuoheEntity;
import com.ruoyi.system.entity.nuohe.NuoheOrder;
import com.ruoyi.system.entity.nuohe.NuoheOrderResp;
import com.ruoyi.system.entity.supplier.SupplierEnum;
import com.ruoyi.system.mapper.nuohe.NuoheMapper;
import com.ruoyi.system.service.SupplierService;
import com.ruoyi.system.service.nuohe.NuoheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @author: keboom
 * @date: 2024/8/28
 */
@Slf4j
@Service
public class NuoheServiceImpl implements NuoheService, SupplierService {

    @Autowired
    private NuoheMapper nuoheMapper;
    // 诺和每次过去 100 条订单
    private static final int pageSize = 100;
    private static final String organizationId = "f380561066c45a6707b441eb140cd5cd";

    @Async
    @Override
    public void getOrderInfoSaveToDB(Date startTime, Date endTime) {
        NuoheOrderResp orderList = getOrderList(startTime, endTime, 1, 1);
        if (orderList == null || orderList.getCode() != 0) {
            log.error("获取 诺禾 订单信息失败. startTime {} endTime {} resp {}", startTime, endTime, orderList);
            return;
        }

        int total = orderList.getTotalCount();
        if (total == 0) {
            log.info("诺禾 订单信息为空");
            return;
        }
        int page = 1;
        int pageNum = NumberUtil.ceilDiv(total, pageSize);
        while (page <= pageNum) {
            NuoheOrderResp orderList1 = getOrderList(startTime, endTime, page, pageSize);
            if (orderList1 == null || orderList1.getData() == null) {
                log.info("触摸订单信息为空 start: {} end: {} order:{}", startTime, endTime, orderList1);
                return;
            }
            // 入库
            saveToDB(orderList1);
            page++;
        }
    }

    private void saveToDB(NuoheOrderResp orderResp) {
        List<NuoheOrder> data = orderResp.getData();
        List<AgtDataSupplierSnapshotNuoheEntity> entities = new ArrayList<>();
        for (NuoheOrder order : data) {
            int rechargeStatus = order.getRechargeStatus();
            // 如果未支付，则不入库
            if (rechargeStatus == 0) {
                continue;
            }
            AgtDataSupplierSnapshotNuoheEntity entity = new AgtDataSupplierSnapshotNuoheEntity();
            BeanUtil.copyProperties(order, entity);
            entity.setOrganizationId(organizationId);
            entity.setAppId(order.getAppid());
            entity.setAppName(order.getAppname());
            entity.setAdvertiserId(order.getPromotionName());
            DateTime payTime = DateUtil.parse(order.getPayTime());
            entity.setCurDate(DateUtil.beginOfDay(payTime));
            entity.setCurHour(Long.valueOf(DateUtil.hour(payTime, true)));
            setDefault(entity);

            entities.add(entity);
        }
        if (entities.isEmpty()) {
            return;
        }
        nuoheMapper.saveOrUpdateBatch(entities);
    }

    private void setDefault(AgtDataSupplierSnapshotNuoheEntity entity) {
        if (entity.getPromotionName() == null) {
            entity.setPromotionName("");
        }
        if (entity.getPayFee() == null) {
            entity.setPayFee(0L);
        }
        if (entity.getPayTime() == null) {
            entity.setPayTime(new Date());
        }
        if (entity.getRechargeId() == null) {
            entity.setRechargeId("");
        }
        if (entity.getUserId() == null) {
            entity.setUserId("");
        }
        if (entity.getRechargeStatus() == null) {
            entity.setRechargeStatus("");
        }
        if (entity.getDeviceType() == null) {
            entity.setDeviceType("");
        }
        if (entity.getOrganizationId() == null) {
            entity.setOrganizationId("");
        }
        if (entity.getAppId() == null) {
            entity.setAppId("");
        }
        if (entity.getAppName() == null) {
            entity.setAppName("");
        }
        if (entity.getPlatform() == null) {
            entity.setPlatform("");
        }
        if (entity.getAgentNickname() == null) {
            entity.setAgentNickname("");
        }
        if (entity.getAgentUsername() == null) {
            entity.setAgentUsername("");
        }
    }

    private NuoheOrderResp getOrderList(Date startTime, Date endTime, int pageNum, int pageSize) {
        String start = DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN);
        String end = DateUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN);
        HashMap<String, String> reqParamMap = new HashMap<>();
        // 此处 id，是机构 id
        reqParamMap.put("id", organizationId);
        reqParamMap.put("startTime", start);
        reqParamMap.put("endTime", end);
        reqParamMap.put("pageNum", String.valueOf(pageNum));
        reqParamMap.put("pageSize", String.valueOf(pageSize));
        reqParamMap.put("secret", "mL2l4Y6SEuL534TutNEfZJFCMQmDajQt");

        StringBuilder param = new StringBuilder();
        reqParamMap.forEach((k, v) -> {
            param.append(k).append("=").append(v).append("&");
        });
        param.deleteCharAt(param.length() - 1);

        String resp = HttpUtil.createGet("https://tcb-sjzsxaxmi7c3ybb-1cp15f4b3c5b.service.tcloudbase.com/agent_data?"
                + param).execute().body();
        NuoheOrderResp nuoheOrderResp = JSONObject.parseObject(resp, NuoheOrderResp.class);
        return nuoheOrderResp;
    }

    @Override
    public HashMap<String, SupplierIncomeItem> getDateSuppItemMap(String startTime, String endTime) {
        HashMap<String, SupplierIncomeItem> dateSuppItemMap = new HashMap<>();
        List<AgtDataSupplierSnapshotNuoheEntity> entities = nuoheMapper.selectIncomeByDay(startTime, endTime);
        for (AgtDataSupplierSnapshotNuoheEntity entity : entities) {
            SupplierIncomeItem item = new SupplierIncomeItem();
            item.setAmount(NumberUtil.div(BigDecimal.valueOf(entity.getPayFee()), 100, 2, RoundingMode.HALF_UP));
            item.setSupplier(SupplierEnum.NUOHE.getName());
            dateSuppItemMap.put(DateUtil.formatDate(entity.getCurDate()), item);
        }
        return dateSuppItemMap;
    }
}
