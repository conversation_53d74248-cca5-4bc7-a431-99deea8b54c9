package com.ruoyi.system.service.shortplay.stat.impl;

import java.util.List;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.shortplay.stat.ShortplayStatMapper;
import com.ruoyi.system.entity.shortplay.stat.ShortplayStatEntity;
import com.ruoyi.system.service.shortplay.stat.ShortplayStatService;

/**
 * 短剧小程序埋点Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShortplayStatServiceImpl implements ShortplayStatService {

    @Autowired
    private ShortplayStatMapper shortplayStatMapper;
    @Value("${stat.send}")
    public Boolean statSend;

    @Override
    public void stat(Integer type, String userId, String appId, String tfid, String promotionId, String cost) {
        return;
//        String url = null;
//        try {
//            if (!SpringEnvironmentUtils.isProd() || !statSend) {
//                return;
//            }
//            // action: appId.行为.tfid.promotionId.收益
//            String uid = StringUtils.defaultString(userId);
//            url = StrUtil.format("http://172.20.216.118:8905/stat?scene=2&uid={}&action={}.{}.{}.{}.{}",
//                    uid, appId, type, tfid, StringUtils.defaultString(promotionId), cost);
//            HttpUtil.createGet(url).executeAsync();
//        } catch (Exception e) {
//            log.error("短剧小程序埋点异常, url={}", url, e);
//        }
    }

    @Override
    public List<ShortplayStatEntity> selectList(ShortplayStatEntity param) {
        return shortplayStatMapper.selectList(param);
    }

    @Override
    public int insert(ShortplayStatEntity stat) {
        return shortplayStatMapper.insert(stat);
    }

    @Override
    public int update(ShortplayStatEntity stat) {
        return shortplayStatMapper.update(stat);
    }
}
