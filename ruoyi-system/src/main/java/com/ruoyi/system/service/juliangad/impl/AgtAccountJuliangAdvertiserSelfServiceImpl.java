package com.ruoyi.system.service.juliangad.impl;

import com.bytedance.ads.ApiClient;
import com.bytedance.ads.ApiException;
import com.bytedance.ads.api.CustomerCenterAdvertiserListV2Api;
import com.bytedance.ads.model.CustomerCenterAdvertiserListV2Response;
import com.bytedance.ads.model.CustomerCenterAdvertiserListV2ResponseDataListInner;
import com.ruoyi.system.entity.juliang.AgtAccountJuliangEntity;
import com.ruoyi.system.entity.juliangad.AgtAccountAdvertiserConstant;
import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.entity.supplier.AgtAccountSupplierEntity;
import com.ruoyi.system.entity.user.AgtAccountUserEntity;
import com.ruoyi.system.mapper.juliang.AgtAccountJuliangMapper;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserSelfMapper;
import com.ruoyi.system.mapper.supplier.AgtAccountSupplierMapper;
import com.ruoyi.system.mapper.user.AgtAccountUserMapper;
import com.ruoyi.system.service.juliangad.AgtAccountJuliangAdvertiserSelfService;
import com.ruoyi.system.service.user.AgtAccountUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * {@code @author:} keboom
 * {@code @date:} 2024/6/28
 */
@Slf4j
@Service
public class AgtAccountJuliangAdvertiserSelfServiceImpl implements AgtAccountJuliangAdvertiserSelfService {

    @Autowired
    private AgtAccountJuliangAdvertiserSelfMapper agtAccountJuliangAdvertiserSelfMapper;
    @Autowired
    private AgtAccountUserMapper agtAccountUserMapper;
    @Autowired
    private AgtAccountSupplierMapper agtAccountSupplierMapper;
    @Autowired
    private AgtAccountJuliangMapper agtAccountJuliangMapper;


    /**
     * @param accountId
     * @param accessToken
     * @param page
     * @param pageSize
     * @return 不规范的广告账号
     * @throws ApiException
     */
    @Override
    public List<AgtAccountJuliangAdvertiserEntity> getAccountAdvertisersSaveToDB(Long accountId, String accessToken, long page, long pageSize) {
        AgtAccountJuliangEntity agtAccountJuliangEntity = agtAccountJuliangMapper.selectByAccountId(accountId);
        CustomerCenterAdvertiserListV2Api api = new CustomerCenterAdvertiserListV2Api();
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", accessToken);
        api.setApiClient(client);

        CustomerCenterAdvertiserListV2Response result = null;
        try {
            result = api.openApi2CustomerCenterAdvertiserListGet(null, accountId, null, page, pageSize);
        } catch (ApiException e) {
            log.warn("Get account advertiser failed {}", e.getMessage());
        }
        if (result == null || result.getData() == null || result.getData().getList() == null) {
            log.warn("Get account advertiser failed {}", result.toString());
            return null;
        }
        List<CustomerCenterAdvertiserListV2ResponseDataListInner> list = result.getData().getList();
        if (CollectionUtils.isEmpty(list)) {
            log.warn("Account {} doesn't have any advertiser", accountId);
            return null;
        }

        log.info("Get account {} advertisers success list.size{}", accountId, list.size());
        ArrayList<AgtAccountJuliangAdvertiserEntity> nonStandard = new ArrayList<>();
        List<AgtAccountJuliangAdvertiserEntity> agtAccountJuliangEntities = new ArrayList<>();

        // 校验广告：用户、分销商，先不校验为空的情况了
        List<AgtAccountSupplierEntity> supplierList = agtAccountSupplierMapper.selectAgtAccountSupplierList(null);
        AgtAccountUserEntity selectPitcher = new AgtAccountUserEntity();
        // 2表示投手
        selectPitcher.setPost("2");
        List<AgtAccountUserEntity> userList = agtAccountUserMapper.selectAgtAccountUserList(selectPitcher);
        Set<String> supplierSet = supplierList.stream()
                .map(AgtAccountSupplierEntity::getSupplierNameShort).collect(Collectors.toSet());
        Set<String> userSet = userList.stream()
                .map(AgtAccountUserEntity::getUserNickname).collect(Collectors.toSet());

        for (CustomerCenterAdvertiserListV2ResponseDataListInner inner : list) {
            AgtAccountJuliangAdvertiserEntity entity = new AgtAccountJuliangAdvertiserEntity();
            entity.setAccountId(accountId);
            entity.setAccountName(agtAccountJuliangEntity.getAccountName());
            entity.setAdvertiserId(inner.getAdvertiserId());
            String advertiserName = inner.getAdvertiserName();
            advertiserName = advertiserName.replaceAll(" ", "");
            entity.setAdvertiserName(advertiserName);
            entity.setRegStatus(1L);
            // parse advertiser name
            if (!AgtAccountAdvertiserConstant.checkAdvertiserNameFormat(advertiserName)) {
                entity.setRegStatus(2L);
                nonStandard.add(entity);
            }
            // 账号名称命名规范解析入库：解析后还要校验是否存在
            AgtAccountAdvertiserConstant.parseAdvertiserName(entity, advertiserName);
            if (!nonStandard.contains(entity)) {
                if (!supplierSet.contains(entity.getSupplierNameShort())
                        || !userSet.contains(entity.getUserNickname())) {
                    entity.setRegStatus(2L);
                    nonStandard.add(entity);
                }
            }
            // 未知的用户、分销商
            if (!userSet.contains(entity.getUserNickname())) {
                entity.setUserNickname("未知");
            }
            if (!supplierSet.contains(entity.getSupplierNameShort())) {
                entity.setSupplierNameShort("未知");
            }
            agtAccountJuliangEntities.add(entity);
        }
        if (agtAccountJuliangEntities.isEmpty()) {
            return agtAccountJuliangEntities;
        }
        agtAccountJuliangAdvertiserSelfMapper.batchInsertOrUpdate(agtAccountJuliangEntities);
        return nonStandard;
    }
}
