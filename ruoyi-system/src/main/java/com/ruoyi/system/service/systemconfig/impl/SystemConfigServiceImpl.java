package com.ruoyi.system.service.systemconfig.impl;

import com.ruoyi.system.entity.systemconfig.SystemConfigEntity;
import com.ruoyi.system.mapper.systemconfig.SystemConfigMapper;
import com.ruoyi.system.service.systemconfig.SystemConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 系统配置表 Service
 *
 * <AUTHOR>
 * @date 2023-7-4 14:48:25
 */
@Service
public class SystemConfigServiceImpl implements SystemConfigService {

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Override
    public Boolean insert(SystemConfigEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return systemConfigMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return systemConfigMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(SystemConfigEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return systemConfigMapper.updateById(entity) > 0;
    }

    @Override
    public SystemConfigEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return systemConfigMapper.selectById(id);
    }

    @Override
    public List<SystemConfigEntity> selectListByConfigKeys(List<String> keys) {
        if(CollectionUtils.isEmpty(keys)){
            return Collections.emptyList();
        }
        return systemConfigMapper.selectListByConfigKeys(keys);
    }

    @Override
    public boolean updateByConfigKey(SystemConfigEntity config) {
        return systemConfigMapper.updateByConfigKey(config) > 0;
    }

    @Override
    public SystemConfigEntity selectByConfigKey(String configKey) {
        if(Objects.isNull(configKey)){
            return null;
        }
        return systemConfigMapper.selectByConfigKey(configKey);
    }


}
