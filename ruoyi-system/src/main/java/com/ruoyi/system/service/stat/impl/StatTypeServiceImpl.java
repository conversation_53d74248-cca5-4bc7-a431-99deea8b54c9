package com.ruoyi.system.service.stat.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.common.exception.BizRuntimeException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.stat.StatTypeMapper;
import com.ruoyi.system.entity.stat.StatTypeEntity;
import com.ruoyi.system.service.stat.StatTypeService;

/**
 * 埋点数值标识Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class StatTypeServiceImpl implements StatTypeService {
    @Autowired
    private StatTypeMapper statTypeMapper;

    /**
     * 查询埋点数值标识
     *
     * @param id 埋点数值标识主键
     * @return 埋点数值标识
     */
    @Override
    public StatTypeEntity selectStatTypeById(Integer id) {
        return statTypeMapper.selectStatTypeById(id);
    }

    /**
     * 查询埋点数值标识列表
     *
     * @param statType 埋点数值标识
     * @return 埋点数值标识
     */
    @Override
    public List<StatTypeEntity> selectStatTypeList(StatTypeEntity statType) {
        return statTypeMapper.selectStatTypeList(statType);
    }

    /**
     * 新增埋点数值标识
     *
     * @param statType 埋点数值标识
     * @return 结果
     */
    @Override
    public int insertStatType(StatTypeEntity statType) {

        return statTypeMapper.insertStatType(statType);
    }

    /**
     * 修改埋点数值标识
     *
     * @param statType 埋点数值标识
     * @return 结果
     */
    @Override
    public int updateStatType(StatTypeEntity statType) {
        return statTypeMapper.updateStatType(statType);
    }

    /**
     * 批量删除埋点数值标识
     *
     * @param ids 需要删除的埋点数值标识主键
     * @return 结果
     */
    @Override
    public int deleteStatTypeByIds(Integer[] ids) {
        return statTypeMapper.deleteStatTypeByIds(ids);
    }

    /**
     * 删除埋点数值标识信息
     *
     * @param id 埋点数值标识主键
     * @return 结果
     */
    @Override
    public int deleteStatTypeById(Integer id) {
        return statTypeMapper.deleteStatTypeById(id);
    }

    @Override
    public Integer selectMaxIdByLevel(Integer level) {
        if(NumberUtils.isNullOrLteZero(level)){
            throw new BizRuntimeException("无效参数");
        }
        return statTypeMapper.selectMaxIdByLevel(level);
    }

    @Override
    public Map<Integer, String> selectTypeRemarkByTypes(List<Integer> ids, Integer typeLevel) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyMap();
        }
        List<StatTypeEntity> entities = statTypeMapper.selectListByTypeIds(ids, typeLevel);
        return entities.stream().collect(Collectors.toMap(StatTypeEntity::getTypeId,StatTypeEntity::getRemark,(v1,v2)->v1));
    }

    @Override
    public StatTypeEntity selectByRemark(String remark) {
        if(StringUtils.isEmpty(remark)){
            return null;
        }
        return statTypeMapper.selectByRemark(remark);
    }
}
