package com.ruoyi.system.service.coupon;

import java.util.List;
import com.ruoyi.system.entity.coupon.AgentCouponUserRecordEntity;

/**
 * 短剧代理商用户权益Service接口
 *
 * <AUTHOR>
 */
public interface AgentCouponUserRecordService {

    /**
     * 查询短剧代理商用户权益
     *
     * @param id 短剧代理商用户权益主键
     * @return 短剧代理商用户权益
     */
    AgentCouponUserRecordEntity selectAgentCouponUserRecordById(Long id);

    /**
     * 查询短剧代理商用户权益
     *
     * @param couponNo 用户权益唯一标识
     * @return 短剧代理商用户权益
     */
    AgentCouponUserRecordEntity selectByCouponNo(String couponNo);

    /**
     * 判断是否存在记录
     */
    boolean existByAgentIdAndRequestId(String agentId, String requestId);

    /**
     * 查询短剧代理商用户权益列表
     *
     * @param param 短剧代理商用户权益
     * @return 短剧代理商用户权益集合
     */
    List<AgentCouponUserRecordEntity> selectList(AgentCouponUserRecordEntity param);

    /**
     * 新增短剧代理商用户权益
     *
     * @param record 短剧代理商用户权益
     * @return 结果
     */
    int insert(AgentCouponUserRecordEntity record);

    /**
     * 修改短剧代理商用户权益
     *
     * @param record 短剧代理商用户权益
     * @return 结果
     */
    int update(AgentCouponUserRecordEntity record);
}
