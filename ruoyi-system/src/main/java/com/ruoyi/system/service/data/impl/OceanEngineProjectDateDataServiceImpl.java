package com.ruoyi.system.service.data.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.shortplay.tv.ShortplayTvEntity;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserMapper;
import com.ruoyi.system.mapper.shortplay.tv.ShortplayTvMapper;
import com.ruoyi.system.vo.playlet.roiall.ProjectQueryParam;
import com.ruoyi.system.vo.playlet.roiall.ProjectQueryParamDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.data.OceanEngineProjectDateDataMapper;
import com.ruoyi.system.entity.data.OceanEngineProjectDateDataEntity;
import com.ruoyi.system.service.data.OceanEngineProjectDateDataService;

/**
 * 巨量广告账号项目维度日数据Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanEngineProjectDateDataServiceImpl implements OceanEngineProjectDateDataService {
    @Autowired
    private OceanEngineProjectDateDataMapper oceanEngineProjectDateDataMapper;
    private final HashSet<String> dimensionSet = new HashSet<>();
    @Autowired
    private ShortplayTvMapper shortplayTvMapper;
    @Autowired
    private AgtAccountJuliangAdvertiserMapper agtAccountJuliangAdvertiserMapper;


    /**
     * 查询巨量广告账号项目维度日数据
     *
     * @param id 巨量广告账号项目维度日数据主键
     * @return 巨量广告账号项目维度日数据
     */
    @Override
    public OceanEngineProjectDateDataEntity selectOceanEngineProjectDateDataById(Long id) {
        return oceanEngineProjectDateDataMapper.selectOceanEngineProjectDateDataById(id);
    }

    /**
     * 查询巨量广告账号项目维度日数据列表
     *
     * @param oceanEngineProjectDateData 巨量广告账号项目维度日数据
     * @return 巨量广告账号项目维度日数据
     */
    @Override
    public List<OceanEngineProjectDateDataEntity> selectOceanEngineProjectDateDataList(OceanEngineProjectDateDataEntity oceanEngineProjectDateData) {
        return oceanEngineProjectDateDataMapper.selectOceanEngineProjectDateDataList(oceanEngineProjectDateData);
    }

    /**
     * 新增巨量广告账号项目维度日数据
     *
     * @param oceanEngineProjectDateData 巨量广告账号项目维度日数据
     * @return 结果
     */
    @Override
    public int insertOceanEngineProjectDateData(OceanEngineProjectDateDataEntity oceanEngineProjectDateData) {

        return oceanEngineProjectDateDataMapper.insertOceanEngineProjectDateData(oceanEngineProjectDateData);
    }

    /**
     * 修改巨量广告账号项目维度日数据
     *
     * @param oceanEngineProjectDateData 巨量广告账号项目维度日数据
     * @return 结果
     */
    @Override
    public int updateOceanEngineProjectDateData(OceanEngineProjectDateDataEntity oceanEngineProjectDateData) {
        return oceanEngineProjectDateDataMapper.updateOceanEngineProjectDateData(oceanEngineProjectDateData);
    }

    /**
     * 批量删除巨量广告账号项目维度日数据
     *
     * @param ids 需要删除的巨量广告账号项目维度日数据主键
     * @return 结果
     */
    @Override
    public int deleteOceanEngineProjectDateDataByIds(Long[] ids) {
        return oceanEngineProjectDateDataMapper.deleteOceanEngineProjectDateDataByIds(ids);
    }

    /**
     * 删除巨量广告账号项目维度日数据信息
     *
     * @param id 巨量广告账号项目维度日数据主键
     * @return 结果
     */
    @Override
    public int deleteOceanEngineProjectDateDataById(Long id) {
        return oceanEngineProjectDateDataMapper.deleteOceanEngineProjectDateDataById(id);
    }

    @Override
    public List<OceanEngineProjectDateDataEntity> dataDetail(ProjectQueryParam queryParam, boolean isPage) {
        List<String> convertedDimension = convertDimension(queryParam.getDimension());
        queryParam.setDimension(convertedDimension);

        List<String> tvIdList = new ArrayList<>();
        // 根据短剧名查询 短剧 id
        String filterTvName = queryParam.getFilterTvName();
        if (StringUtils.isNotEmpty(filterTvName)) {
            List<ShortplayTvEntity> shortplayTvEntities = shortplayTvMapper.selectByTvName(filterTvName);
            if (CollectionUtils.isNotEmpty(shortplayTvEntities)) {
                List<String> tbIds = shortplayTvEntities.stream().map(ShortplayTvEntity::getTbId).collect(Collectors.toList());
                tvIdList.addAll(tbIds);
            }
        }

        // 是否进行分页，如果是查询接口，那就分页，如果是导出接口，那就不分页
        if (isPage) {
            TableSupport.startPage();
        }

        ProjectQueryParamDto dto = new ProjectQueryParamDto();
        BeanUtil.copyProperties(queryParam, dto);
        dto.setTvIdList(tvIdList);

        List<OceanEngineProjectDateDataEntity> dataEntities = oceanEngineProjectDateDataMapper.dataDetail(dto);
        if (CollectionUtils.isEmpty(dataEntities)) {
            return new ArrayList<>();
        }

        return dataEntities;
    }

    /**
     * 直接计算会导致比率之和不为 100%。所以计算前几个的比率，最后一个人用 100% - 前几个的比率之和。
     *
     * @param startDate
     * @param endDate
     * @param miniappIds
     * @param dimension
     * @return
     */
    @Override
    public Map<String, BigDecimal> calculateIncomeRate(String startDate, String endDate, List<String> miniappIds, List<String> dimension) {
        // 根据起始和结束日期计算每天的每个小程序的每个投手的投手对应小程序24小时变现金额
        List<OceanEngineProjectDateDataEntity> dataEntities;
        if (dimension.contains("d.cur_date")) {
            dataEntities = oceanEngineProjectDateDataMapper.selectUser24hAmount(startDate, endDate, miniappIds);
        } else {
            dataEntities = oceanEngineProjectDateDataMapper.selectUser24hAmountNoDate(startDate, endDate, miniappIds);
        }
        if (CollectionUtils.isEmpty(dataEntities)) {
            return new HashMap<>();
        }
        // convert to map<cur_data+app_id,list<user_nickname,stat_attribution_micro_game_24h_amount>>
        Map<String, List<OceanEngineProjectDateDataEntity>> dateAppIdMap = dataEntities.stream().collect(Collectors.groupingBy(e -> DateUtil.formatDate(e.getCurDate()) + "_" + e.getAppId()));

        Map<String, BigDecimal> resultMap = new HashMap<>();
        dateAppIdMap.forEach((key, value) -> {
            BigDecimal sum = value.stream().map(OceanEngineProjectDateDataEntity::getStatAttributionMicroGame24hAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal rateSum = BigDecimal.ZERO;

            for (int i = 0; i < value.size(); i++) {
                OceanEngineProjectDateDataEntity e = value.get(i);
                if (i == value.size() - 1) {
                    resultMap.put(DateUtil.formatDate(e.getCurDate()) + "_" + e.getAppId() + "_" + e.getUserNickname(), BigDecimal.ONE.subtract(rateSum));
                    continue;
                }

                if (sum.compareTo(BigDecimal.ZERO) == 0) {
                    resultMap.put(DateUtil.formatDate(e.getCurDate()) + "_" + e.getAppId() + "_" + e.getUserNickname(), BigDecimal.ZERO);
                } else {
                    BigDecimal rate = e.getStatAttributionMicroGame24hAmount().divide(sum, 4, RoundingMode.HALF_UP);
                    resultMap.put(DateUtil.formatDate(e.getCurDate()) + "_" + e.getAppId() + "_" + e.getUserNickname(), rate);
                    rateSum = rateSum.add(rate);
                }
            }

        });

        return resultMap;

    }

    /**
     * 校验，并转化维度字段，将驼峰转化为下划线
     *
     * @param dimension
     * @return
     */
    private List<String> convertDimension(List<String> dimension) {
        ArrayList<String> convertedList = new ArrayList<>();
        // 默认是日期维度。
        if (CollectionUtils.isEmpty(dimension)) {
            return convertedList;
        }
        if (dimensionSet.isEmpty()) {
            dimensionSet.add("cur_date");
            dimensionSet.add("account_id");
            dimensionSet.add("account_name_co");
            dimensionSet.add("account_agent");
            dimensionSet.add("advertiser_id");
            dimensionSet.add("advertiser_name");
            dimensionSet.add("project_name");
            dimensionSet.add("user_nickname");
            dimensionSet.add("app_id");
            dimensionSet.add("project_id");
            dimensionSet.add("tv_id");
        }
        for (String dim : dimension) {
            if (!dimensionSet.contains(StrUtil.toUnderlineCase(dim))) {
                throw new ServiceException("维度字段不合法" + dim);
            }
            convertedList.add(StrUtil.toUnderlineCase(dim));
        }
        return convertedList;
    }
}
