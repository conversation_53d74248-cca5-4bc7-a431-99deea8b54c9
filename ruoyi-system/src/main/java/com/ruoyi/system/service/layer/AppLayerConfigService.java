package com.ruoyi.system.service.layer;


import com.ruoyi.system.entity.layer.AppLayerConfigEntity;
import com.ruoyi.system.req.playlet.app.LayerReq;

import java.util.List;

/**
 * app弹窗配置表 Service
 *
 * <AUTHOR>
 * @date 2023-10-23 13:56:05
 */
public interface AppLayerConfigService {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean insert(AppLayerConfigEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    Boolean updateById(AppLayerConfigEntity entity);

    /**
     * 根据状态获取最大排序
     * @param status
     * @return
     */
    int selectMaxSortByStatus(Integer status);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    AppLayerConfigEntity selectById(Long id);

    /**
     * 根据条件查询列表
     * @param req
     * @return
     */
    List<AppLayerConfigEntity> selectListByReq(LayerReq req);

    /**
     * 查询所有
     * @return
     */
    List<AppLayerConfigEntity> selectAll();

    /**
     * 批量更新排序
     * @param updateSortList
     * @return
     */
    boolean batchUpdateSort(List<AppLayerConfigEntity> updateSortList);
}
