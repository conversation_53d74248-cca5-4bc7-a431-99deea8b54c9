package com.ruoyi.system.service.chumo.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.entity.chumo.AgtDataSupplierSnapshotChumoEntity;
import com.ruoyi.system.entity.chumo.ChumoOrderResp;
import com.ruoyi.system.entity.chumo.ChumoTokenResp;
import com.ruoyi.system.entity.fundStat.SupplierIncomeItem;
import com.ruoyi.system.entity.supplier.SupplierEnum;
import com.ruoyi.system.mapper.chumo.AgtDataSupplierSnapshotChumoSelfMapper;
import com.ruoyi.system.service.SupplierService;
import com.ruoyi.system.service.chumo.AgtDataSupplierSnapshotChumoSelfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @author: keboom
 * @date: 2024/7/9
 */
@Slf4j
@Service
public class AgtDataSupplierSnapshotChumoSelfServiceImpl implements AgtDataSupplierSnapshotChumoSelfService, SupplierService {

    @Value("${chumo.secret_key}")
    private String secretKey;
    @Value("${chumo.host}")
    private String host;
    @Autowired
    private RedisCache redisCache;
    // 触摸获取订单每页的大小
    private static final int pageSize = 100;
    @Autowired
    private AgtDataSupplierSnapshotChumoSelfMapper chumoSelfMapper;

    /**
     * 获取触摸订单信息存到快照表
     *
     * @param startTime
     * @param endTime
     */
    @Async
    @Override
    public void getOrderInfoSaveToDB(Date startTime, Date endTime) {
        log.info("获取触摸订单信息存到快照表 start: {} end: {}", startTime, endTime);
        // 获得 token，token 缓存，token 两小时过期
        // 1. 获取 token
        String chuMoToken = getChuMoToken();
        if (StringUtils.isEmpty(chuMoToken)) {
            log.error("获取触摸 token 失败. start: {} end: {}", startTime, endTime);
            return;
        }

        // 2. 获取订单信息 然后入库

        // 此请求只是为了获得 pageNum
        ChumoOrderResp orderList = getOrderList(startTime, endTime, chuMoToken, 1, 1);
        if (orderList == null || orderList.getData() == null || orderList.getData().getTotal() == 0) {
            log.info("触摸订单信息为空 start: {} end: {} order:{}", startTime, endTime, orderList);
            return;
        }
        // 下面正式查询订单，然后入库
        int page = 1;
        // total 总共有几条订单
        int total = orderList.getData().getTotal();
        // 向上取整
        int pageNum = NumberUtil.ceilDiv(total, pageSize);
        log.trace("触摸订单信息 start:{} end:{} 总数: {} 页数: {}", startTime, endTime, total, pageNum);
        while (page <= pageNum) {
            ChumoOrderResp orderList1 = getOrderList(startTime, endTime, chuMoToken, page, pageSize);
            if (orderList1 == null || orderList1.getData() == null || orderList1.getData().getTotal() == 0) {
                log.info("触摸订单信息为空 start: {} end: {} order:{}", startTime, endTime, orderList1);
                return;
            }
            // 入库
            saveToDB(orderList1);
            page++;
        }
    }

    @Override
    public HashMap<String, SupplierIncomeItem> getDateSuppItemMap(String startTime, String endTime) {
        HashMap<String, SupplierIncomeItem> dateSuppItemMap = new HashMap<>();
        List<AgtDataSupplierSnapshotChumoEntity> entities = chumoSelfMapper.selectIncomeByDay(startTime, endTime);
        for (AgtDataSupplierSnapshotChumoEntity entity : entities) {
            SupplierIncomeItem item = new SupplierIncomeItem();
            item.setAmount(NumberUtil.div(BigDecimal.valueOf(entity.getTotalFee()), 100, 2, RoundingMode.HALF_UP));
            item.setSupplier(SupplierEnum.CHUMO.getName());
            dateSuppItemMap.put(DateUtil.formatDate(entity.getCurDate()), item);
        }
        return dateSuppItemMap;
    }

    private void saveToDB(ChumoOrderResp orderList) {
        ArrayList<AgtDataSupplierSnapshotChumoEntity> chumoEntities = new ArrayList<>(100);
        for (ChumoOrderResp.ChumoOrderData.Item item : orderList.getData().getList()) {
            if (StringUtils.isEmpty(item.getAdvertiserId())) {
                log.error("触摸订单信息入库: advertiserId 为空. orderInfo: {}", item.toString());
                continue;
            }
            AgtDataSupplierSnapshotChumoEntity entity = new AgtDataSupplierSnapshotChumoEntity();
            entity.setAdvertiserId(Long.parseLong(item.getAdvertiserId()));
            entity.setOrderPayType(getOrderPayType(item.getOutTradeNo()));
            entity.setCurDate(iosToDate(item.getCreatedAt()));
            entity.setCurHour((long) DateUtil.hour(iosToDate(item.getCreatedAt()), true));
            entity.setUserId(item.getUserId());
            entity.setOpenid(item.getOpenid());
            entity.setOutTradeNo(item.getOutTradeNo());
            entity.setTotalFee((long) item.getTotalFee());
            entity.setState((long) item.getState());
            entity.setCreatedAt(iosToDate(item.getCreatedAt()));
            entity.setUpdatedAt(iosToDate(item.getUpdatedAt()));
            entity.setPayTime(iosToDate(item.getPayTime()));
            entity.setCallbackTime(iosToDate(item.getCallbackTime()));
            entity.setPayType((long) item.getPayType());
            entity.setTradeNo(item.getTradeNo());
            entity.setDescription(item.getDesc());
            entity.setCmZdyId(item.getCmZdyId());
            entity.setType((long) item.getType());
            entity.setNum((long) item.getNum());
            entity.setEpisodeId((long) item.getEpisodeId());
            entity.setProductId((long) item.getProductId());
            entity.setPayTimes((long) item.getPayTimes());
            entity.setVideoId((long) item.getVideoId());
            entity.setIpAddr(item.getIpAddr());
            entity.setAppid(item.getAppid());
            entity.setAppType((long) item.getAppType());
            entity.setUploadState((long) item.getUploadState());
            entity.setPlatformPromotionId(item.getPlatformPromotionId());
            entity.setPromotionId(item.getPromotionId());
            entity.setSysUserId((long) item.getSysUserId());
            entity.setVideoName(item.getVideoName());
            entity.setUserCreatedAt(iosToDate(item.getUserCreatedAt()));

            chumoEntities.add(entity);
        }
        log.trace("触摸订单信息入库: {}", chumoEntities.size());
        chumoSelfMapper.saveOrUpdateBatch(chumoEntities);
    }

    /**
     * wxvp_04172049789095244066
     * 订单编号  wxvp_:微信安卓支付     shl_,wx_:微信Ios支付   tt_:抖音支
     * 默认 0,微信安卓 1，微信 ios2，抖音 3
     *
     * @param outTradeNo
     * @return
     */
    private Long getOrderPayType(String outTradeNo) {
        if (outTradeNo.startsWith("wxvp_")) {
            return 1L;
        } else if (outTradeNo.startsWith("shl_") || outTradeNo.startsWith("wx_")) {
            return 2L;
        } else if (outTradeNo.startsWith("tt_")) {
            return 3L;
        }
        return 0L;
    }

    private Date iosToDate(String iosDate) {
        return Date.from(ZonedDateTime.parse(iosDate).toInstant());
    }

    private ChumoOrderResp getOrderList(Date startTime, Date endTime, String chuMoToken, int page, int size) {
        JSONObject body = new JSONObject();
        body.put("start_date", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime));
        body.put("end_date", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));
        // 0 未支付 1 已支付
        body.put("state", 1);
        body.put("page", page);
        body.put("size", size);
        String resp = HttpUtil.createPost(host + "/open/V1/getOrderList")
                .header("Authorization", "Bearer " + chuMoToken)
                .body(body.toString(), ContentType.JSON.toString()).execute().body();
        ChumoOrderResp chumoOrderResp = JSONObject.parseObject(resp, ChumoOrderResp.class);
        return chumoOrderResp;
    }

    private String getChuMoToken() {
        String key = RedisKeyFactory.K043.join("chumo_token");
        String token = redisCache.getCacheObject(key);
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        StringBuilder param = new StringBuilder();
        param.append("secret_key=").append(secretKey);
        String response = HttpUtils.sendGet(host + "/open/V1/getAuthToken", param.toString());
        ChumoTokenResp chumoTokenResp = JSONObject.parseObject(response, ChumoTokenResp.class);
        if (chumoTokenResp == null) {
            log.error("获取触摸 token 失败.");
            return "";
        }
        log.info("获取触摸 token: {}", chumoTokenResp.toString());
        if (chumoTokenResp.getCode() == 0) {
            String authToken = chumoTokenResp.getData().getAuthToken();
            redisCache.setCacheObject(key, authToken, 1, TimeUnit.HOURS);
            return authToken;
        }
        return "";
    }
}
