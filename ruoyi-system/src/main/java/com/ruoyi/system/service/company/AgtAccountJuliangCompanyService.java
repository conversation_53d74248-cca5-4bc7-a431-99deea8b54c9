package com.ruoyi.system.service.company;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity;

/**
 * 巨量组织与主体关系Service接口
 *
 * <AUTHOR>
 */
public interface AgtAccountJuliangCompanyService {
    /**
     * 查询巨量组织与主体关系
     *
     * @param id 巨量组织与主体关系主键
     * @return 巨量组织与主体关系
     */
    public AgtAccountJuliangCompanyEntity selectAgtAccountJuliangCompanyById(Integer id);

    /**
     * 查询巨量组织与主体关系列表
     *
     * @param agtAccountJuliangCompany 巨量组织与主体关系
     * @return 巨量组织与主体关系集合
     */
    public List<AgtAccountJuliangCompanyEntity> selectAgtAccountJuliangCompanyList(AgtAccountJuliangCompanyEntity agtAccountJuliangCompany);

    /**
     * 新增巨量组织与主体关系
     *
     * @param agtAccountJuliangCompany 巨量组织与主体关系
     * @return 结果
     */
    public int insertAgtAccountJuliangCompany(AgtAccountJuliangCompanyEntity agtAccountJuliangCompany);

    /**
     * 修改巨量组织与主体关系
     *
     * @param agtAccountJuliangCompany 巨量组织与主体关系
     * @return 结果
     */
    public int updateAgtAccountJuliangCompany(AgtAccountJuliangCompanyEntity agtAccountJuliangCompany);

    /**
     * 批量删除巨量组织与主体关系
     *
     * @param ids 需要删除的巨量组织与主体关系主键集合
     * @return 结果
     */
    public int deleteAgtAccountJuliangCompanyByIds(Integer[] ids);

    /**
     * 删除巨量组织与主体关系信息
     *
     * @param id 巨量组织与主体关系主键
     * @return 结果
     */
    public int deleteAgtAccountJuliangCompanyById(Integer id);

    /**
     * 查询账户主体数量
     * @return
     */
    Map<Long, Integer> queryAccountCompanyNum();
}
