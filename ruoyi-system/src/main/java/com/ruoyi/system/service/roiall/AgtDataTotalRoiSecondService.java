package com.ruoyi.system.service.roiall;

import com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity;

import java.text.ParseException;
import java.util.Date;
import java.util.List;


/**
 * 数据清洗：准备工作
 * 1.拉取巨量源数据
 * 2.拉取供剧商：友和数据
 * 数据清洗：
 * 1.拉取友和数据
 * 2.拉取友和的分成配置
 * 3.计算分成，入库
 */
public interface AgtDataTotalRoiSecondService {

    /**
     * 数据清洗：1.友和
     *
     * @param dateReq
     * @param hourReq
     * @return
     */
    List<AgtDataTotalRoiEntity> youheData(Date dateReq, String hourReq);

    /**
     * 美光数据清洗
     *
     * @param date 日期
     * @param hour 小时，为空则为当天
     * @return 清洗后的数据
     */
    void meiguangData(Date date, Integer hour);

    List<AgtDataTotalRoiEntity> chumoData(Date dateReq, String hourReq);

    void lianfanData(Date dateReq, String hourReq);

    void bingwuData(Date dateReq, String hourReq);

    void fanqieData(Date dateReq, String hourReq);

    void dianZhongData(Date dateReq, String hourReq);

    void nuoheData(Date dateReq, String hourReq);

    void liuyiData(Date dateReq, String hourReq);

    List<AgtDataTotalRoiEntity> calculateRoi(Date dateReq, String hourReq);
}
