package com.ruoyi.system.service.douyin.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.shortplay.app.ShortplayAppEntity;
import com.ruoyi.system.service.pangolin.PangolinService;
import com.ruoyi.system.service.shortplay.abspub.ShortplayIaaEcpmService;
import com.ruoyi.system.service.shortplay.abspub.impl.ShortplayIaaEcpmServiceImpl;
import com.ruoyi.system.service.shortplay.app.ShortplayAppService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.douyin.DouyinActiveUserNumMapper;
import com.ruoyi.system.entity.douyin.DouyinActiveUserNumEntity;
import com.ruoyi.system.service.douyin.DouyinActiveUserNumService;

/**
 * 抖音活跃用户Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DouyinActiveUserNumServiceImpl implements DouyinActiveUserNumService {
    @Autowired
    private DouyinActiveUserNumMapper douyinActiveUserNumMapper;
    @Autowired
    private ShortplayAppService shortplayAppService;
    @Autowired
    private PangolinService pangolinService;
    @Autowired
    private ShortplayIaaEcpmServiceImpl shortplayIaaEcpmServiceimpl;

    /**
     * 查询抖音活跃用户
     *
     * @param id 抖音活跃用户主键
     * @return 抖音活跃用户
     */
    @Override
    public DouyinActiveUserNumEntity selectDouyinActiveUserNumById(Long id)
    {
        return douyinActiveUserNumMapper.selectDouyinActiveUserNumById(id);
    }

    /**
     * 查询抖音活跃用户列表
     *
     * @param douyinActiveUserNum 抖音活跃用户
     * @return 抖音活跃用户
     */
    @Override
    public List<DouyinActiveUserNumEntity> selectDouyinActiveUserNumList(DouyinActiveUserNumEntity douyinActiveUserNum)
    {
        return douyinActiveUserNumMapper.selectDouyinActiveUserNumList(douyinActiveUserNum);
    }

    /**
     * 新增抖音活跃用户
     *
     * @param douyinActiveUserNum 抖音活跃用户
     * @return 结果
     */
    @Override
    public int insertDouyinActiveUserNum(DouyinActiveUserNumEntity douyinActiveUserNum)
    {

            return douyinActiveUserNumMapper.insertDouyinActiveUserNum(douyinActiveUserNum);
    }

    /**
     * 修改抖音活跃用户
     *
     * @param douyinActiveUserNum 抖音活跃用户
     * @return 结果
     */
    @Override
    public int updateDouyinActiveUserNum(DouyinActiveUserNumEntity douyinActiveUserNum)
    {
        return douyinActiveUserNumMapper.updateDouyinActiveUserNum(douyinActiveUserNum);
    }

    /**
     * 批量删除抖音活跃用户
     *
     * @param ids 需要删除的抖音活跃用户主键
     * @return 结果
     */
    @Override
    public int deleteDouyinActiveUserNumByIds(Long[] ids)
    {
        return douyinActiveUserNumMapper.deleteDouyinActiveUserNumByIds(ids);
    }

    /**
     * 删除抖音活跃用户信息
     *
     * @param id 抖音活跃用户主键
     * @return 结果
     */
    @Override
    public int deleteDouyinActiveUserNumById(Long id)
    {
        return douyinActiveUserNumMapper.deleteDouyinActiveUserNumById(id);
    }

    @Override
    public void syncHistoryDayActiveUserNum() {
        List<ShortplayAppEntity> dyAppList = shortplayAppService.selectShortplayAppList(null).stream()
                .filter(s -> Objects.equals(s.getAppplatform(), "tt") && !StringUtils.containsAny(s.getAppname(), "废弃")).collect(Collectors.toList());
        for (ShortplayAppEntity shortplayAppEntity : dyAppList) {
            getHistoryDayActiveUserNum(DateUtils.minusDays(new Date(), 1), shortplayAppEntity.getAppid(), StringUtils.isNotBlank(shortplayAppEntity.getComponentAppid()));
        }
    }

    @Override
    public void fixHistoryDayActiveUserNum(String time) {
        try {
            log.info("fixHistoryDayActiveUserNum,time={}", time);
            List<ShortplayAppEntity> dyAppList = shortplayAppService.selectShortplayAppList(null).stream()
                    .filter(s -> Objects.equals(s.getAppplatform(), "tt") && !StringUtils.containsAny(s.getAppname(), "废弃")).collect(Collectors.toList());
            for (ShortplayAppEntity shortplayAppEntity : dyAppList) {
                getHistoryDayActiveUserNum(DateUtils.parseDate(time, DateUtils.YYYY_MM_DD), shortplayAppEntity.getAppid(), StringUtils.isNotBlank(shortplayAppEntity.getComponentAppid()));
            }
        }catch (Exception e) {
            log.error("fixHistoryDayActiveUserNum error", e);
        }

    }

    @Override
    public void sycActiveDayActiveUserNum() {
        List<ShortplayAppEntity> dyAppList = shortplayAppService.selectShortplayAppList(null).stream()
                .filter(s -> Objects.equals(s.getAppplatform(), "tt") && !StringUtils.containsAny(s.getAppname(), "废弃")).collect(Collectors.toList());
        for (ShortplayAppEntity shortplayAppEntity : dyAppList) {
            getActiveDayActiveUserNum(shortplayAppEntity.getAppid(), StringUtils.isNotBlank(shortplayAppEntity.getComponentAppid()));
        }
    }

    @Override
    public List<DouyinActiveUserNumEntity> selectDouyinActiveUserByAppidAndDate(List<String> appidList, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(appidList) || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate) ) {
            return new ArrayList<>();
        }
        return douyinActiveUserNumMapper.selectDouyinActiveUserByAppidAndDate(appidList, startDate, endDate);

    }

    /**
     * 同步历史日活跃用户数
     * @param date
     * @return
     */
    public void getHistoryDayActiveUserNum(Date date, String appid, boolean isComponent) {
        if ( Objects.isNull(date) ) {
            return;
        }

        Date startOfDay = DateUtils.getStartOfDay(date);
        Date endOfDay = DateUtils.getEndOfDay(date);
        HashMap<String, Object> body = new HashMap<>();
        body.put("end_time", endOfDay.getTime()/1000);
        body.put("start_time", startOfDay.getTime()/1000);

        String resp = null;
        try {
            String accesstoken = isComponent ? pangolinService.getAuthorizerAccessToken(appid) : shortplayIaaEcpmServiceimpl.getAccessToken(appid);

            resp = HttpUtil.createGet("https://open.douyin.com/api/platform/v2/data_analysis/query_behavior_data/")
                    .header("access-token", accesstoken)
                    .form(body).execute().body();
            log.info("获取历史日活跃用户数: appid={}, body={}, resp={}", appid, JSONObject.toJSONString(body), resp);

            if ( !StringUtils.isEmpty(resp) ) {
                JSONObject jsonObject = JSONObject.parseObject(resp);
                int errNo = jsonObject.getIntValue("err_no");
                if ( Objects.equals(errNo, 0) ) {
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    if ( Objects.isNull(dataObject) || dataObject.isEmpty() ) {
                        return;
                    }
                    int dayUserNum = dataObject.getJSONObject("sum").getIntValue("active_user_num");
                    List<Integer> hourUserNum = dataObject
                            .getJSONArray("behaviors")
                            .stream()
                            .map(behavior -> ((JSONObject) behavior).getInteger("active_user_num"))
                            .collect(Collectors.toList());
                    DouyinActiveUserNumEntity douyinActiveUserNumEntity = new DouyinActiveUserNumEntity();
                    douyinActiveUserNumEntity.setDayUserNum(dayUserNum);
                    douyinActiveUserNumEntity.setHourUserNum(JSONObject.toJSONString(hourUserNum));
                    douyinActiveUserNumEntity.setCurDate(date);
                    douyinActiveUserNumEntity.setMpId(appid);
                    douyinActiveUserNumMapper.deleteDouyinActiveUserNumByAppidAndDate(appid, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date));
                    douyinActiveUserNumMapper.insertDouyinActiveUserNum(douyinActiveUserNumEntity);
                }else {
                    log.error("获取历史日活跃用户数失败: appid={}, body={}, resp={}", appid, JSONObject.toJSONString(body), resp);
                }
            }
        }catch (Exception e) {
            log.error("获取历史日活跃用户数失败: appid={}, body={}, resp={}", appid, JSONObject.toJSONString(body), resp, e);
        }

    }

    /**
     * 获取当日实时用户数
     * @param appid
     * @param isComponent
     */
    public void getActiveDayActiveUserNum(String appid, boolean isComponent){
        String resp = null;
        try {
            log.info("getActiveDayActiveUserNum 获取token appid={}, isComponent={}", appid,isComponent);
            String accesstoken = isComponent ? pangolinService.getAuthorizerAccessToken(appid) : shortplayIaaEcpmServiceimpl.getAccessToken(appid);
            log.info("getActiveDayActiveUserNum 获取token成功， accesstoken={}", accesstoken);
            log.info("getActiveDayActiveUserNum 开始请求数据");
            resp = HttpUtil.createGet("https://open.douyin.com/api/platform/v2/data_analysis/query_real_time_user_data/")
                    .header("access-token", accesstoken)
                    .execute().body();
            log.info("getActiveDayActiveUserNum 获取当日实时日活跃用户数: appid={}, resp={}", appid, resp);

            if ( !StringUtils.isEmpty(resp) ) {
                JSONObject jsonObject = JSONObject.parseObject(resp);
                int errNo = jsonObject.getIntValue("err_no");
                if ( Objects.equals(errNo, 0) ) {
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    if ( Objects.isNull(dataObject) || dataObject.isEmpty() ) {
                        return;
                    }
                    int dayUserNum = dataObject.getJSONObject("sum").getIntValue("active_user_num");
                    List<Integer> hourUserNum = dataObject
                            .getJSONArray("behaviors")
                            .stream()
                            .map(behavior -> ((JSONObject) behavior).getInteger("active_user_num"))
                            .collect(Collectors.toList());
                    DouyinActiveUserNumEntity douyinActiveUserNumEntity = new DouyinActiveUserNumEntity();
                    douyinActiveUserNumEntity.setDayUserNum(dayUserNum);
                    douyinActiveUserNumEntity.setHourUserNum(JSONObject.toJSONString(hourUserNum));
                    douyinActiveUserNumEntity.setCurDate(new Date());
                    douyinActiveUserNumEntity.setMpId(appid);
                    log.info("getActiveDayActiveUserNum 删除历史数据");
                    douyinActiveUserNumMapper.deleteDouyinActiveUserNumByAppidAndDate(appid, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date()));
                    log.info("getActiveDayActiveUserNum 插入新数据");
                    douyinActiveUserNumMapper.insertDouyinActiveUserNum(douyinActiveUserNumEntity);
                    log.info("getActiveDayActiveUserNum 插入新数据成功");
                }else {
                    log.error("获取当日实时日活跃用户数失败: appid={}, resp={}", appid, resp);
                }
            }
        }catch (Exception e) {
            log.error("获取当日实时日活跃用户数失败: appid={}, resp={}", appid, resp, e);
        }


    }


}
