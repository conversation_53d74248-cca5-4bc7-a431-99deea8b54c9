package com.ruoyi.system.service.aiaa.impl;

import com.ruoyi.system.entity.aiaa.UserBehaviorEntity;
import com.ruoyi.system.mapper.aiaa.UserBehaviorMapper;
import com.ruoyi.system.service.aiaa.UserBehaviorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户行为记录表 serviceImpl
 *
 * <AUTHOR> Yang
 * @Date 2023/8/15 18:19
 * @Description
 */
@Service
public class UserBehaviorServiceImpl implements UserBehaviorService {


    @Autowired
    private UserBehaviorMapper userBehaviorMapper;

    @Override
    public boolean insert(UserBehaviorEntity userBehaviorEntity) {
        return userBehaviorMapper.insert(userBehaviorEntity);
    }
}
