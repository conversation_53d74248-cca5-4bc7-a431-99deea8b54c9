package com.ruoyi.system.service.douyin;

import java.util.List;
import com.ruoyi.system.entity.douyin.DouyinActiveUserNumEntity;

/**
 * 抖音活跃用户Service接口
 *
 * <AUTHOR>
 */
public interface DouyinActiveUserNumService {
    /**
     * 查询抖音活跃用户
     *
     * @param id 抖音活跃用户主键
     * @return 抖音活跃用户
     */
    public DouyinActiveUserNumEntity selectDouyinActiveUserNumById(Long id);

    /**
     * 查询抖音活跃用户列表
     *
     * @param douyinActiveUserNum 抖音活跃用户
     * @return 抖音活跃用户集合
     */
    public List<DouyinActiveUserNumEntity> selectDouyinActiveUserNumList(DouyinActiveUserNumEntity douyinActiveUserNum);

    /**
     * 新增抖音活跃用户
     *
     * @param douyinActiveUserNum 抖音活跃用户
     * @return 结果
     */
    public int insertDouyinActiveUserNum(DouyinActiveUserNumEntity douyinActiveUserNum);

    /**
     * 修改抖音活跃用户
     *
     * @param douyinActiveUserNum 抖音活跃用户
     * @return 结果
     */
    public int updateDouyinActiveUserNum(DouyinActiveUserNumEntity douyinActiveUserNum);

    /**
     * 批量删除抖音活跃用户
     *
     * @param ids 需要删除的抖音活跃用户主键集合
     * @return 结果
     */
    public int deleteDouyinActiveUserNumByIds(Long[] ids);

    /**
     * 删除抖音活跃用户信息
     *
     * @param id 抖音活跃用户主键
     * @return 结果
     */
    public int deleteDouyinActiveUserNumById(Long id);

    /**
     * 同步历史抖音活跃用户
     */
    void syncHistoryDayActiveUserNum();

    /**
     * 修改指定日期抖音活跃用户数据
     * @param time
     */
    void fixHistoryDayActiveUserNum(String time);

    /**
     * 同步今日抖音实时活跃用户数
     */
    void sycActiveDayActiveUserNum();

    /**
     * 根据appid和日期获取抖音活跃用户数量
     * @param appidList
     * @param startDate
     * @param endDate
     */
    List<DouyinActiveUserNumEntity> selectDouyinActiveUserByAppidAndDate(List<String> appidList, String startDate, String endDate);
}
