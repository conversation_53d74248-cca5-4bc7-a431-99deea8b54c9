package com.ruoyi.system.service.hotcake;

import java.util.Date;
import java.util.List;
import com.ruoyi.system.entity.hotcake.HotcakeShareEntity;

/**
 * 分享裂变Service接口
 *
 * <AUTHOR>
 */
public interface HotcakeShareService {
    /**
     * 查询分享裂变
     *
     * @param id 分享裂变主键
     * @return 分享裂变
     */
    public HotcakeShareEntity selectHotcakeShareById(Long id);

    /**
     * 查询分享裂变列表
     *
     * @param hotcakeShare 分享裂变
     * @return 分享裂变集合
     */
    public List<HotcakeShareEntity> selectHotcakeShareList(HotcakeShareEntity hotcakeShare);

    /**
     * 新增分享裂变
     *
     * @param hotcakeShare 分享裂变
     * @return 结果
     */
    public int insertHotcakeShare(HotcakeShareEntity hotcakeShare);

    /**
     * 修改分享裂变
     *
     * @param hotcakeShare 分享裂变
     * @return 结果
     */
    public int updateHotcakeShare(HotcakeShareEntity hotcakeShare);

    /**
     * 批量删除分享裂变
     *
     * @param ids 需要删除的分享裂变主键集合
     * @return 结果
     */
    public int deleteHotcakeShareByIds(Long[] ids);

    /**
     * 删除分享裂变信息
     *
     * @param id 分享裂变主键
     * @return 结果
     */
    public int deleteHotcakeShareById(Long id);

    /**
     * 查询用户分享邀请人数
     * @param userId
     * @param date
     * @return
     */
    int countByUserIdAndDate(Long userId, Date date);
}
