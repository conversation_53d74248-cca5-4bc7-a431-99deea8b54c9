package com.ruoyi.system.service.dyAdData.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.dyAdData.ShortplayDyAdDataMapper;
import com.ruoyi.system.entity.dyAdData.ShortplayDyAdDataEntity;
import com.ruoyi.system.service.dyAdData.ShortplayDyAdDataService;

/**
 * 抖音短剧小程序广告埋点Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ShortplayDyAdDataServiceImpl implements ShortplayDyAdDataService {
    @Autowired
    private ShortplayDyAdDataMapper shortplayDyAdDataMapper;

    /**
     * 查询抖音短剧小程序广告埋点
     *
     * @param id 抖音短剧小程序广告埋点主键
     * @return 抖音短剧小程序广告埋点
     */
    @Override
    public ShortplayDyAdDataEntity selectShortplayDyAdDataById(Long id)
    {
        return shortplayDyAdDataMapper.selectShortplayDyAdDataById(id);
    }

    /**
     * 查询抖音短剧小程序广告埋点列表
     *
     * @param shortplayDyAdData 抖音短剧小程序广告埋点
     * @return 抖音短剧小程序广告埋点
     */
    @Override
    public List<ShortplayDyAdDataEntity> selectShortplayDyAdDataList(ShortplayDyAdDataEntity shortplayDyAdData)
    {
        return shortplayDyAdDataMapper.selectShortplayDyAdDataList(shortplayDyAdData);
    }

    /**
     * 新增抖音短剧小程序广告埋点
     *
     * @param shortplayDyAdData 抖音短剧小程序广告埋点
     * @return 结果
     */
    @Override
    public int insertShortplayDyAdData(ShortplayDyAdDataEntity shortplayDyAdData)
    {

            return shortplayDyAdDataMapper.insertShortplayDyAdData(shortplayDyAdData);
    }

    /**
     * 修改抖音短剧小程序广告埋点
     *
     * @param shortplayDyAdData 抖音短剧小程序广告埋点
     * @return 结果
     */
    @Override
    public int updateShortplayDyAdData(ShortplayDyAdDataEntity shortplayDyAdData)
    {
        return shortplayDyAdDataMapper.updateShortplayDyAdData(shortplayDyAdData);
    }

    /**
     * 批量删除抖音短剧小程序广告埋点
     *
     * @param ids 需要删除的抖音短剧小程序广告埋点主键
     * @return 结果
     */
    @Override
    public int deleteShortplayDyAdDataByIds(Long[] ids)
    {
        return shortplayDyAdDataMapper.deleteShortplayDyAdDataByIds(ids);
    }

    /**
     * 删除抖音短剧小程序广告埋点信息
     *
     * @param id 抖音短剧小程序广告埋点主键
     * @return 结果
     */
    @Override
    public int deleteShortplayDyAdDataById(Long id)
    {
        return shortplayDyAdDataMapper.deleteShortplayDyAdDataById(id);
    }

    @Override
    public Long selectIdBy(Date curDate, String appId, Long fTvId, Integer series, Integer times) {
        return shortplayDyAdDataMapper.selectIdBy(curDate, appId, fTvId, series, times);
    }

    @Override
    public void addExposurePV(Long id) {
        if (id == null) {
            return;
        }
        shortplayDyAdDataMapper.addExposurePV(id);
    }

    @Override
    public void addWatchPv(Long id) {
        if (id == null) {
            return;
        }
        shortplayDyAdDataMapper.addWatchPv(id);
    }
}
