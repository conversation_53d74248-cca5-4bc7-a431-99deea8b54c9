package com.ruoyi.system.service.douyin.impl;

import java.util.List;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.douyin.TbJlAccountMapper;
import com.ruoyi.system.entity.douyin.TbJlAccount;
import com.ruoyi.system.service.douyin.ITbJlAccountService;

/**
 * 巨量主体Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-03
 */
@Service
public class TbJlAccountServiceImpl implements ITbJlAccountService 
{
    @Autowired
    private TbJlAccountMapper tbJlAccountMapper;

    /**
     * 查询巨量主体
     * 
     * @param id 巨量主体主键
     * @return 巨量主体
     */
    @Override
    public TbJlAccount selectTbJlAccountById(String id)
    {
        return tbJlAccountMapper.selectTbJlAccountById(id);
    }

    /**
     * 查询巨量主体列表
     * 
     * @param tbJlAccount 巨量主体
     * @return 巨量主体
     */
    @Override
    public List<TbJlAccount> selectTbJlAccountList(TbJlAccount tbJlAccount)
    {
        return tbJlAccountMapper.selectTbJlAccountList(tbJlAccount);
    }

    /**
     * 新增巨量主体
     * 
     * @param tbJlAccount 巨量主体
     * @return 结果
     */
    @Override
    public int insertTbJlAccount(TbJlAccount tbJlAccount)
    {
        return tbJlAccountMapper.insertTbJlAccount(tbJlAccount);
    }

    /**
     * 修改巨量主体
     * 
     * @param tbJlAccount 巨量主体
     * @return 结果
     */
    @Override
    public int updateTbJlAccount(TbJlAccount tbJlAccount)
    {
        return tbJlAccountMapper.updateTbJlAccount(tbJlAccount);
    }

    /**
     * 批量删除巨量主体
     * 
     * @param ids 需要删除的巨量主体主键
     * @return 结果
     */
    @Override
    public int deleteTbJlAccountByIds(String[] ids)
    {
        return tbJlAccountMapper.deleteTbJlAccountByIds(ids);
    }

    /**
     * 删除巨量主体信息
     * 
     * @param id 巨量主体主键
     * @return 结果
     */
    @Override
    public int deleteTbJlAccountById(String id)
    {
        return tbJlAccountMapper.deleteTbJlAccountById(id);
    }

    @Override
    public String selectSecretByAppId(String appId) {
        if(StringUtils.isBlank(appId)){
            return "";
        }
        return tbJlAccountMapper.selectSecretByAppId(appId);
    }
}
