package com.ruoyi.system.service.wxiaa.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.system.bo.wxiaa.GdtAdgroupCountBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.wxiaa.GdtAdgroupMapper;
import com.ruoyi.system.entity.wxiaa.GdtAdgroupEntity;
import com.ruoyi.system.service.wxiaa.GdtAdgroupService;

/**
 * 广点通广告计划Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class GdtAdgroupServiceImpl implements GdtAdgroupService {

    @Autowired
    private GdtAdgroupMapper gdtAdgroupMapper;

    /**
     * 查询广点通广告计划
     *
     * @param id 广点通广告计划主键
     * @return 广点通广告计划
     */
    @Override
    public GdtAdgroupEntity selectGdtAdgroupById(Long id)
    {
        return gdtAdgroupMapper.selectGdtAdgroupById(id);
    }

    @Override
    public GdtAdgroupEntity selectByAdgroupId(Long adgroupId) {
        if (null == adgroupId) {
            return null;
        }
        return gdtAdgroupMapper.selectByAdgroupId(adgroupId);
    }

    @Override
    public List<GdtAdgroupEntity> selectGdtAdgroupList(GdtAdgroupEntity gdtAdgroup) {
        return gdtAdgroupMapper.selectGdtAdgroupList(gdtAdgroup);
    }

    @Override
    public Map<Long, Integer> selectAdgroupCountMap(List<Long> advertisementIds) {
        if (CollectionUtils.isEmpty(advertisementIds)) {
            return Collections.emptyMap();
        }
        List<GdtAdgroupCountBo> list = gdtAdgroupMapper.selectAdgroupCount(advertisementIds);
        return list.stream().collect(Collectors.toMap(GdtAdgroupCountBo::getAdvertisementId, GdtAdgroupCountBo::getAdgroupCount));
    }

    /**
     * 新增广点通广告计划
     *
     * @param gdtAdgroup 广点通广告计划
     * @return 结果
     */
    @Override
    public int insertGdtAdgroup(GdtAdgroupEntity gdtAdgroup)
    {

            return gdtAdgroupMapper.insertGdtAdgroup(gdtAdgroup);
    }

    /**
     * 修改广点通广告计划
     *
     * @param gdtAdgroup 广点通广告计划
     * @return 结果
     */
    @Override
    public int updateGdtAdgroup(GdtAdgroupEntity gdtAdgroup)
    {
        return gdtAdgroupMapper.updateGdtAdgroup(gdtAdgroup);
    }

    /**
     * 批量删除广点通广告计划
     *
     * @param ids 需要删除的广点通广告计划主键
     * @return 结果
     */
    @Override
    public int deleteGdtAdgroupByIds(Long[] ids)
    {
        return gdtAdgroupMapper.deleteGdtAdgroupByIds(ids);
    }

    /**
     * 删除广点通广告计划信息
     *
     * @param id 广点通广告计划主键
     * @return 结果
     */
    @Override
    public int deleteGdtAdgroupById(Long id)
    {
        return gdtAdgroupMapper.deleteGdtAdgroupById(id);
    }

    @Override
    public int batchInsertUpdate(List<GdtAdgroupEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return gdtAdgroupMapper.batchInsertUpdate(list);
    }

    @Override
    public List<GdtAdgroupEntity> selectByAdgroupIds(List<Long> adgroupIds) {
        if(CollectionUtils.isEmpty(adgroupIds)){
            return Collections.emptyList();
        }
        return gdtAdgroupMapper.selectByAdgroupIds(adgroupIds);
    }
}
