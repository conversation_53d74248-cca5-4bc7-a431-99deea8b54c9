package com.ruoyi.system.service.shortplay.tv.impl;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.lang.Pair;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.shortplay.tv.ShortplayTvSeriesMapper;
import com.ruoyi.system.entity.shortplay.tv.ShortplayTvSeriesEntity;
import com.ruoyi.system.service.shortplay.tv.ShortplayTvSeriesService;

/**
 * 短剧剧集Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShortplayTvSeriesServiceImpl implements ShortplayTvSeriesService {

    @Autowired
    private ShortplayTvSeriesMapper shortplayTvSeriesMapper;

    @Autowired
    private ExecutorService executorService;

    public LoadingCache<Pair<String, Integer>, Optional<ShortplayTvSeriesEntity>> TV_SERIES_CACHE = CacheBuilder.newBuilder()
            .refreshAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Pair<String, Integer>, Optional<ShortplayTvSeriesEntity>>() {
                @Override
                public Optional<ShortplayTvSeriesEntity> load(Pair<String, Integer> key) {
                    ShortplayTvSeriesEntity tvSeries = selectByTvIdAndSeries(key.getKey(), key.getValue());
                    return Optional.ofNullable(tvSeries);
                }

                @Override
                public ListenableFuture<Optional<ShortplayTvSeriesEntity>> reload(Pair<String, Integer> key, Optional<ShortplayTvSeriesEntity> oldValue) {
                    ListenableFutureTask<Optional<ShortplayTvSeriesEntity>> task = ListenableFutureTask.create(() -> load(key));
                    executorService.submit(task);
                    return task;
                }
            });

    @Override
    public ShortplayTvSeriesEntity selectShortplayTvSeriesById(String id) {
        return shortplayTvSeriesMapper.selectShortplayTvSeriesById(id);
    }

    @Override
    public ShortplayTvSeriesEntity selectByTtSeriesId(String ttSeriesId) {
        if (StringUtils.isBlank(ttSeriesId)) {
            return null;
        }
        return shortplayTvSeriesMapper.selectByTtSeriesId(ttSeriesId);
    }

    @Override
    public ShortplayTvSeriesEntity selectCacheByTvIdAndSeries(String tvId, Integer series) {
        if (StringUtils.isBlank(tvId) || null == series) {
            return null;
        }
        try {
            return TV_SERIES_CACHE.get(Pair.of(tvId, series)).orElse(null);
        } catch (Exception e) {
            log.error("selectByTvIdAndSeriesCache error, tvId={}, series={}", tvId, series, e);
        }
        return null;
    }

    @Override
    public ShortplayTvSeriesEntity selectByTvIdAndSeries(String tvId, Integer series) {
        if (StringUtils.isBlank(tvId) || null == series) {
            return null;
        }
        return shortplayTvSeriesMapper.selectByTvIdAndSeries(tvId, series);
    }

    /**
     * 查询短剧剧集列表
     *
     * @param shortplayTvSeries 短剧剧集
     * @return 短剧剧集
     */
    @Override
    public List<ShortplayTvSeriesEntity> selectShortplayTvSeriesList(ShortplayTvSeriesEntity shortplayTvSeries)
    {
        return shortplayTvSeriesMapper.selectShortplayTvSeriesList(shortplayTvSeries);
    }

    /**
     * 新增短剧剧集
     *
     * @param shortplayTvSeries 短剧剧集
     * @return 结果
     */
    @Override
    public int insertShortplayTvSeries(ShortplayTvSeriesEntity shortplayTvSeries)
    {

            return shortplayTvSeriesMapper.insertShortplayTvSeries(shortplayTvSeries);
    }

    /**
     * 修改短剧剧集
     *
     * @param shortplayTvSeries 短剧剧集
     * @return 结果
     */
    @Override
    public int updateShortplayTvSeries(ShortplayTvSeriesEntity shortplayTvSeries)
    {
        return shortplayTvSeriesMapper.updateShortplayTvSeries(shortplayTvSeries);
    }

    /**
     * 批量删除短剧剧集
     *
     * @param ids 需要删除的短剧剧集主键
     * @return 结果
     */
    @Override
    public int deleteShortplayTvSeriesByIds(String[] ids)
    {
        return shortplayTvSeriesMapper.deleteShortplayTvSeriesByIds(ids);
    }

    /**
     * 删除短剧剧集信息
     *
     * @param id 短剧剧集主键
     * @return 结果
     */
    @Override
    public int deleteShortplayTvSeriesByTbId(String tbId)
    {
        return shortplayTvSeriesMapper.deleteShortplayTvSeriesByTbId(tbId);
    }

    /**
     * 根据抖音视频ID查询短剧ID
     * @param ttVideoId
     * @return
     */
    @Override
    public ShortplayTvSeriesEntity selectByTtVideoId(String ttVideoId) {
        return shortplayTvSeriesMapper.selectByTtVideoId(ttVideoId);
    }
}
