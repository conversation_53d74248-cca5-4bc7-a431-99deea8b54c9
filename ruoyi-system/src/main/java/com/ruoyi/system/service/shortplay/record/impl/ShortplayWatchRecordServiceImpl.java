package com.ruoyi.system.service.shortplay.record.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.shortplay.record.ShortplayWatchRecordMapper;
import com.ruoyi.system.entity.shortplay.record.ShortplayWatchRecordEntity;
import com.ruoyi.system.service.shortplay.record.ShortplayWatchRecordService;

/**
 * 短剧小程序播放记录Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ShortplayWatchRecordServiceImpl implements ShortplayWatchRecordService {

    @Autowired
    private ShortplayWatchRecordMapper shortplayWatchRecordMapper;

    @Override
    public Integer selectNewestSeries(Long userId, Long tvId) {
        if (null == userId || null == tvId) {
            return null;
        }
        return shortplayWatchRecordMapper.selectNewestSeries(userId, tvId);
    }

    @Override
    public List<ShortplayWatchRecordEntity> selectAllNewestSeries(Long userId) {
        return shortplayWatchRecordMapper.selectAllNewestSeries(userId);
    }

    @Override
    public List<ShortplayWatchRecordEntity> selectList(ShortplayWatchRecordEntity param) {
        return shortplayWatchRecordMapper.selectList(param);
    }

    @Override
    public int insert(ShortplayWatchRecordEntity record) {
        return shortplayWatchRecordMapper.insert(record);
    }

    @Override
    public int update(ShortplayWatchRecordEntity record) {
        return shortplayWatchRecordMapper.update(record);
    }
}
