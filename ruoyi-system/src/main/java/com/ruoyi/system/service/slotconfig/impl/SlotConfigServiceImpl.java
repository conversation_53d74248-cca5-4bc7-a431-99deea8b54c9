package com.ruoyi.system.service.slotconfig.impl;

import com.ruoyi.system.entity.slotconfig.SlotConfigEntity;
import com.ruoyi.system.mapper.slotconfig.SlotConfigMapper;
import com.ruoyi.system.service.slotconfig.SlotConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告位地域分流配置Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SlotConfigServiceImpl implements SlotConfigService {
    @Autowired
    private SlotConfigMapper slotConfigMapper;

    /**
     * 查询广告位地域分流配置
     *
     * @param id 广告位地域分流配置主键
     * @return 广告位地域分流配置
     */
    @Override
    public SlotConfigEntity selectSlotConfigById(Long id)
    {
        return slotConfigMapper.selectSlotConfigById(id);
    }

    /**
     * 查询广告位地域分流配置列表
     *
     * @param slotConfig 广告位地域分流配置
     * @return 广告位地域分流配置
     */
    @Override
    public List<SlotConfigEntity> selectSlotConfigList(SlotConfigEntity slotConfig)
    {
        return slotConfigMapper.selectSlotConfigList(slotConfig);
    }

    /**
     * 新增广告位地域分流配置
     *
     * @param slotConfig 广告位地域分流配置
     * @return 结果
     */
    @Override
    public int insertSlotConfig(SlotConfigEntity slotConfig)
    {

            return slotConfigMapper.insertSlotConfig(slotConfig);
    }

    /**
     * 修改广告位地域分流配置
     *
     * @param slotConfig 广告位地域分流配置
     * @return 结果
     */
    @Override
    public int updateSlotConfig(SlotConfigEntity slotConfig)
    {
        return slotConfigMapper.updateSlotConfig(slotConfig);
    }

    /**
     * 批量删除广告位地域分流配置
     *
     * @param ids 需要删除的广告位地域分流配置主键
     * @return 结果
     */
    @Override
    public int deleteSlotConfigByIds(Long[] ids)
    {
        return slotConfigMapper.deleteSlotConfigByIds(ids);
    }

    @Override
    public int deleteByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return 0;
        }
        return slotConfigMapper.deleteByIds(ids);
    }

    /**
     * 删除广告位地域分流配置信息
     *
     * @param id 广告位地域分流配置主键
     * @return 结果
     */
    @Override
    public int deleteSlotConfigById(Long id)
    {
        return slotConfigMapper.deleteSlotConfigById(id);
    }

    @Override
    public List<SlotConfigEntity> selectBySlotId(Long slotId) {
        return slotConfigMapper.selectBySlotId(slotId);
    }

    @Override
    public Map<Long, String> selectConfigNameMap(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyMap();
        }
        return slotConfigMapper.selectByIds(ids).stream().collect(Collectors.toMap(SlotConfigEntity::getId, SlotConfigEntity::getConfigName,(v1,v2)->v1));
    }
}
