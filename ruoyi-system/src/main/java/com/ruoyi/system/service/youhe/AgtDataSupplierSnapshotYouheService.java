package com.ruoyi.system.service.youhe;

import java.util.List;
import com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity;

/**
 * 短剧供应商友和Service接口
 *
 * <AUTHOR>
 */
public interface AgtDataSupplierSnapshotYouheService {
    /**
     * 查询短剧供应商友和
     *
     * @param id 短剧供应商友和主键
     * @return 短剧供应商友和
     */
    public AgtDataSupplierSnapshotYouheEntity selectAgtDataSupplierSnapshotYouheById(String id);

    /**
     * 查询短剧供应商友和列表
     *
     * @param agtDataSupplierSnapshotYouhe 短剧供应商友和
     * @return 短剧供应商友和集合
     */
    public List<AgtDataSupplierSnapshotYouheEntity> selectAgtDataSupplierSnapshotYouheList(AgtDataSupplierSnapshotYouheEntity agtDataSupplierSnapshotYouhe);

    /**
     * 新增短剧供应商友和
     *
     * @param agtDataSupplierSnapshotYouhe 短剧供应商友和
     * @return 结果
     */
    public int insertAgtDataSupplierSnapshotYouhe(AgtDataSupplierSnapshotYouheEntity agtDataSupplierSnapshotYouhe);

    /**
     * 修改短剧供应商友和
     *
     * @param agtDataSupplierSnapshotYouhe 短剧供应商友和
     * @return 结果
     */
    public int updateAgtDataSupplierSnapshotYouhe(AgtDataSupplierSnapshotYouheEntity agtDataSupplierSnapshotYouhe);

    /**
     * 批量删除短剧供应商友和
     *
     * @param ids 需要删除的短剧供应商友和主键集合
     * @return 结果
     */
    public int deleteAgtDataSupplierSnapshotYouheByIds(String[] ids);

    /**
     * 删除短剧供应商友和信息
     *
     * @param id 短剧供应商友和主键
     * @return 结果
     */
    public int deleteAgtDataSupplierSnapshotYouheById(String id);
}
