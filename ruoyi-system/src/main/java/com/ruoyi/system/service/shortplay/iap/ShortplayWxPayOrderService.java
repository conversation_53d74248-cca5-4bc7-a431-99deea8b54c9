package com.ruoyi.system.service.shortplay.iap;

import java.util.List;
import com.ruoyi.system.entity.shortplay.iap.ShortplayWxPayOrderEntity;

/**
 * 短剧小程序微信支付订单Service接口
 *
 * <AUTHOR>
 */
public interface ShortplayWxPayOrderService {

    /**
     * 查询短剧小程序微信支付订单
     *
     * @param outTradeNo 商户订单号
     * @return 短剧小程序微信支付订单
     */
    ShortplayWxPayOrderEntity selectByOutTradeNo(String outTradeNo);

    /**
     * 查询短剧小程序微信支付订单列表
     *
     * @param param 短剧小程序微信支付订单
     * @return 短剧小程序微信支付订单集合
     */
    List<ShortplayWxPayOrderEntity> selectList(ShortplayWxPayOrderEntity param);

    /**
     * 新增短剧小程序微信支付订单
     *
     * @param order 短剧小程序微信支付订单
     * @return 结果
     */
    int insert(ShortplayWxPayOrderEntity order);

    /**
     * 修改短剧小程序微信支付订单
     *
     * @param order 短剧小程序微信支付订单
     * @return 结果
     */
    int update(ShortplayWxPayOrderEntity order);
}
