package com.ruoyi.system.service.coupon.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.PlayletCouponUseStatusEnum;
import com.ruoyi.common.utils.CheckSignUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.entity.coupon.PlayletCouponEntity;
import com.ruoyi.system.mapper.coupon.PlayletCouponMapper;
import com.ruoyi.system.service.coupon.PlayletCouponService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 短剧小程序看剧权益券Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-22
 */
@Slf4j
@Service
public class PlayletCouponServiceImpl implements PlayletCouponService {

    @Autowired
    private PlayletCouponMapper playletCouponMapper;

    @Value("${jfsd.url}")
    private String url;

    @Value("${jfsd.signKey}")
    private String signKey;


    /**
     * 查询短剧小程序看剧权益券
     * 
     * @param id 短剧小程序看剧权益券主键
     * @return 短剧小程序看剧权益券
     */
    @Override
    public PlayletCouponEntity selectPlayletCouponById(String id)
    {
        return playletCouponMapper.selectPlayletCouponById(id);
    }

    /**
     * 查询短剧小程序看剧权益券列表
     * 
     * @param playletCoupon 短剧小程序看剧权益券
     * @return 短剧小程序看剧权益券
     */
    @Override
    public List<PlayletCouponEntity> selectPlayletCouponList(PlayletCouponEntity playletCoupon)
    {
        if (Objects.nonNull(playletCoupon)&&Objects.nonNull(playletCoupon.getStartDate())){
            playletCoupon.setStartDate(DateUtil.beginOfDay(playletCoupon.getStartDate()));
        }
        if (Objects.nonNull(playletCoupon)&&Objects.nonNull(playletCoupon.getEndDate())){
            playletCoupon.setEndDate(DateUtil.endOfDay(playletCoupon.getEndDate()));
        }
        return playletCouponMapper.selectPlayletCouponList(playletCoupon);
    }

    /**
     * 新增短剧小程序看剧权益券
     *
     * @param playletCoupon 短剧小程序看剧权益券
     * @return 结果
     */
    @Override
    public boolean insertPlayletCoupon(PlayletCouponEntity playletCoupon)
    {
        return playletCouponMapper.insertPlayletCoupon(playletCoupon) > 0;
    }

    /**
     * 修改短剧小程序看剧权益券
     * 
     * @param playletCoupon 短剧小程序看剧权益券
     * @return 结果
     */
    @Override
    public int updatePlayletCoupon(PlayletCouponEntity playletCoupon)
    {
        return playletCouponMapper.updatePlayletCoupon(playletCoupon);
    }

    /**
     * 批量删除短剧小程序看剧权益券
     * 
     * @param ids 需要删除的短剧小程序看剧权益券主键
     * @return 结果
     */
    @Override
    public int deletePlayletCouponByIds(String[] ids)
    {
        return playletCouponMapper.deletePlayletCouponByIds(ids);
    }

    /**
     * 删除短剧小程序看剧权益券信息
     * 
     * @param id 短剧小程序看剧权益券主键
     * @return 结果
     */
    @Override
    public int deletePlayletCouponById(String id)
    {
        return playletCouponMapper.deletePlayletCouponById(id);
    }

    @Override
    public PlayletCouponEntity selectByOrderId(String orderId) {
        if(StringUtils.isBlank(orderId)){
            return null;
        }
        return playletCouponMapper.selectByOrderId(orderId);
    }

    @Override
    public PlayletCouponEntity selectByPhoneAndVideoId(String phone, String videoId) {
        if(StringUtils.isBlank(phone) ||StringUtils.isBlank(videoId)){
            return null;
        }
        return playletCouponMapper.selectByPhoneAndVideoId(phone,videoId);
    }

    @Override
    public PlayletCouponEntity selectUnusedListByPhone(String phone) {
        if(StringUtils.isBlank(phone)){
            return null;
        }
        return playletCouponMapper.selectUnusedListByPhone(phone, PlayletCouponUseStatusEnum.UNUSED.getStatus());
    }

    @Override
    public List<PlayletCouponEntity> selectByPhoneAndStatus(String phone, Integer useStatus) {
        if(StringUtils.isBlank(phone)){
            return Collections.emptyList();
        }
        return playletCouponMapper.selectListByPhoneAndStatus(phone,useStatus);
    }

    @Override
    public List<PlayletCouponEntity> selectExpiredList(Integer status,Long lastId) {
        return playletCouponMapper.selectExpiredList(status,lastId);
    }

    @Override
    public Boolean batchUpdateUseStatusByIds(List<Long> playletCouponIdList,Integer status) {
        if (CollectionUtils.isEmpty(playletCouponIdList) || Objects.isNull(PlayletCouponUseStatusEnum.getStatusStrByStatus(status))){
            return false;
        }
        return playletCouponMapper.batchUpdateUseStatusByIds(playletCouponIdList,status);
    }

    @Override
    public boolean reportCouponStatus(PlayletCouponEntity entity) {
        Map<String,Object> param = new HashMap<>();
        param.put("orderId",entity.getOrderId());
        param.put("useStatus",PlayletCouponUseStatusEnum.getStatusStrByStatus(entity.getUseStatus()));

        if(PlayletCouponUseStatusEnum.isUsed(entity.getUseStatus())){
            param.put("useTime", DateUtil.formatDateTime(entity.getUseTime()));
            param.put("message","核销成功");
            param.put("useProductName", entity.getVideoName());
        }else if(Objects.equals(entity.getUseStatus(),PlayletCouponUseStatusEnum.UNUSED.getStatus())) {
            param.put("message", "已过期");
            param.put("expiredTime",DateUtil.formatDateTime(entity.getExpiredTime()));
            param.put("useProductName","");
        }
        param.put("useProductType",entity.getUseProductType());
        String sign = CheckSignUtils.getGig(param,signKey);
        param.put("sign",sign);
        String resp="";
        int retryCount=0;
        while (retryCount<3){
            resp = HttpUtils.sendPostJSON(url, JSONObject.toJSONString(param));
            log.info("第{}上报核销结果,orderId:{},sign:{},resp:{}",retryCount+1,entity.getOrderId(),sign,resp);
            JSONObject jsonObject = JSON.parseObject(resp);
            Integer code = jsonObject.getInteger("code");
            if (code==0){
                return true;
            }
            retryCount++;
        }
        log.error("上报核销结果失败,orderId:{},sign:{},resp:{}",entity.getOrderId(),sign,resp);
        return false;
    }

}