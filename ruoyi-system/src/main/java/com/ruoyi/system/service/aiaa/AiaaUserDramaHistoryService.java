package com.ruoyi.system.service.aiaa;


import com.ruoyi.system.entity.aiaa.AiaaUserDramaHistoryEntity;
import com.ruoyi.system.req.playlet.aiaa.ListAiaaUserDramaHistoryReq;

import java.util.List;

/**
 * 用户查看短剧历史 Service
 *
 * <AUTHOR>
 * @date 2023年7月11日 下午5:45:32
 */
public interface AiaaUserDramaHistoryService {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean insert(AiaaUserDramaHistoryEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    Boolean updateById(AiaaUserDramaHistoryEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    AiaaUserDramaHistoryEntity selectById(Long id);
    List<AiaaUserDramaHistoryEntity> selectList(ListAiaaUserDramaHistoryReq req);

}
