package com.ruoyi.system.service.wxiaa;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.entity.wxiaa.GdtAdgroupEntity;

/**
 * 广点通广告计划Service接口
 *
 * <AUTHOR>
 */
public interface GdtAdgroupService {

    /**
     * 查询广点通广告计划
     *
     * @param id 广点通广告计划主键
     * @return 广点通广告计划
     */
    public GdtAdgroupEntity selectGdtAdgroupById(Long id);

    /**
     * 查询广点通广告计划
     *
     * @param adgroupId 广点通广告计划ID
     * @return 广点通广告计划
     */
    GdtAdgroupEntity selectByAdgroupId(Long adgroupId);

    /**
     * 查询广点通广告计划列表
     *
     * @param gdtAdgroup 广点通广告计划
     * @return 广点通广告计划集合
     */
    List<GdtAdgroupEntity> selectGdtAdgroupList(GdtAdgroupEntity gdtAdgroup);

    /**
     * 查询广点通账户对应的广告计划数
     *
     * @param advertisementIds 广点通广告账户ID列表
     * @return 广告账户ID-广告计划数映射
     */
    Map<Long, Integer> selectAdgroupCountMap(List<Long> advertisementIds);

    /**
     * 新增广点通广告计划
     *
     * @param gdtAdgroup 广点通广告计划
     * @return 结果
     */
    public int insertGdtAdgroup(GdtAdgroupEntity gdtAdgroup);

    /**
     * 修改广点通广告计划
     *
     * @param gdtAdgroup 广点通广告计划
     * @return 结果
     */
    public int updateGdtAdgroup(GdtAdgroupEntity gdtAdgroup);

    /**
     * 批量删除广点通广告计划
     *
     * @param ids 需要删除的广点通广告计划主键集合
     * @return 结果
     */
    public int deleteGdtAdgroupByIds(Long[] ids);

    /**
     * 删除广点通广告计划信息
     *
     * @param id 广点通广告计划主键
     * @return 结果
     */
    public int deleteGdtAdgroupById(Long id);

    /**
     * 批量新增广点通广告计划
     *
     * @param list 数据列表
     * @return 影响行数
     */
    int batchInsertUpdate(List<GdtAdgroupEntity> list);

    /**
     * 根据广告id列表查询
     * @param adgroupIds
     * @return
     */
    List<GdtAdgroupEntity> selectByAdgroupIds(List<Long> adgroupIds);
}
