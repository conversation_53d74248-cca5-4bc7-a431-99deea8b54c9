package com.ruoyi.system.service.hotcake;

import java.util.Date;
import java.util.List;
import com.ruoyi.system.entity.hotcake.HotcakeDayDataEntity;
import com.ruoyi.system.req.hotcake.HotcakeDayDataListReq;
import com.ruoyi.system.req.hotcake.HotcakeDayDataUpdateReq;

/**
 * 爆款小程序日数据Service接口
 *
 * <AUTHOR>
 */
public interface HotcakeDayDataService {

    /**
     * 查询爆款小程序日数据
     *
     * @param id 爆款小程序日数据主键
     * @return 爆款小程序日数据
     */
    Long selectIdBy(Date curDate, String appId, String channelCode);
    /**
     * 查询爆款小程序日数据
     *
     * @param id 爆款小程序日数据主键
     * @return 爆款小程序日数据
     */
    public HotcakeDayDataEntity selectHotcakeDayDataById(Long id);

    /**
     * 查询爆款小程序日数据列表
     *
     * @param hotcakeDayData 爆款小程序日数据
     * @return 爆款小程序日数据集合
     */
    List<HotcakeDayDataEntity> selectHotcakeDayDataList(HotcakeDayDataListReq req);

    /**
     * 新增爆款小程序日数据
     *
     * @param hotcakeDayData 爆款小程序日数据
     * @return 结果
     */
    int insert(Date curDate, String appId, String channelCode);

    /**
     * 修改爆款小程序日数据
     *
     * @param hotcakeDayData 爆款小程序日数据
     * @return 结果
     */
    public int updateHotcakeDayData(HotcakeDayDataUpdateReq req);
}
