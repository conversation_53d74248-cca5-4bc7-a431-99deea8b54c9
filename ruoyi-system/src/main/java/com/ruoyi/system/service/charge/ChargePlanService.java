package com.ruoyi.system.service.charge;


import com.ruoyi.system.entity.charge.ChargePlanEntity;

import java.util.List;

/**
 * 充值方案表 Service
 *
 * <AUTHOR>
 * @date 2023-7-4 14:48:11
 */
public interface ChargePlanService {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean insert(ChargePlanEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    Boolean updateById(ChargePlanEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    ChargePlanEntity selectById(Long id);

    /**
     * 查询所有充值方案
     * @return
     */
    List<ChargePlanEntity> selectAll();

    /**
     * 批量移动
     * @param chargePlanEntities
     * @return
     */
    boolean batchMove(List<ChargePlanEntity> chargePlanEntities);

    /**
     * 查询最大的排序
     * @return
     */
    int selectMaxSort();
}
