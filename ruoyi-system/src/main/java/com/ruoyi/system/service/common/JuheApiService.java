package com.ruoyi.system.service.common;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 聚合接口
 *
 * <AUTHOR>
 * @date 2021/9/2
 */
@Slf4j
@Service
public class JuheApiService {

    // IP解析查询地址
    public static String IP_ANALYSIS_URL = "http://apis.juhe.cn/ip/ipNewV3?key=";
    // 接口请求超时时间
    private static final int HTTP_TIMEOUT = 5000;

    // 聚合-IP解析接口Key
    @Value("${juhe.key.ipAnalysis}")
    private String ipAnalysisKey;



    /**
     * IP解析
     *
     * @param ip IP地址
     * @return 解析结果
     */
    public JSONObject ipAnalysis(String ip) {
        if (StringUtils.isBlank(ip)) {
            return null;
        }

        try {
            // 接口Key判断
            if (StringUtils.isBlank(ipAnalysisKey) || ipAnalysisKey.length() < 32) {
                return null;
            }

            // 调用聚合接口解析IP
            String url = IP_ANALYSIS_URL + ipAnalysisKey + "&ip=" + ip;
            String result = HttpUtil.get(url, HTTP_TIMEOUT);
            if (!JSONUtil.isTypeJSONObject(result)) {
                log.error("IP解析异常, 非JSON格式, ip={}, result={}", ip, StrUtil.subPre(result, 100));
                return null;
            }
            return JSON.parseObject(result);
        } catch (HttpException e) {
            log.info("IP解析异常, ip={}", ip, e);
        } catch (Exception e) {
            log.error("IP解析异常, ip={}", ip, e);
        }
        return null;
    }
}
