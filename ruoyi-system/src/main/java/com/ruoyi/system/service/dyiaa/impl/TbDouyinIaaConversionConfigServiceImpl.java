package com.ruoyi.system.service.dyiaa.impl;

import com.ruoyi.system.entity.dyiaa.TbDouyinIaaConversionConfig;
import com.ruoyi.system.mapper.dyiaa.TbDouyinIaaConversionConfigMapper;
import com.ruoyi.system.service.dyiaa.ITbDouyinIaaConversionConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 抖音IAA回传配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Service
public class TbDouyinIaaConversionConfigServiceImpl implements ITbDouyinIaaConversionConfigService {

    @Autowired
    private TbDouyinIaaConversionConfigMapper tbDouyinIaaConversionConfigMapper;

    /**
     * 查询抖音IAA回传配置
     *
     * @param configId 回传配置ID
     * @return 抖音IAA回传配置
     */
    @Override
    public TbDouyinIaaConversionConfig selectByConfigId(String configId) {
        return tbDouyinIaaConversionConfigMapper.selectByConfigId(configId);
    }

    /**
     * 查询抖音IAA回传配置列表
     *
     * @param tbDouyinIaaConversionConfig 抖音IAA回传配置
     * @return 抖音IAA回传配置
     */
    @Override
    public List<TbDouyinIaaConversionConfig> selectTbDouyinIaaConversionConfigList(TbDouyinIaaConversionConfig tbDouyinIaaConversionConfig) {
        return tbDouyinIaaConversionConfigMapper.selectTbDouyinIaaConversionConfigList(tbDouyinIaaConversionConfig);
    }

    @Override
    public List<TbDouyinIaaConversionConfig> selectFixedCostConfigList() {
        return tbDouyinIaaConversionConfigMapper.selectFixedCostConfigList();
    }

    /**
     * 新增抖音IAA回传配置
     *
     * @param tbDouyinIaaConversionConfig 抖音IAA回传配置
     * @return 结果
     */
    @Override
    public int insertTbDouyinIaaConversionConfig(TbDouyinIaaConversionConfig tbDouyinIaaConversionConfig) {
        return tbDouyinIaaConversionConfigMapper.insertTbDouyinIaaConversionConfig(tbDouyinIaaConversionConfig);
    }

    /**
     * 修改抖音IAA回传配置
     *
     * @param tbDouyinIaaConversionConfig 抖音IAA回传配置
     * @return 结果
     */
    @Override
    public int updateTbDouyinIaaConversionConfig(TbDouyinIaaConversionConfig tbDouyinIaaConversionConfig) {
        return tbDouyinIaaConversionConfigMapper.updateTbDouyinIaaConversionConfig(tbDouyinIaaConversionConfig);
    }
}
