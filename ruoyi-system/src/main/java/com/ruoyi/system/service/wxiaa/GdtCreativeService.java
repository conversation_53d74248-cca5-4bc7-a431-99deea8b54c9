package com.ruoyi.system.service.wxiaa;

import java.util.List;

import com.ruoyi.system.entity.wxiaa.GdtCreativeEntity;

/**
 * 广点通广告创意Service接口
 *
 * <AUTHOR>
 */
public interface GdtCreativeService {
    /**
     * 查询广点通广告创意
     *
     * @param id 广点通广告创意主键
     * @return 广点通广告创意
     */
    public GdtCreativeEntity selectGdtCreativeById(Long id);

    /**
     * 查询广点通广告创意列表
     *
     * @param gdtCreative 广点通广告创意
     * @return 广点通广告创意集合
     */
    public List<GdtCreativeEntity> selectGdtCreativeList(GdtCreativeEntity gdtCreative);

    /**
     * 新增广点通广告创意
     *
     * @param gdtCreative 广点通广告创意
     * @return 结果
     */
    public int insertGdtCreative(GdtCreativeEntity gdtCreative);

    /**
     * 修改广点通广告创意
     *
     * @param gdtCreative 广点通广告创意
     * @return 结果
     */
    public int updateGdtCreative(GdtCreativeEntity gdtCreative);

    /**
     * 批量删除广点通广告创意
     *
     * @param ids 需要删除的广点通广告创意主键集合
     * @return 结果
     */
    public int deleteGdtCreativeByIds(Long[] ids);

    /**
     * 删除广点通广告创意信息
     *
     * @param id 广点通广告创意主键
     * @return 结果
     */
    public int deleteGdtCreativeById(Long id);

    /**
     * 批量新增广点通创意
     *
     * @param list 数据列表
     * @return 影响行数
     */
    int batchInsertUpdate(List<GdtCreativeEntity> list);
}
