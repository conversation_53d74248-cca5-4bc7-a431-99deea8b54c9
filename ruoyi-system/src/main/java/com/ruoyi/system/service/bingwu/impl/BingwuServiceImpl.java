package com.ruoyi.system.service.bingwu.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.bingwu.AgtDataSupplierSnapshotBingwuEntity;
import com.ruoyi.system.entity.fundStat.SupplierIncomeItem;
import com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity;
import com.ruoyi.system.entity.supplier.SupplierEnum;
import com.ruoyi.system.mapper.bingwu.AgtDataSupplierSnapshotBingwuMapper;
import com.ruoyi.system.mapper.bingwu.BingwuMapper;
import com.ruoyi.system.service.SupplierService;
import com.ruoyi.system.service.bingwu.AgtDataSupplierSnapshotBingwuSelfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @author: keboom
 * @date: 2024/7/30
 */
@Slf4j
@Service
public class BingwuServiceImpl implements AgtDataSupplierSnapshotBingwuSelfService, SupplierService {

    @Autowired
    private BingwuMapper bingwuMapper;
    @Value("${bingwu.key}")
    private String key;
    private int pageSize;
    private int pageNum;

    @Override
    @Async
    public void getOrderInfoSaveToDB(Date startTime, Date endTime) {
        log.info("获取订单信息并保存到数据库");
        pageNum = 1;
        pageSize = 30;
        JSONObject orderList = getOrderList(startTime, endTime);
        if (orderList == null) {
            return;
        }
        JSONObject data = orderList.getJSONObject("data");
        if (data == null) {
            return;
        }
        JSONArray list = data.getJSONArray("list");
        if (list == null || list.isEmpty()) {
            return;
        }
        saveOrderList(list);

        Integer total = data.getInteger("total");
        int pageTotal = NumberUtil.ceilDiv(total, pageSize);
        pageNum++;
        while (pageNum <= pageTotal) {
            orderList = getOrderList(startTime, endTime);
            if (orderList == null) {
                return;
            }
            data = orderList.getJSONObject("data");
            if (data == null) {
                return;
            }
            list = data.getJSONArray("list");
            if (list == null || list.isEmpty()) {
                return;
            }
            saveOrderList(list);
            pageNum++;
        }
    }

    @Override
    public HashMap<String, SupplierIncomeItem> getDateSuppItemMap(String startTime, String endTime) {
        HashMap<String, SupplierIncomeItem> dateSuppItemMap = new HashMap<>();
        List<AgtDataSupplierSnapshotBingwuEntity> entities = bingwuMapper.selectIncomeByDay(startTime, endTime);
        for (AgtDataSupplierSnapshotBingwuEntity entity : entities) {
            SupplierIncomeItem item = new SupplierIncomeItem();
            item.setAmount(NumberUtil.div(entity.getAmount(), 100, 2, RoundingMode.HALF_UP));
            item.setSupplier(SupplierEnum.BINGWU.getName());
            dateSuppItemMap.put(DateUtil.formatDate(entity.getCurDate()), item);
        }
        return dateSuppItemMap;
    }

    private void saveOrderList(JSONArray orderList) {
        ArrayList<AgtDataSupplierSnapshotBingwuEntity> saveEntities = new ArrayList<>();
        for (int i = 0; i < orderList.size(); i++) {
            JSONObject order = orderList.getJSONObject(i);
            AgtDataSupplierSnapshotBingwuEntity entity = new AgtDataSupplierSnapshotBingwuEntity();
            BeanUtil.copyProperties(order, entity);
            //支付状态（1：未支付，2：支付成功，4已退款） 我只入库支付成功的
            if (entity.getPayStatus() != 2) {
                continue;
            }
            String promotionName = entity.getPromotionName();
            if (StringUtils.isEmpty(promotionName)) {
                entity.setPromotionName("");
                log.warn("订单信息中的promotionName为空 {}",order.toString());
            }
            String[] split = promotionName.split("-");
            String adId = split[0];
            if (NumberUtil.isLong(adId)) {
                entity.setAdvertiserId(adId);
            } else {
                log.warn("promotionName wrong. promotionName: {}", promotionName);
            }
            String createTimeStr = order.getString("createTime");
            if (StringUtils.isEmpty(createTimeStr)) {
                log.warn("订单信息中的createTime为空 {}",order.toString());
                entity.setCurDate(new Date());
                entity.setCurHour(-1L);
            }
            DateTime createTime = DateUtil.parse(createTimeStr, DateUtils.YYYY_MM_DD_HH_MM_SS);
            entity.setCurDate(DateUtil.beginOfDay(createTime));
            entity.setCurHour(Long.valueOf(DateUtil.hour(createTime, true)));
            setDefaultValue(entity);
            saveEntities.add(entity);
        }
        if (saveEntities.isEmpty()) {
            return;
        }
        bingwuMapper.saveOrUpdateBatch(saveEntities);
    }

    private void setDefaultValue(AgtDataSupplierSnapshotBingwuEntity entity) {
        if (entity.getAdvertiserId()==null) {
            entity.setAdvertiserId("");
        }
        if (entity.getCurDate()==null) {
            entity.setCurDate(new Date());
        }
        if (entity.getCurHour()==null) {
            entity.setCurHour(-1L);
        }
        if (entity.getAppId()==null) {
            entity.setAppId("");
        }
        if (entity.getAppName()==null) {
            entity.setAppName("");
        }
        if (entity.getSubAppId()==null) {
            entity.setSubAppId("");
        }
        if (entity.getSubAppName()==null) {
            entity.setSubAppName("");
        }
        if (entity.getOpenId()==null) {
            entity.setOpenId("");
        }
        if (entity.getUserCode()==null) {
            entity.setUserCode("");
        }
        if (entity.getPromotionId()==null) {
            entity.setPromotionId("");
        }
        if (entity.getPromotionName()==null) {
            entity.setPromotionName("");
        }
        if (entity.getPromotionTime()==null) {
            entity.setPromotionTime(new Date());
        }
        if (entity.getRegisterTime()==null) {
            entity.setRegisterTime(new Date());
        }
        if (entity.getOrderNo()==null) {
            entity.setOrderNo("");
        }
        if (entity.getPayAppType()==null) {
            entity.setPayAppType(0L);
        }
        if (entity.getDescription() == null) {
            entity.setDescription("");
        }
        if (entity.getPayStatus() == null) {
            entity.setPayStatus(0L);
        }
        if (entity.getState() == null) {
            entity.setState(0L);
        }
        if (entity.getAmount() == null) {
            entity.setAmount(BigDecimal.ZERO);
        }
        if (entity.getSourceId() == null) {
            entity.setSourceId(0L);
        }
        if (entity.getSource() == null) {
            entity.setSource("");
        }
        if (entity.getIsVip() == null) {
            entity.setIsVip(0L);
        }
        if (entity.getDramaId() == null) {
            entity.setDramaId(0L);
        }
        if (entity.getEpNo() == null) {
            entity.setEpNo(0L);
        }
        if (entity.getSuccessTime() == null) {
            entity.setSuccessTime(new Date());
        }
        if (entity.getDramaName() == null) {
            entity.setDramaName("");
        }
        if (entity.getSystem() == null) {
            entity.setSystem("");
        }
    }

    private JSONObject getOrderList(Date startTime, Date endTime) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("page_size", pageSize);
        requestBody.put("page_num", pageNum);
        requestBody.put("start_time", DateUtil.format(startTime, DateUtils.YYYY_MM_DD_HH_MM_SS));
        requestBody.put("end_time", DateUtil.format(endTime, DateUtils.YYYY_MM_DD_HH_MM_SS));
        String respStr = HttpUtil.createPost("https://vnovel-out.bwumedia.com/api/order/query?key=" + key)
                .body(requestBody.toString()).execute().body();
        if (StringUtils.isEmpty(respStr)) {
            log.error("请求 炳午 订单信息失败 {}", respStr);
            return null;
        }
        JSONObject resp;
        try {
            resp = JSONObject.parseObject(respStr);
        } catch (Exception e) {
            log.error("获取订单列表失败: {}", respStr);
            return null;
        }
        if (resp.getInteger("code") != 200) {
            log.error("获取订单列表失败: {}", respStr);
            return null;
        }
        return resp;
    }
}
