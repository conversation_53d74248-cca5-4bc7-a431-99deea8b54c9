package com.ruoyi.system.service.dydata.impl;

import java.util.Date;
import java.util.List;

import com.ruoyi.system.mapper.dydata.DouyinMonitorKeywordDataMapper;
import com.ruoyi.system.req.dydata.DouyinMonitorKeywordDataListReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.entity.dydata.DouyinMonitorKeywordDataEntity;
import com.ruoyi.system.service.dydata.DouyinMonitorKeywordDataService;

/**
 * 抖音监测关键词数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
public class DouyinMonitorKeywordDataServiceImpl implements DouyinMonitorKeywordDataService {

    @Autowired
    private DouyinMonitorKeywordDataMapper douyinMonitorKeywordDataMapper;

    /**
     * 查询抖音监测关键词数据
     *
     * @param id 抖音监测关键词数据主键
     * @return 抖音监测关键词数据
     */
    @Override
    public DouyinMonitorKeywordDataEntity selectDouyinMonitorKeywordDataById(Long id) {
        return douyinMonitorKeywordDataMapper.selectDouyinMonitorKeywordDataById(id);
    }

    @Override
    public DouyinMonitorKeywordDataEntity selectBy(Long keywordId, String awemeId, Date curDate) {
        return douyinMonitorKeywordDataMapper.selectBy(keywordId, awemeId, curDate);
    }

    /**
     * 查询抖音监测关键词数据列表
     *
     * @param req 抖音监测关键词数据
     * @return 抖音监测关键词数据
     */
    @Override
    public List<DouyinMonitorKeywordDataEntity> selectList(DouyinMonitorKeywordDataListReq req) {
        return douyinMonitorKeywordDataMapper.selectList(req);
    }

    /**
     * 新增抖音监测关键词数据
     *
     * @param data 抖音监测关键词数据
     * @return 结果
     */
    @Override
    public int insert(DouyinMonitorKeywordDataEntity data) {
        return douyinMonitorKeywordDataMapper.insert(data);
    }

    /**
     * 修改抖音监测关键词数据
     *
     * @param douyinMonitorKeywordData 抖音监测关键词数据
     * @return 结果
     */
    @Override
    public int update(DouyinMonitorKeywordDataEntity douyinMonitorKeywordData) {
        return douyinMonitorKeywordDataMapper.update(douyinMonitorKeywordData);
    }
}
