package com.ruoyi.system.service.wxiaa.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.JsonObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.wx.WxPublisherAdposGeneralBo;
import com.ruoyi.system.service.wxiaa.WxMaAdService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.json.GsonParser;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 微信小程序广告服务
 * <AUTHOR>
 * @date 2024/9/9 17:34
 */
@Slf4j
@Service
public class WxMaAdServiceImpl implements WxMaAdService {

    private static String PUBLISHER_STAT_URL = "https://api.weixin.qq.com/publisher/stat?action=publisher_adpos_general";

    @Override
    public WxPublisherAdposGeneralBo getPublisherStat(WxMaService wxMaService, Date day) throws WxErrorException {
        String param = StringUtils.format( "start_date={}&end_date={}&page={}&page_size={}",DateFormatUtils.format(day, "yyyy-MM-dd"),DateFormatUtils.format(day, "yyyy-MM-dd"),1,100);
        String post =wxMaService.get(PUBLISHER_STAT_URL, param);
        log.info("广告汇总数据结果:{}",post);
        JsonObject response = GsonParser.parse(post);
        if(response.has("summary")){
            return JSONObject.parseObject(response.getAsJsonObject("summary").toString(), WxPublisherAdposGeneralBo.class);
        }
        return null;
    }
}
