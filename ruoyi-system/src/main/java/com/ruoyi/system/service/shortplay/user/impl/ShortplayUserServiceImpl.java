package com.ruoyi.system.service.shortplay.user.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.ruoyi.common.config.AlipayMpProperties;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.pangolin.PangolinService;
import com.ruoyi.system.service.shortplay.app.ShortplayAppService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.shortplay.user.ShortplayUserMapper;
import com.ruoyi.system.entity.shortplay.user.ShortplayUserEntity;
import com.ruoyi.system.service.shortplay.user.ShortplayUserService;

import static com.ruoyi.common.enums.shortplay.ShortplayPlatformEnum.isAlipay;
import static com.ruoyi.common.enums.shortplay.ShortplayPlatformEnum.isDouyin;

/**
 * 短剧小程序用户Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShortplayUserServiceImpl implements ShortplayUserService {

    private static final String API_URL = "https://openapi.alipay.com/gateway.do";
    private static final Map<String, AlipayClient> alipayClientMap = new ConcurrentHashMap<>();

    @Autowired
    private ShortplayUserMapper shortplayUserMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ShortplayAppService shortplayAppService;

    @Autowired
    private PangolinService pangolinService;

    @Override
    public JSONObject getSession(Integer platform, String code, String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        if (isDouyin(platform)) {
            String componentAppid = shortplayAppService.getComponentAppidCache(appId);
            if (StringUtils.isNotBlank(componentAppid)) {
                return code2sessionV2(code, appId);
            }
            return code2session(code, appId);
        }
        if (isAlipay(platform)) {
            return aliCode2session(code, appId);
        }
        return jscode2session(code, appId);
    }

    @Override
    public JSONObject jscode2session(String code, String appId) {
        try {
            if (StringUtils.isBlank(appId)) {
                return new JSONObject();
            }

            String secret = shortplayAppService.getAppSecretCache(appId);

            String resp = HttpUtil.get(StrUtil.format("https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type=authorization_code", appId, secret, code));
            log.info("微信小程序jscode2session, code={}, resp: {}", code, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result) {
                return result;
            }
            log.error("微信小程序jscode2session失败, code={}, appId={}, resp={}", code, appId, resp);
        } catch (Exception e) {
            log.error("微信小程序jscode2session异常, code={}, appId={}", code, appId, e);
        }
        return new JSONObject();
    }

    @Override
    public JSONObject code2session(String code, String appId) {
        try {
            String secret = shortplayAppService.getAppSecretCache(appId);

            JSONObject body = new JSONObject();
            body.put("appid", appId);
            body.put("secret", secret);
            body.put("anonymous_code", "");
            body.put("code", code);
            String resp = HttpUtil.post("https://developer.toutiao.com/api/apps/v2/jscode2session", body.toString());
            log.info("抖音小程序code2session, body={}, resp: {}", body, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && result.containsKey("data")) {
                return result.getJSONObject("data");
            }
            log.error("抖音小程序code2session失败, code={}, appId={}, resp={}", code, appId, resp);
        } catch (Exception e) {
            log.error("抖音小程序code2session异常, code={}, appId={}", code, appId, e);
        }
        return new JSONObject();
    }

    @Override
    public JSONObject code2sessionV2(String code, String appId) {
        if (StringUtils.isBlank(code)) {
            return new JSONObject();
        }
        try {
            JSONObject body = new JSONObject();
            body.put("app_id", appId);
            body.put("code", code);
            String resp = HttpUtil.createPost("https://open.douyin.com/api/apps/v1/microapp/code2session/")
                    .header("content-type", "application/json")
                    .header("access-token", pangolinService.getAuthorizerAccessToken(appId))
                    .body(body.toString())
                    .execute().body();
            log.info("抖音小程序code2sessionV2, body={}, resp: {}", body, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && result.containsKey("data")) {
                return result.getJSONObject("data");
            }
            log.error("抖音小程序code2sessionV2失败, code={}, appId={}, resp={}", code, appId, resp);
        } catch (Exception e) {
            log.error("抖音小程序code2sessionV2异常, code={}, appId={}", code, appId, e);
        }
        return new JSONObject();
    }

    @Override
    public JSONObject aliCode2session(String code, String appId) {
        try {
            AlipayClient alipayClient = getAlipayClient(appId);

            AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
            request.setCode(code);
            request.setGrantType("authorization_code");
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);

            if (response.isSuccess()) {
                JSONObject result = new JSONObject();
                result.put("openid", response.getOpenId());
                result.put("access_token", response.getAccessToken());
                result.put("alipay_user_id", response.getAlipayUserId());
                result.put("user_id", response.getUserId());
                result.put("expires_in", response.getExpiresIn());
                result.put("refresh_token", response.getRefreshToken());
                result.put("re_expires_in", response.getReExpiresIn());
                result.put("auth_start", response.getAuthStart());
                return result;
            } else {
                log.error("支付宝小程序code2session失败, code={}, appId={}, resp={}", code, appId, response.getBody());
                // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
                // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                // System.out.println(diagnosisUrl);
            }
        } catch (Exception e) {
            log.error("支付宝小程序code2session异常, code={}, appId={}", code, appId, e);
        }
        return new JSONObject();
    }

    @Override
    public AlipayClient getAlipayClient(String appId) {
        AlipayMpProperties.Config config = AlipayMpProperties.getConfigByAppId(appId);
        AlipayClient alipayClient = alipayClientMap.get(appId);
        if (null == alipayClient) {
            alipayClient = new DefaultAlipayClient(API_URL, config.getAppId(), config.getPrivateKey(), "json", "UTF-8", config.getAlipayPublicKey(), "RSA2");
            alipayClientMap.put(appId, alipayClient);
        }
        return alipayClient;
    }

    @Override
    public String getSessionKey(String openid, String code, String appId) {
        String sessionKey = redisCache.getCacheObject(RedisKeyFactory.K020.join(openid));
        if (StringUtils.isBlank(sessionKey) || !checkSessionKey(openid, sessionKey, appId)) {
            JSONObject session = jscode2session(code, appId);
            sessionKey = session.getString("session_key");
            if (StringUtils.isNotBlank(sessionKey)) {
                redisCache.setCacheObject(RedisKeyFactory.K020.join(openid), sessionKey, 1, TimeUnit.DAYS);
            }
        }
        return sessionKey;
    }

    @Override
    public ShortplayUserEntity selectById(Long id) {
        if (null == id) {
            return null;
        }
        return shortplayUserMapper.selectById(id);
    }

    @Override
    public ShortplayUserEntity selectByWxOpenid(String wxOpenid) {
        if (StringUtils.isBlank(wxOpenid)) {
            return null;
        }
        return shortplayUserMapper.selectByWxOpenid(wxOpenid);
    }

    @Override
    public ShortplayUserEntity selectByTtOpenid(String ttOpenid) {
        if (StringUtils.isBlank(ttOpenid)) {
            return null;
        }
        return shortplayUserMapper.selectByTtOpenid(ttOpenid);
    }

    @Override
    public ShortplayUserEntity selectByAliOpenid(String aliOpenid) {
        if (StringUtils.isBlank(aliOpenid)) {
            return null;
        }
        return shortplayUserMapper.selectByAliOpenid(aliOpenid);
    }

    @Override
    public List<ShortplayUserEntity> selectList(ShortplayUserEntity param) {
        return shortplayUserMapper.selectList(param);
    }

    @Override
    public int insert(ShortplayUserEntity user) {
        if (StringUtils.contains(user.getClickid(), "CLICKID")) {
            user.setClickid(null);
        } else if (StringUtils.contains(user.getClickid(), ",")) {
            user.setClickid(user.getClickid().split(",")[0]);
        }
        if (StringUtils.contains(user.getPromotionId(), ",")) {
            user.setPromotionId(user.getPromotionId().split(",")[0]);
        }
        return shortplayUserMapper.insert(user);
    }

    @Override
    public int update(ShortplayUserEntity user) {
        if(Objects.isNull(user)){
            return 0;
        }
        if (StringUtils.contains(user.getClickid(), "CLICKID")) {
            user.setClickid(null);
        } else if (StringUtils.contains(user.getClickid(), ",")) {
            user.setClickid(user.getClickid().split(",")[0]);
        }
        if (StringUtils.contains(user.getPromotionId(), ",")) {
            user.setPromotionId(user.getPromotionId().split(",")[0]);
        }
        return shortplayUserMapper.update(user);
    }

    @Override
    public void updateRebackTimeAfterRegister(Long userId) {
        if ( Objects.isNull(userId) ) {
            return;
        }
        shortplayUserMapper.updateRebackTime(userId);
    }

    /**
     * 检查微信sessionKey是否有效
     */
    private boolean checkSessionKey(String openid, String sessionKey, String appId) {
        String resp = HttpUtil.get(StrUtil.format("https://api.weixin.qq.com/wxa/checksession?access_token={}&signature={}&openid={}&sig_method=SIG_METHOD",
                getAccessToken(appId), SecureUtil.hmacMd5("").digestHex(sessionKey), openid));
        if (StringUtils.isNotBlank(resp)) {
            JSONObject result = JSON.parseObject(resp);
            return Objects.equals(result.getInteger("errcode"), 0);
        }
        return false;
    }

    /**
     * 获取微信小程序accessToken
     */
    private String getAccessToken(String appId) {
        try {
            String key = RedisKeyFactory.K021.join(appId);
            String accessToken = redisCache.getCacheObject(key);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }

            String secret = shortplayAppService.getAppSecretCache(appId);

            JSONObject param = new JSONObject();
            param.put("grant_type", "client_credential");
            param.put("appid", appId);
            param.put("secret", secret);
            String resp = HttpUtil.post("https://api.weixin.qq.com/cgi-bin/stable_token", param.toString());
            log.info("短剧小程序获取accessToken, appId={}, resp={}", appId, resp);
            if (StringUtils.isNotBlank(resp)) {
                JSONObject result = JSON.parseObject(resp);
                accessToken = result.getString("access_token");
                if (StringUtils.isNotBlank(accessToken)) {
                    redisCache.setCacheObject(key, accessToken, result.getIntValue("expires_in") - 60, TimeUnit.SECONDS);
                    return accessToken;
                }
                log.error("短剧小程序获取accessToken失败, appId={}, resp={}", appId, resp);
            }
        } catch (Exception e) {
            log.error("短剧小程序获取accessToken异常, appId={}", appId, e);
        }
        return "";
    }
}
