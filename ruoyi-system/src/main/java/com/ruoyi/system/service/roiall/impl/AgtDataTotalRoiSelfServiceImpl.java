package com.ruoyi.system.service.roiall.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bytedance.ads.ApiClient;
import com.bytedance.ads.ApiException;
import com.bytedance.ads.api.ReportAdvertiserGetV2Api;
import com.bytedance.ads.model.ReportAdvertiserGetV2Response;
import com.bytedance.ads.model.ReportAdvertiserGetV2ResponseData;
import com.bytedance.ads.model.ReportAdvertiserGetV2ResponseDataListInner;
import com.bytedance.ads.model.ReportAdvertiserGetV2TimeGranularity;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.common.AgtLocalCache;
import com.ruoyi.system.common.RateLimiterEnum;
import com.ruoyi.system.common.RateLimiterFactory;
import com.ruoyi.system.dao.juliangad.AgtAccountJuliangAdvertiserDao;
import com.ruoyi.system.dao.roiall.AgtRoiDao;
import com.ruoyi.system.entity.juliangad.AdvertiserTokenEntity;
import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity;
import com.ruoyi.system.entity.user.AgtAccountUserEntity;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserSelfMapper;
import com.ruoyi.system.mapper.roiall.AgtDataTotalRoiSelfMapper;
import com.ruoyi.system.service.roiall.AgtDataTotalRoiSelfService;
import com.ruoyi.system.service.ruoyi.ISysConfigService;
import com.ruoyi.system.service.user.AgtAccountUserService;
import com.ruoyi.system.vo.playlet.roiall.DetailQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * {@code @author:} keboom
 * {@code @date:} 2024/6/28
 */
@Slf4j
@Service
public class AgtDataTotalRoiSelfServiceImpl implements AgtDataTotalRoiSelfService {

    @Autowired
    private AgtDataTotalRoiSelfMapper agtDataTotalRoiSelfMapper;
    @Autowired
    private AgtRoiDao agtRoiDao;
    @Autowired
    private AgtAccountJuliangAdvertiserDao agtAccountJuliangAdvertiserDao;
    @Autowired
    private AgtAccountUserService agtAccountUserService;
    @Autowired
    private AgtAccountJuliangAdvertiserSelfMapper agtAccountJuliangAdvertiserSelfMapper;
    //    @Autowired
//    private RedisTokenBucket redisTokenBucket;
    @Autowired
    private RateLimiterFactory rateLimiterFactory;
    @Autowired
    private ISysConfigService sysConfigService;

    ConcurrentLinkedQueue<AgtDataTotalRoiEntity> queue = new ConcurrentLinkedQueue<>();
    private final HashSet<String> dimensionSet = new HashSet<>();

    private static int pullErrorNum = 0;
    private static int pullSuccessNum = 0;

    @Autowired
    private AgtLocalCache agtLocalCache;

    /**
     * 获取广告账户数据，入库
     *
     * @param accountId
     * @param advertiserIds
     * @param accessToken
     * @throws ApiException
     */
    @Override
    public void getAdvertiserInfoAndSaveToDB(Long accountId,
                                             List<Long> advertiserIds, String accessToken, Date curDate) throws Exception {

        pullErrorNum = 0;
        pullSuccessNum = 0;

        ReportAdvertiserGetV2Api api = new ReportAdvertiserGetV2Api(new ApiClient());
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", accessToken);
        api.setApiClient(client);

        ArrayList<String> fields = new ArrayList<>();
        setFields(fields);


        List<CompletableFuture<String>> futures = new ArrayList<>();
        String currentDateStr = DateUtil.format(curDate, DatePattern.NORM_DATE_PATTERN);
        for (Long advertiserId : advertiserIds) {

            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                rateLimiterFactory.getRateLimiter(RateLimiterEnum.advertiserMetrics.getName()).acquire();

                ReportAdvertiserGetV2Response result = null;
                try {
                    for (int retry = 0; retry < 3; retry++) {
                        result = api.openApi2ReportAdvertiserGetGet(advertiserId, currentDateStr, fields,
                                null, null, null
                                , null, 1L, 24L,
                                currentDateStr, ReportAdvertiserGetV2TimeGranularity.HOURLY);
                        if (result == null || result.getData() == null || result.getData().getList() == null) {
                            log.warn("拉取广告账户数据失败 retry {} advertiserId {} accessToken {} result {} pullErrorNum {}", retry, advertiserId, accessToken, result.toString(), pullErrorNum++);
                        } else {
                            break;
                        }
                    }
                    pullRemoteAdData(result, advertiserId, accountId);
                } catch (Exception e) {
                    log.warn("拉取广告账户数据失败 advertiser{} error:{}", advertiserId, e.getMessage());
                }
                return "";
            }, GlobalThreadPool.advertiserMetricsThreadPool);

            futures.add(future);
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        while (!allOf.isDone() || !queue.isEmpty()) {
            int batchSize = 500;
            if (queue.size() < 500) {
                Thread.sleep(3000);
            }
            ArrayList<AgtDataTotalRoiEntity> saveEntities = new ArrayList<>(batchSize);
            while (!queue.isEmpty() && batchSize > 0) {
                saveEntities.add(queue.poll());
                batchSize--;
            }
            if (CollectionUtils.isNotEmpty(saveEntities)) {
                log.info("批量插入数据，size:{}", saveEntities.size());
                agtDataTotalRoiSelfMapper.batchInsertOrUpdateRoi(saveEntities);
            }
        }
    }

    /**
     * 获取广告账户数据，入库【核心方法】
     *
     * @param result
     * @param advertiserId
     * @param accountId
     */
    private void pullRemoteAdData(ReportAdvertiserGetV2Response result,
                                  Long advertiserId, Long accountId) {
        // 代理返点系数(1+返点比例，默认1.05)
        BigDecimal costBackRatio = getCostBackRatio();

//        log.info("拉取广告账户数据成功 advertiser{} code{} msg{} requestId:{} successNum:{}", advertiserId, result.getCode(), result.getMessage(), result.getRequestId(), pullSuccessNum++);
        ReportAdvertiserGetV2ResponseData responseData = result.getData();
        // 这里面是今天 24小时的数据，未来的小时的数据都是零。
        // todo 这个具体插入哪几个小时的数据后面讨论，这里先插入所有的数据
        for (ReportAdvertiserGetV2ResponseDataListInner advertiserInfo : responseData.getList()) {
            if (advertiserInfo == null) {
                continue;
            }
            // 将数据入库
            AgtDataTotalRoiEntity entity = new AgtDataTotalRoiEntity();
            entity.setCost(BigDecimal.valueOf(advertiserInfo.getCost() * 100));
            entity.setCostCash(entity.getCost().divide(costBackRatio, 2, RoundingMode.HALF_UP));
            entity.setShowPv(advertiserInfo.getShow());
            if (entity.getCost().compareTo(BigDecimal.ZERO) == 0 && entity.getShowPv() == 0) {
                continue;
            }
            entity.setAccountId(accountId);
            entity.setAdvertiserId(advertiserId);
            // 我们这个是存快照的，三分钟一次，那么我们用自己的时间了，不用接口返回的时间，接口返回的是天维度的。
            entity.setStatDatetime(advertiserInfo.getStatDatetime());
            entity.setAvgShowCost(BigDecimal.valueOf(advertiserInfo.getAvgShowCost() * 100));
            entity.setClick(advertiserInfo.getClick());
            entity.setAvgClickCost(BigDecimal.valueOf(advertiserInfo.getAvgClickCost() * 100));
            entity.setCtr(BigDecimal.valueOf(advertiserInfo.getCtr()));
            entity.setConvertUv(advertiserInfo.getConvert());
            entity.setConvertCost(BigDecimal.valueOf(advertiserInfo.getConvertCost() * 100));
            entity.setConvertRate(BigDecimal.valueOf(advertiserInfo.getConvertRate()));
            entity.setActiveCount(advertiserInfo.getActive());
            entity.setActiveCost(BigDecimal.valueOf(advertiserInfo.getActiveCost() * 100));
            // "stat_datetime": "2024-06-28 13:00:00"
            String statDatetime = advertiserInfo.getStatDatetime();
            Date date = null;
            try {
                date = DateUtils.parseDate(statDatetime, DateUtils.YYYY_MM_DD_HH_MM_SS);
            } catch (ParseException e) {
                log.error("日期转换失败，日期字符串：{}", statDatetime);
                continue;
            }
            // 精确到天，取当天的日期
            entity.setCurDate(date);
            entity.setCurHour((long) DateUtil.hour(date, true));

            queue.add(entity);
        }

    }

    private void setFields(ArrayList<String> fields) {
        fields.add("cost");
        fields.add("show");
        fields.add("avg_show_cost");
        fields.add("click");
        fields.add("avg_click_cost");
        fields.add("ctr");
        fields.add("convert");
        fields.add("convert_cost");
        fields.add("convert_rate");
        fields.add("active");
        fields.add("active_cost");
    }

    @Override
    public List<AgtDataTotalRoiEntity> getCostNotZero() {
        return agtRoiDao.selectCostNotZero();
    }

    @Override
    public List<Long> filterCostAdvertiserId() {
        return agtDataTotalRoiSelfMapper.filterCostAdvertiserId();
    }

    /**
     * 获取数据明细
     *
     * @param filterSupplierName
     * @param filterUserNickname
     * @param filterPlayletName
     * @param filterStartDate
     * @param filterEndDate
     * @param dimension
     * @param orderBy
     * @param isPage
     * @return
     */
    @Override
    public List<AgtDataTotalRoiEntity> dataDetail(List<Long> filterAdvertiserId, List<String> filterSupplierName,
                                                  List<String> filterUserNickname,
                                                  List<String> filterPlayletName, String filterStartDate,
                                                  String filterEndDate, List<String> dimension,
                                                  List<DetailQueryParam.DetailOrderItem> orderBy, boolean isPage) {

        List<String> convertedDimension = convertDimension(dimension);

        // 要对短剧名称模糊查询，对于 roi 表太大了，从广告主表中查出所有的短剧名称
        if (CollectionUtils.isNotEmpty(filterPlayletName) && filterPlayletName.size() == 1) {
            // 这个短剧名应该只有一个吧，从页面上看
            List<AgtAccountJuliangAdvertiserEntity> playletNameList = agtAccountJuliangAdvertiserDao.selectPlayletNameByPlayletName(filterPlayletName.get(0));
            filterPlayletName = playletNameList.stream().map(AgtAccountJuliangAdvertiserEntity::getPlayletName).collect(Collectors.toList());
        }

        // 是否进行分页，如果是查询接口，那就分页，如果是导出接口，那就不分页
        if (isPage) {
            TableSupport.startPage();
        }

        // 校验维度字段是否合法，并且将驼峰转化为下划线
        List<AgtDataTotalRoiEntity> agtDataTotalRoiEntities =
                agtDataTotalRoiSelfMapper.selectRoiData(filterAdvertiserId,
                        filterSupplierName, filterUserNickname, filterPlayletName,
                        filterStartDate, filterEndDate, convertedDimension, orderBy);

        // 获得投手真实名字
        List<AgtAccountUserEntity> agtAccountUserEntities = agtAccountUserService.selectAgtAccountUserList(null);
        Map<String, String> userName = agtAccountUserEntities.stream().collect(Collectors.toMap(AgtAccountUserEntity::getUserNickname, AgtAccountUserEntity::getUserRealname));
        if (CollectionUtils.isEmpty(agtDataTotalRoiEntities)) {
            return new ArrayList<>();
        }
        for (AgtDataTotalRoiEntity entity : agtDataTotalRoiEntities) {
            // 如果过滤条件查询出一些短剧名，但是 roi 表中是没有这种短剧的，那么 entity 就是 null。一般来说 7 月 15号之前的 roi 数据可能会出现此问题
            if (entity == null || entity.getCost() == null) {
                continue;
            }
            // 处理 name
            if (null != entity.getUserNickname() && userName.containsKey(entity.getUserNickname())) {
                entity.setUserRealname(userName.get(entity.getUserNickname()));
            }
            // 处理单位问题
            entity.setCost(NumberUtils.fenToYuan(entity.getCost()));
            entity.setCostCash(entity.getCost().divide(NumberUtils.COST_CASH_RATE, 2, RoundingMode.HALF_UP));
            entity.setCostIncome(NumberUtils.fenToYuan(entity.getCostIncome()));
            entity.setCostIncomeShare(NumberUtils.fenToYuan(entity.getCostIncomeShare()));
            entity.setCostIncomeAdv(NumberUtils.fenToYuan(entity.getCostIncomeAdv()));
            entity.setCostProfit(NumberUtils.fenToYuan(entity.getCostProfit()));
            entity.setCostProfitOrigin(NumberUtils.fenToYuan(entity.getCostProfitOrigin()));
            // 填入 account name 和 advertiser name
            if (entity.getAdvertiserId() != null && entity.getAdvertiserId() != 0) {
                try {
                    agtLocalCache.advertiserCache.get(entity.getAdvertiserId()).ifPresent(advertiserEntity -> {
                        entity.setAccountName(advertiserEntity.getAccountName());
                        entity.setAdvertiserName(advertiserEntity.getAdvertiserName());
                    });
                } catch (ExecutionException e) {
                    log.error("从缓存中获取广告主信息失败", e);
                }
            }
        }

        return agtDataTotalRoiEntities;
    }

    @Override
    public int deleteUselessData() {
        return agtDataTotalRoiSelfMapper.deleteUselessData();
    }

    @Override
    public void refreshAdMetrics(String content) {
        JSONObject jsonObject = JSONObject.parseObject(content);
        JSONArray advertiserIds = jsonObject.getJSONArray("advertiser_ids");
        for (int i = 0; i < advertiserIds.size(); i++) {
            Long id = advertiserIds.getLong(i);
            // 查询此账户的组织 token
            AdvertiserTokenEntity advertiserTokenEntity = agtAccountJuliangAdvertiserSelfMapper.getAdvertiserTokenEntity(String.valueOf(id));
            if (advertiserTokenEntity == null || advertiserTokenEntity.getAccessToken().isEmpty()) {
                // 巨量上报 **************** 这个账号，在数据库里面找不到，降低日志等级
                log.warn("广告主 {} 的组织 token 不存在", id);
                continue;
            }
            try {
                log.info("SPI 订阅 拉取广告主 {} 数据", id);
                getAdvertiserInfoAndSaveToDB(Long.valueOf(advertiserTokenEntity.getAccountId()),
                        Collections.singletonList(id),
                        advertiserTokenEntity.getAccessToken(),
                        DateUtil.date());
            } catch (Exception e) {
                log.error("拉取广告主{}数据失败", id, e);
            }
        }
    }

    /**
     * 校验，并转化维度字段，将驼峰转化为下划线
     *
     * @param dimension
     * @return
     */
    private List<String> convertDimension(List<String> dimension) {
        ArrayList<String> convertedList = new ArrayList<>();
        // 默认是日期维度。
        if (CollectionUtils.isEmpty(dimension)) {
            return convertedList;
        }
        if (dimensionSet.isEmpty()) {
            dimensionSet.add("advertiser_id");
            dimensionSet.add("account_id");
            dimensionSet.add("playlet_name");
            dimensionSet.add("user_nickname");
            dimensionSet.add("supplier_name_short");
            dimensionSet.add("platform_dy_wx");
            dimensionSet.add("cur_date");
        }
        for (String dim : dimension) {
            if (!dimensionSet.contains(StrUtil.toUnderlineCase(dim))) {
                throw new ServiceException("维度字段不合法" + dim);
            }
            convertedList.add(StrUtil.toUnderlineCase(dim));
        }
        return convertedList;
    }

    /**
     * 代理返点系数(1+返点比例，默认1.05)
     */
    private BigDecimal getCostBackRatio() {
        String ratio = sysConfigService.selectConfigByKey("agt.costBack.ratio");
        return new BigDecimal(StrUtil.blankToDefault(ratio, "1.05"));
    }
}
