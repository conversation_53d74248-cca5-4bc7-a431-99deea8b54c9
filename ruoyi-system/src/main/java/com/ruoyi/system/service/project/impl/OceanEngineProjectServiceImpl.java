package com.ruoyi.system.service.project.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.ruoyi.common.utils.http.DingRobotConstant;
import com.ruoyi.common.utils.http.DingRobotUtil;
import com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity;
import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.entity.user.AgtAccountUserEntity;
import com.ruoyi.system.mapper.company.AgtAccountJuliangCompanyMapper;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserMapper;
import com.ruoyi.system.mapper.user.AgtAccountUserMapper;
import com.ruoyi.system.service.juliangad.ProjectDataPullService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.project.OceanEngineProjectMapper;
import com.ruoyi.system.entity.project.OceanEngineProjectEntity;
import com.ruoyi.system.service.project.OceanEngineProjectService;

import static com.ruoyi.system.service.juliangad.impl.ProjectDataPullServiceImpl.parseNickName;

/**
 * 巨量广告账户项目Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OceanEngineProjectServiceImpl implements OceanEngineProjectService {
    @Autowired
    private OceanEngineProjectMapper oceanEngineProjectMapper;
    @Autowired
    private AgtAccountJuliangAdvertiserMapper agtAccountJuliangAdvertiserMapper;
    @Autowired
    private AgtAccountUserMapper agtAccountUserMapper;
    @Autowired
    private AgtAccountJuliangCompanyMapper agtAccountJuliangCompanyMapper;
    @Autowired
    private ProjectDataPullService projectDataPullService;

    /**
     * 查询巨量广告账户项目
     *
     * @param id 巨量广告账户项目主键
     * @return 巨量广告账户项目
     */
    @Override
    public OceanEngineProjectEntity selectOceanEngineProjectById(Long id) {
        return oceanEngineProjectMapper.selectOceanEngineProjectById(id);
    }

    /**
     * 查询巨量广告账户项目列表
     *
     * @param oceanEngineProject 巨量广告账户项目
     * @return 巨量广告账户项目
     */
    @Override
    public List<OceanEngineProjectEntity> selectOceanEngineProjectList(OceanEngineProjectEntity oceanEngineProject) {
        return oceanEngineProjectMapper.selectOceanEngineProjectList(oceanEngineProject);
    }

    /**
     * 新增巨量广告账户项目
     *
     * @param oceanEngineProject 巨量广告账户项目
     * @return 结果
     */
    @Override
    public int insertOceanEngineProject(OceanEngineProjectEntity oceanEngineProject) {

        return oceanEngineProjectMapper.insertOceanEngineProject(oceanEngineProject);
    }

    /**
     * 修改巨量广告账户项目
     *
     * @param oceanEngineProject 巨量广告账户项目
     * @return 结果
     */
    @Override
    public int updateOceanEngineProject(OceanEngineProjectEntity oceanEngineProject) {
        return oceanEngineProjectMapper.updateOceanEngineProject(oceanEngineProject);
    }

    /**
     * 批量删除巨量广告账户项目
     *
     * @param ids 需要删除的巨量广告账户项目主键
     * @return 结果
     */
    @Override
    public int deleteOceanEngineProjectByIds(Long[] ids) {
        return oceanEngineProjectMapper.deleteOceanEngineProjectByIds(ids);
    }

    /**
     * 删除巨量广告账户项目信息
     *
     * @param id 巨量广告账户项目主键
     * @return 结果
     */
    @Override
    public int deleteOceanEngineProjectById(Long id) {
        return oceanEngineProjectMapper.deleteOceanEngineProjectById(id);
    }

    @Override
    public void insertOrUpdateBatch(List<OceanEngineProjectEntity> saveOrUpdate) {
        oceanEngineProjectMapper.insertOrUpdateBatch(saveOrUpdate);
    }

    @Override
    public List<OceanEngineProjectEntity> selectEmptyAppIdProject(Long advertiserId) {
        return oceanEngineProjectMapper.selectEmptyAppIdProject(advertiserId);
    }

    @Override
    public List<OceanEngineProjectEntity> selectByProjectId(List<Long> cdpProjectIds) {
        if (cdpProjectIds.isEmpty()) {
            return new ArrayList<>();
        }
        return oceanEngineProjectMapper.selectByProjectId(cdpProjectIds);
    }

    @Override
    public void checkNotStandardOceanAdvertiser() {

        // get 投手
        AgtAccountUserEntity agtAccountUserEntity = new AgtAccountUserEntity();
        agtAccountUserEntity.setPost("2");
        List<AgtAccountUserEntity> toushouList = agtAccountUserMapper.selectAgtAccountUserList(agtAccountUserEntity);

        Set<String> toushouSet = toushouList.stream().map(AgtAccountUserEntity::getUserNickname).collect(Collectors.toSet());

        List<AgtAccountJuliangAdvertiserEntity> juliangAdvertiserEntities = agtAccountJuliangAdvertiserMapper.selectAgtAccountJuliangAdvertiserList(null);
        List<String> notStandard = new ArrayList<>();
        for (AgtAccountJuliangAdvertiserEntity entity : juliangAdvertiserEntities) {
            List<AgtAccountJuliangCompanyEntity> agtAccountJuliangCompanyEntities = projectDataPullService.getAccountInfo(entity.getAccountId());
            // 从账户名解析  诺禾短剧pj-80
            AgtAccountJuliangCompanyEntity company = projectDataPullService.parseCompanyName(entity.getAdvertiserName(), agtAccountJuliangCompanyEntities);
            if (company == null) {
                notStandard.add(entity.getAdvertiserId() + " " + entity.getAdvertiserName());
            }

            String nickName = parseNickName(entity.getAdvertiserName());
            if (!toushouSet.contains(nickName)) {
                notStandard.add(entity.getAdvertiserId() + " " + entity.getAdvertiserName());
            }
        }
        StringBuilder notice = new StringBuilder();
        for (String row : notStandard) {
            notice.append(row).append("\n");
        }
        // report WeCom
        DingRobotUtil.sendText(DingRobotConstant.ROBOT_NAME_REG,
                DingRobotConstant.JLDY_URL + notice, null, true);
    }


}
