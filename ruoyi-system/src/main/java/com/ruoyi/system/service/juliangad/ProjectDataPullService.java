package com.ruoyi.system.service.juliangad;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Pair;
import com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity;

import java.util.Date;
import java.util.List;

public interface ProjectDataPullService {


    /**
     * 拉取广告账户下的所有项目信息
     */
    void pullProjectInfo(long advertiserId);

    /**
     * advertiserId 项目id 查询该项目的 appid
     * @param advertiserId
     * @param projectId
     * @return appid
     */
    Pair<String, String> getProjectAppId(long advertiserId, long projectId);

    /**
     * 拉取广告账户下项目数据
     *
     * @param advertiserId
     */
    void pullProjectData(long advertiserId, Date startDate, Date endDate);

    /**
     * 获得组织的主体信息
     * @param accountId
     * @return
     */
    List<AgtAccountJuliangCompanyEntity> getAccountInfo(long accountId);

    /**
     * 解析公司名
     * @param advertiserName
     * @param agtAccountJuliangCompanyEntities
     * @return
     */
    AgtAccountJuliangCompanyEntity parseCompanyName(String advertiserName, List<AgtAccountJuliangCompanyEntity> agtAccountJuliangCompanyEntities);

    /**
     * 根据代理商分成，重新修改代理商和计算现金消耗
     * @param dateTime
     */
    void recalculateProjectDataByAccountJuliangCompany(DateTime dateTime);
}
