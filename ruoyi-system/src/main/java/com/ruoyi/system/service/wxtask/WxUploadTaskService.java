package com.ruoyi.system.service.wxtask;


import com.ruoyi.system.entity.wxtask.WxUploadTaskEntity;

import java.util.List;

/**
 * 微信上传任务表 Service
 *
 * <AUTHOR>
 * @date 2023-7-14 15:56:47
 */
public interface WxUploadTaskService {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean insertOrUpdate(WxUploadTaskEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    Boolean deleteById(Long id);
    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    WxUploadTaskEntity selectById(Long id);

    /**
     * 查询未完成的任务
     * @return
     */
    List<WxUploadTaskEntity> selectUnSuccessTaskListByAppId(String appId);

    /**
     * 根据id列表删除
     * @param ids
     * @return
     */
    boolean deleteByIds(List<Long> ids);
}
