package com.ruoyi.system.service.ruoyi;

import com.ruoyi.system.bo.system.SlotCacheBo;
import com.ruoyi.system.entity.system.SlotEntity;

import java.util.List;
import java.util.Map;

/**
 * 广告位Service接口
 *
 * <AUTHOR>
 */
public interface SlotService {

    /**
     * 查询广告位(缓存)
     *
     * @param key 广告位标识
     * @return 广告位
     */
    SlotCacheBo selectCacheByKey(String key);

    /**
     * 查询广告位
     *
     * @param id 广告位主键
     * @return 广告位
     */
    SlotEntity selectSlotById(Long id);

    /**
     * 查询广告位
     *
     * @param key 广告位标识
     * @return 广告位
     */
    SlotEntity selectByKey(String key);

    /**
     * 查询广告位列表
     *
     * @param slot 广告位
     * @return 广告位集合
     */
    List<SlotEntity> selectSlotList(SlotEntity slot);

    /**
     * 新增广告位
     *
     * @param slot 广告位
     * @return 结果
     */
    int insertSlot(SlotEntity slot);

    /**
     * 修改广告位
     *
     * @param slot 广告位
     * @return 结果
     */
    int updateSlot(SlotEntity slot);


    /**
     * 刷新广告位缓存
     *
     * @param slotKey
     */
    void refreshCache(String slotKey);

    /**
     * 根据广告位id列表查询广告位名称
     * @param ids
     * @return
     */
    Map<Long,String> slotNameMap(List<Long> ids);
}
