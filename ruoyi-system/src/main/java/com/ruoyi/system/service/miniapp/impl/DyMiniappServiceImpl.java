package com.ruoyi.system.service.miniapp.impl;

import com.ruoyi.system.entity.miniapp.DyMiniappEntity;
import com.ruoyi.system.mapper.miniapp.DyMiniappMapper;
import com.ruoyi.system.service.miniapp.DyMiniappService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 抖音小程序配置表 Service
 *
 * <AUTHOR>
 * @date 2024-2-5 13:56:40
 */
@Service
public class DyMiniappServiceImpl implements DyMiniappService {

    @Autowired
    private DyMiniappMapper dyMiniappMapper;

    @Override
    public Boolean insert(DyMiniappEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return dyMiniappMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return dyMiniappMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(DyMiniappEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return dyMiniappMapper.updateById(entity) > 0;
    }

    @Override
    public DyMiniappEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return dyMiniappMapper.selectById(id);
    }

    @Override
    public List<DyMiniappEntity> selectList() {
        return dyMiniappMapper.selectList();
    }

    @Override
    public List<String> selectAppIdList() {
        return dyMiniappMapper.selectAppIdList();
    }

    @Override
    public DyMiniappEntity selectByAppId(String appId) {
        return dyMiniappMapper.selectByAppId(appId);
    }
}
