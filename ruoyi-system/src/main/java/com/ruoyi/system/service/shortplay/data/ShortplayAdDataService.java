package com.ruoyi.system.service.shortplay.data;

import com.ruoyi.system.entity.shortplay.data.ShortplayAdDataEntity;

import java.util.Date;
import java.util.List;

/**
 * 短剧小程序广告数据Service接口
 *
 * <AUTHOR>
 */
public interface ShortplayAdDataService {

    /**
     * 查询短剧小程序广告数据ID
     */
    Long selectIdBy(Date curDate, String miniappId, Long tvId, Integer series);

    /**
     * 查询短剧小程序广告数据
     *
     * @param id 短剧小程序广告数据主键
     * @return 短剧小程序广告数据
     */
    ShortplayAdDataEntity selectShortplayAdDataById(Long id);

    /**
     * 查询短剧小程序广告数据列表
     *
     * @param shortplayAdData 短剧小程序广告数据
     * @return 短剧小程序广告数据集合
     */
    List<ShortplayAdDataEntity> selectShortplayAdDataList(ShortplayAdDataEntity shortplayAdData);

    /**
     * 新增短剧小程序广告数据
     *
     * @param shortplayAdData 短剧小程序广告数据
     * @return 结果
     */
    int insert(ShortplayAdDataEntity shortplayAdData);

    /**
     * 修改短剧小程序广告数据
     *
     * @param shortplayAdData 短剧小程序广告数据
     * @return 结果
     */
    int update(ShortplayAdDataEntity shortplayAdData);
}
