package com.ruoyi.system.service.dydata;

import java.util.Date;
import java.util.List;
import com.ruoyi.system.entity.dydata.DouyinMonitorKeywordDataEntity;
import com.ruoyi.system.req.dydata.DouyinMonitorKeywordDataListReq;

/**
 * 抖音监测关键词数据Service接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface DouyinMonitorKeywordDataService {
    /**
     * 查询抖音监测关键词数据
     *
     * @param id 抖音监测关键词数据主键
     * @return 抖音监测关键词数据
     */
    public DouyinMonitorKeywordDataEntity selectDouyinMonitorKeywordDataById(Long id);

    DouyinMonitorKeywordDataEntity selectBy(Long keywordId, String awemeId, Date curDate);

    /**
     * 查询抖音监测关键词数据列表
     *
     * @param req 抖音监测关键词数据
     * @return 抖音监测关键词数据集合
     */
    public List<DouyinMonitorKeywordDataEntity> selectList(DouyinMonitorKeywordDataListReq req);

    /**
     * 新增抖音监测关键词数据
     *
     * @param data 抖音监测关键词数据
     * @return 结果
     */
    int insert(DouyinMonitorKeywordDataEntity data);

    /**
     * 修改抖音监测关键词数据
     *
     * @param douyinMonitorKeywordData 抖音监测关键词数据
     * @return 结果
     */
    public int update(DouyinMonitorKeywordDataEntity douyinMonitorKeywordData);
}
