package com.ruoyi.system.service.charge;


import com.ruoyi.system.bo.playlet.AppChargeRecordSumBo;
import com.ruoyi.system.bo.playlet.ChargeRecordSumBo;
import com.ruoyi.system.entity.charge.ChargeRecordEntity;
import com.ruoyi.system.req.playlet.param.ChargeRecordParam;

import java.util.List;
import java.util.Map;

/**
 * 充值记录 Service
 *
 * <AUTHOR>
 * @date 2023-7-4 19:35:48
 */
public interface ChargeRecordService {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean insert(ChargeRecordEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    Boolean updateById(ChargeRecordEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    ChargeRecordEntity selectById(Long id);

    /**
     * 查询充值总金额
     *
     * @param userIds
     * @return
     */
    Map<Long,Integer> selectChargeSum(List<Long> userIds);

    /**
     * 根据请求参数查询列表
     *
     * @param param
     * @return
     */
    List<ChargeRecordEntity> selectListByReq(ChargeRecordParam param);

    /**
     * 充值总金额汇总
     * @param param
     * @return
     */
    ChargeRecordSumBo chargeRecordSum(ChargeRecordParam param);

    /**
     * 根据id列表获取
     *
     * @param ids id
     * @return 结果
     */
    List<ChargeRecordEntity> selectListByIds(List<Long> ids);

    /**
     * app充值金额汇总
     * @param param
     * @return
     */
    AppChargeRecordSumBo appChargeRecordSum(ChargeRecordParam param);

    /**
     * 根据条件分页查询充值记录
     * @param chargeRecordParam
     * @param lastId
     * @param limit
     * @return
     */
    List<ChargeRecordEntity> selectListPageByParam(ChargeRecordParam chargeRecordParam,Long lastId,Integer limit);
}
