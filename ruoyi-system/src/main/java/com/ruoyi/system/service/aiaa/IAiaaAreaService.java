package com.ruoyi.system.service.aiaa;


import com.ruoyi.system.entity.aiaa.AiaaAreaEntity;

import java.util.List;


/**
 * 行政区划代码Service接口
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface IAiaaAreaService {

    /**
     * 查询行政区划代码列表
     *
     * @param aiaaAreaEntity 行政区划代码
     * @return 行政区划代码集合
     */
    public List<AiaaAreaEntity> selectAiaaAreaList(AiaaAreaEntity aiaaAreaEntity);


    /**
     * 批量修改行政区划可见性
     * @param req
     * @return
     */
    boolean changeVisibleList(List<Integer> req);


    /**
     * 根据名字查询对应行政区
     * @param areaName
     * @return
     */
    List<AiaaAreaEntity> selectAiaaAreaByName(String areaName);

    /**
     * 查询不可见的地域
     * @return
     */
    List<AiaaAreaEntity> invisibleAreaList();

    /**
     * 根据地区编码查询
     * @param adcode 地区编码
     * @return
     */
    AiaaAreaEntity selectAiaaAreaByAreaNum(Integer adcode);

    /**
     * 设置所有地区是否可见
     */
    boolean setVisible(boolean visible);
}
