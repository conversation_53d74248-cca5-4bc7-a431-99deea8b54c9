package com.ruoyi.system.service.shortplay.record.impl;

import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.util.CollectionUtils;

import com.ruoyi.system.service.shortplay.record.ShortplayUnlockRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.shortplay.record.ShortplayUnlockRecordMapper;
import com.ruoyi.system.entity.shortplay.record.ShortplayUnlockRecordEntity;

/**
 * 短剧小程序解锁记录Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ShortplayUnlockRecordServiceImpl implements ShortplayUnlockRecordService {

    @Autowired
    private ShortplayUnlockRecordMapper shortplayUnlockRecordMapper;

    @Override
    public Set<Integer> selectSeriesByUserIdAndTvId(Long userId, Long tvId) {
        if (null == userId || null == tvId) {
            return Collections.emptySet();
        }
        return new HashSet<>(shortplayUnlockRecordMapper.selectSeriesByUserIdAndTvId(userId, tvId));
    }

    @Override
    public Integer countByUserIdAndDate(Long userId, Date startDate, Date endDate) {
        if (null == userId || null == startDate || null == endDate) {
            return 0;
        }
        return shortplayUnlockRecordMapper.countByUserIdAndDate(userId, startDate, endDate);
    }

    @Override
    public List<ShortplayUnlockRecordEntity> selectList(ShortplayUnlockRecordEntity param) {
        return shortplayUnlockRecordMapper.selectList(param);
    }

    @Override
    public int insert(ShortplayUnlockRecordEntity record) {
        return shortplayUnlockRecordMapper.insert(record);
    }

    @Override
    public int batchInsert(List<ShortplayUnlockRecordEntity> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        return shortplayUnlockRecordMapper.batchInsert(records);
    }

    @Override
    public int update(ShortplayUnlockRecordEntity record) {
        return shortplayUnlockRecordMapper.update(record);
    }
}
