package com.ruoyi.system.service.youhe.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.DingRobotConstant;
import com.ruoyi.common.utils.http.DingRobotUtil;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.entity.fundStat.SupplierIncomeItem;
import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity;
import com.ruoyi.system.entity.supplier.SupplierEnum;
import com.ruoyi.system.entity.youhe.AgtDataSupplierSnapshotYouheEntity;
import com.ruoyi.system.entity.youhe.YouheResponse;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserMapper;
import com.ruoyi.system.mapper.youhe.AgtDataSupplierSnapshotYouheSelfMapper;
import com.ruoyi.system.service.SupplierService;
import com.ruoyi.system.service.youhe.AgtDataSupplierSnapshotYouheSelfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * {@code @author:} keboom
 * {@code @date:} 2024/6/28
 */
@Slf4j
@Service
public class AgtDataSupplierSnapshotYouheSelfServiceImpl implements AgtDataSupplierSnapshotYouheSelfService, SupplierService {

    @Value("${youhe.wechat_id}")
    private String wechatId;
    @Value("${youhe.app_key}")
    private String appKey;
    @Value("${youhe.secret}")
    private String secret;
    @Value("${youhe.username}")
    private String username;
    @Value("${youhe.password}")
    private String password;
    @Value("${youhe.username2}")
    private String username2;
    @Value("${youhe.password2}")
    private String password2;
    @Value("${youhe.wechat_id2}")
    private String wechatId2;
    @Value("${youhe.host}")
    private String host;

    private static final int pageSize = 20;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private AgtAccountJuliangAdvertiserMapper agtAccountJuliangAdvertiserMapper;
    @Autowired
    private AgtDataSupplierSnapshotYouheSelfMapper agtDataSupplierSnapshotYouheSelfMapper;

    Map<Long, String> promotionIdName;

    /**
     * @param startTime
     * @param endTime
     */
    @Override
    @Async
    public void getOrderInfoSaveToDB(Date startTime, Date endTime) {
        // todo 现在友和有两个账号，那么现在同时拉取两个账号的订单数据
        // 这是第一个账号
        StringBuilder param = new StringBuilder();
        param.append("wechat_id=").append(wechatId)
                .append("&app_key=").append(appKey)
                .append("&secret=").append(secret)
                .append("&username=").append(username)
                .append("&password=").append(password)
                .append("&num=").append(pageSize);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
        if (startTime != null) {
            param.append("&start_time=").append(simpleDateFormat.format(startTime));
        }
        if (endTime != null) {
            param.append("&end_time=").append(simpleDateFormat.format(endTime));
        }
        log.info("友和订单数据开始，拉取时间：start:{} end:{}", startTime.toString(), endTime.toString());
        String response = HttpUtils.sendGet(host + "/third/order/getOrderLists", param.toString());
        YouheResponse youheResponse = JSONObject.parseObject(response, YouheResponse.class);
        List<YouheResponse.OrderData> orderDataList = youheResponse.getResult().getData();

        List<AgtDataSupplierSnapshotYouheEntity> newestPromotionName =
                agtDataSupplierSnapshotYouheSelfMapper.getNewestPromotionName(DateUtil.format(startTime, DateUtils.YYYY_MM_DD));

        promotionIdName = newestPromotionName
                .stream()
                .collect(Collectors.toMap(AgtDataSupplierSnapshotYouheEntity::getPromotionsId,
                        AgtDataSupplierSnapshotYouheEntity::getPromotionsName));
        saveDataToDB(orderDataList);

        int curPage = youheResponse.getResult().getCurPage();
        int totalPage = youheResponse.getResult().getTotalPage();
        while (curPage < totalPage) {
            curPage++;
            StringBuilder sb = new StringBuilder(param.toString());
            sb.append("&page=").append(curPage);
            response = HttpUtils.sendGet(host + "/third/order/getOrderLists", sb.toString());
            youheResponse = JSONObject.parseObject(response, YouheResponse.class);
            orderDataList = youheResponse.getResult().getData();
            saveDataToDB(orderDataList);
        }

        // 这是第二个账号 -----------------------------------------------------------------------------------
        StringBuilder param2 = new StringBuilder();
        param2.append("wechat_id=").append(wechatId2)
                .append("&app_key=").append(appKey)
                .append("&secret=").append(secret)
                .append("&username=").append(username2)
                .append("&password=").append(password2)
                .append("&num=").append(pageSize);

        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
        if (startTime != null) {
            param2.append("&start_time=").append(simpleDateFormat2.format(startTime));
        }
        if (endTime != null) {
            param2.append("&end_time=").append(simpleDateFormat2.format(endTime));
        }

        String response2 = HttpUtils.sendGet(host + "/third/order/getOrderLists", param2.toString());
        YouheResponse youheResponse2 = JSONObject.parseObject(response2, YouheResponse.class);
        List<YouheResponse.OrderData> orderDataList2 = youheResponse2.getResult().getData();

        saveDataToDB(orderDataList2);

        int curPage2 = youheResponse2.getResult().getCurPage();
        int totalPage2 = youheResponse2.getResult().getTotalPage();
        while (curPage2 < totalPage2) {
            curPage2++;
            StringBuilder sb = new StringBuilder(param2.toString());
            sb.append("&page=").append(curPage2);
            response2 = HttpUtils.sendGet(host + "/third/order/getOrderLists", sb.toString());
            youheResponse2 = JSONObject.parseObject(response2, YouheResponse.class);
            orderDataList2 = youheResponse2.getResult().getData();
            saveDataToDB(orderDataList2);
        }
    }

    @Override
    public HashMap<String, SupplierIncomeItem> getDateSuppItemMap(String startTime, String endTime) {
        HashMap<String, SupplierIncomeItem> dateSuppItemMap = new HashMap<>();
        List<AgtDataSupplierSnapshotYouheEntity> entities = agtDataSupplierSnapshotYouheSelfMapper.selectIncomeByDay(startTime, endTime);
        for (AgtDataSupplierSnapshotYouheEntity youheEntity : entities) {
            SupplierIncomeItem item = new SupplierIncomeItem();
            item.setAmount(NumberUtil.div(BigDecimal.valueOf(youheEntity.getTotalFee()), 100, 2, RoundingMode.HALF_UP));
            item.setSupplier(SupplierEnum.YOUHE.getName());
            dateSuppItemMap.put(DateUtil.formatDate(youheEntity.getCurDate()), item);
        }
        return dateSuppItemMap;
    }

    /**
     * 执行批量插入，如果有插入时某条重复数据，导致插入失败，将会影响此 list 中其他要插入的数据
     *
     * @param orderDataList
     * @throws ParseException
     */
    private void saveDataToDB(List<YouheResponse.OrderData> orderDataList) {
        if (CollectionUtils.isEmpty(orderDataList)) {
            return;
        }


        ArrayList<AgtDataSupplierSnapshotYouheEntity> insertList = new ArrayList<>(pageSize);
        Set<String> setDing = new HashSet<>();
        // 获取所有的广告账户id
        List<AgtAccountJuliangAdvertiserEntity> advertiserList = agtAccountJuliangAdvertiserMapper.selectAgtAccountJuliangAdvertiserList(null);
        Set<String> advertiserSet = advertiserList.stream()
                .map(dto -> dto.getAdvertiserId() + "").collect(Collectors.toSet());

        for (YouheResponse.OrderData orderData : orderDataList) {
            AgtDataSupplierSnapshotYouheEntity entity = new AgtDataSupplierSnapshotYouheEntity();
            BeanUtils.copyProperties(orderData, entity);
            // "create_time": "2024-06-27 15:56:32"
            String createTime = orderData.getCreateTime();
            entity.setCreateTimeStr(createTime);

            // 1.告警通知：****************-张小虎，查询Id是否存在
            String promotionsName = orderData.getPromotionsName();
            String accountNickname = orderData.getAccountNickname();

            // 校验命名规范：根据promotions_id
            if (StringUtils.isNotEmpty(orderData.getPromotionsId())
                    && StringUtils.isNotEmpty(promotionIdName.get(Long.parseLong(orderData.getPromotionsId())))) {
                promotionsName = promotionIdName.get(Long.parseLong(orderData.getPromotionsId()));
            }
            String advertiserId = getLegalAdvertiserId(promotionsName);
            if (StringUtils.isEmpty(advertiserId)) {
                // 一些账户promotionsName可能为：张小虎-****************
                advertiserId = getUnLegalAdvertiserId(promotionsName);
                setDing.add(accountNickname + "，名称：" + promotionsName);
                // 钉钉通知
//                continue;
            }
            if (!advertiserSet.contains(advertiserId)) {
                setDing.add(accountNickname + "，id写错名称：" + promotionsName);
            }

            // 2.ua判断处理
            long androidIos = getAndroidIos(orderData);
            entity.setAndroidIos(androidIos);

            String[] dateSplit = createTime.split(" ");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            try {
                entity.setCurDate(simpleDateFormat.parse(dateSplit[0]));
            } catch (ParseException e) {
                log.error("友和订单数据插入，日期转换异常：{}", e.getMessage());
                continue;
            }
            String[] hourSplit = dateSplit[1].split(":");
            entity.setCurHour(hourSplit[0]);

            entity.setAdvertiserId(Long.valueOf(Objects.equals(advertiserId, "") ? "0" : advertiserId));

            entity.setUserId(Long.parseLong(orderData.getUserId()));
            entity.setWechatId(Long.parseLong(orderData.getWechatId()));
            entity.setPromotionsId(Long.parseLong(orderData.getPromotionsId()));
            entity.setTotalFee(Long.parseLong(orderData.getTotalFee()));
            entity.setAccountId(Long.parseLong(orderData.getAccountId()));
            // nickname 说格式是 advertiserId-nickname，
            entity.setAccountNickname(accountNickname);

            insertList.add(entity);
        }

        // 钉钉通知：友和命名规范问题
        checkAndSendDinDing(setDing);

        log.info("友和订单数据插入size：{}", insertList.size());
        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }

        agtDataSupplierSnapshotYouheSelfMapper.saveOrUpdateBatch(insertList);
    }

    private String getUnLegalAdvertiserId(String promotionsName) {
        if (StringUtils.isEmpty(promotionsName)) {
            return "";
        }
        String[] nameSplit = promotionsName.split("-");
        for (String s : nameSplit) {
            // 这种格式基本上可能是广告账户id
            if (s.length() > 14 && NumberUtil.isLong(s)) {
                return s;
            }
        }
        return "";
    }

    /**
     * 判断用户是安卓，还是ios
     *
     * @return
     */
    private long getAndroidIos(YouheResponse.OrderData orderData) {
        String ua = orderData.getUa();
        long androidIos = 1L;
        // ua不为空，可判断
        if (StringUtils.isNotEmpty(ua)) {
            if (ua.length() >= 50) {
                ua = ua.substring(0, 50);
            }
            ua = ua.toLowerCase();
            if (ua.contains("android")) {
                return androidIos;
            }
            if (ua.contains("iphone") || ua.contains("ios")) {
                return 2L;
            }
        }
        // 不可判断，走支付判断
        String orderChannel = orderData.getOrderChannel();
        if (StringUtils.isNotEmpty(orderChannel) && orderChannel.contains("微信")) {
            return 2L;
        }

        return androidIos;
    }

    /**
     * 钉钉通知：友和命名规范问题
     */
    private void checkAndSendDinDing(Set<String> setDing) {
        if (CollectionUtils.isEmpty(setDing)) {
            return;

        }
        StringBuilder dingMsgSend = new StringBuilder();
        for (String s : setDing) {
            dingMsgSend.append(s).append("; \n");
        }
        String key = RedisKeyFactory.K041.join("youhe");
        redisCache.setCacheObject(key, "1", 10, TimeUnit.MINUTES);
        if (dingMsgSend.length() == 0) {
            return;
        }
        DingRobotUtil.sendText(DingRobotConstant.ROBOT_NAME_REG,
                DingRobotConstant.YOUHE_URL + dingMsgSend.toString(), null, true);
    }

    /**
     * 校验命名规范，钉钉通知
     * 十分钟通知1次，redisKey
     *
     * @return
     */
    private String getLegalAdvertiserId(String promotionsName) {
        // 去除空格
        promotionsName = promotionsName.replaceAll("\\s+", "");
        if (!StringUtils.contains(promotionsName, "-")) {
            return "";
        }
        String[] nameSplit = promotionsName.split("-");
        String advertiserId = nameSplit[0];
        // 前面是广告账户id
        if (!NumberUtil.isLong(advertiserId)) {
            return "";
        }
        return advertiserId;
    }
}
