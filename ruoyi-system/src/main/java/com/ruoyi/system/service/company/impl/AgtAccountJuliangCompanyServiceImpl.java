package com.ruoyi.system.service.company.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.system.entity.company.AccountCompanyNumEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.company.AgtAccountJuliangCompanyMapper;
import com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity;
import com.ruoyi.system.service.company.AgtAccountJuliangCompanyService;

/**
 * 巨量组织与主体关系Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class AgtAccountJuliangCompanyServiceImpl implements AgtAccountJuliangCompanyService {
    @Autowired
    private AgtAccountJuliangCompanyMapper agtAccountJuliangCompanyMapper;

    /**
     * 查询巨量组织与主体关系
     *
     * @param id 巨量组织与主体关系主键
     * @return 巨量组织与主体关系
     */
    @Override
    public AgtAccountJuliangCompanyEntity selectAgtAccountJuliangCompanyById(Integer id)
    {
        return agtAccountJuliangCompanyMapper.selectAgtAccountJuliangCompanyById(id);
    }

    /**
     * 查询巨量组织与主体关系列表
     *
     * @param agtAccountJuliangCompany 巨量组织与主体关系
     * @return 巨量组织与主体关系
     */
    @Override
    public List<AgtAccountJuliangCompanyEntity> selectAgtAccountJuliangCompanyList(AgtAccountJuliangCompanyEntity agtAccountJuliangCompany)
    {
        return agtAccountJuliangCompanyMapper.selectAgtAccountJuliangCompanyList(agtAccountJuliangCompany);
    }

    /**
     * 新增巨量组织与主体关系
     *
     * @param agtAccountJuliangCompany 巨量组织与主体关系
     * @return 结果
     */
    @Override
    public int insertAgtAccountJuliangCompany(AgtAccountJuliangCompanyEntity agtAccountJuliangCompany)
    {

            return agtAccountJuliangCompanyMapper.insertAgtAccountJuliangCompany(agtAccountJuliangCompany);
    }

    /**
     * 修改巨量组织与主体关系
     *
     * @param agtAccountJuliangCompany 巨量组织与主体关系
     * @return 结果
     */
    @Override
    public int updateAgtAccountJuliangCompany(AgtAccountJuliangCompanyEntity agtAccountJuliangCompany)
    {
        return agtAccountJuliangCompanyMapper.updateAgtAccountJuliangCompany(agtAccountJuliangCompany);
    }

    /**
     * 批量删除巨量组织与主体关系
     *
     * @param ids 需要删除的巨量组织与主体关系主键
     * @return 结果
     */
    @Override
    public int deleteAgtAccountJuliangCompanyByIds(Integer[] ids)
    {
        return agtAccountJuliangCompanyMapper.deleteAgtAccountJuliangCompanyByIds(ids);
    }

    /**
     * 删除巨量组织与主体关系信息
     *
     * @param id 巨量组织与主体关系主键
     * @return 结果
     */
    @Override
    public int deleteAgtAccountJuliangCompanyById(Integer id)
    {
        return agtAccountJuliangCompanyMapper.deleteAgtAccountJuliangCompanyById(id);
    }

    @Override
    public Map<Long, Integer> queryAccountCompanyNum() {
        List<AccountCompanyNumEntity> accountCompanyNumEntities = agtAccountJuliangCompanyMapper.queryAccountCompanyNum();
        Map<Long, Integer> accountCompanyNum = accountCompanyNumEntities.stream().collect(Collectors.toMap(AccountCompanyNumEntity::getAccountId, AccountCompanyNumEntity::getCompanyNum));

        return accountCompanyNum;

    }
}
