package com.ruoyi.system.service.dyiaa;

import com.ruoyi.system.entity.dyiaa.TbDouyinIaaConversionConfig;

import java.util.List;

/**
 * 抖音IAA回传配置Service接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface ITbDouyinIaaConversionConfigService {

    /**
     * 查询抖音IAA回传配置
     *
     * @param configId 回传配置ID
     * @return 抖音IAA回传配置
     */
    TbDouyinIaaConversionConfig selectByConfigId(String configId);

    /**
     * 查询抖音IAA回传配置列表
     *
     * @param tbDouyinIaaConversionConfig 抖音IAA回传配置
     * @return 抖音IAA回传配置集合
     */
    List<TbDouyinIaaConversionConfig> selectTbDouyinIaaConversionConfigList(TbDouyinIaaConversionConfig tbDouyinIaaConversionConfig);

    /**
     * 查询固定收益上报配置
     */
    List<TbDouyinIaaConversionConfig> selectFixedCostConfigList();

    /**
     * 新增抖音IAA回传配置
     *
     * @param tbDouyinIaaConversionConfig 抖音IAA回传配置
     * @return 结果
     */
    int insertTbDouyinIaaConversionConfig(TbDouyinIaaConversionConfig tbDouyinIaaConversionConfig);

    /**
     * 修改抖音IAA回传配置
     *
     * @param tbDouyinIaaConversionConfig 抖音IAA回传配置
     * @return 结果
     */
    int updateTbDouyinIaaConversionConfig(TbDouyinIaaConversionConfig tbDouyinIaaConversionConfig);
}
