package com.ruoyi.system.service.pangolin;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.util.Date;

/**
 * 抖音小程序数据接口Service
 *
 * <AUTHOR>
 * @date 2025/02/21
 */
public interface PangolinDataService {

    /**
     * 查询广告收入
     * https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/basic-abilities/traffic-permission/query-ad-income
     *
     * @param appId 小程序ID
     * @param date 日期
     * @return 收入列表
     */
    JSONArray queryAdIncome(String appId, Date date, Boolean isComponent);

    /**
     * 查询用户行为数据
     * https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/operation/data-analysis/user-analysis/behavior-analysis-datav3
     *
     * @param appId 小程序ID
     * @param date 日期
     * @return 用户行为数据
     */
    JSONObject queryBehaviorData(String appId, Date date, Boolean isComponent);
}
