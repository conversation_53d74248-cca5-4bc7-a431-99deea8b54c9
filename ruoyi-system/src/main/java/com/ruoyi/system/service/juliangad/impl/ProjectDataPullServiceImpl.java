package com.ruoyi.system.service.juliangad.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.NumberUtil;
import com.bytedance.ads.ApiClient;
import com.bytedance.ads.ApiException;
import com.bytedance.ads.api.ProjectListV30Api;
import com.bytedance.ads.api.PromotionListV30Api;
import com.bytedance.ads.api.ReportCustomGetV30Api;
import com.bytedance.ads.model.*;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.company.AgtAccountJuliangCompanyEntity;
import com.ruoyi.system.entity.data.OceanEngineProjectDateDataEntity;
import com.ruoyi.system.entity.juliang.AgtAccountJuliangEntity;
import com.ruoyi.system.entity.juliangad.AdvertiserTokenEntity;
import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.entity.project.OceanEngineProjectEntity;
import com.ruoyi.system.mapper.company.AgtAccountJuliangCompanyMapper;
import com.ruoyi.system.mapper.data.OceanEngineProjectDateDataMapper;
import com.ruoyi.system.mapper.juliang.AgtAccountJuliangMapper;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserMapper;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserSelfMapper;
import com.ruoyi.system.service.juliangad.ProjectDataPullService;
import com.ruoyi.system.service.project.OceanEngineProjectService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: keboom
 * @date: 2025/2/20
 * <p>
 * 巨量项目数据拉取服务
 */
@Slf4j
@Service
public class ProjectDataPullServiceImpl implements ProjectDataPullService {

    @Autowired
    private OceanEngineProjectService oceanEngineProjectService;
    @Autowired
    private AgtAccountJuliangAdvertiserSelfMapper agtAccountJuliangAdvertiserSelfMapper;
    @Autowired
    private OceanEngineProjectDateDataMapper projectDateDataMapper;
    @Autowired
    private AgtAccountJuliangMapper juliangMapper;
    @Autowired
    private AgtAccountJuliangAdvertiserMapper juliangAdvertiserMapper;
    @Autowired
    private AgtAccountJuliangCompanyMapper agtAccountJuliangCompanyMapper;


    @SneakyThrows
    @Override
    public void pullProjectInfo(long advertiserId) {
        String token = getAdvertiserToken(advertiserId);
        if (StringUtils.isEmpty(token)) return;

        ProjectListV30Api api = new ProjectListV30Api(new ApiClient());
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", token);
        api.setApiClient(client);

        /**
         *     "fields": [
         *         "name",
         *         "project_id",
         *         "status",
         *         "status_first",
         *         "project_create_time",
         *         "project_modify_time",
         *         "delivery_product",
         *         "delivery_product",
         *         "micro_promotion_type"
         *     ],
         */
        List<String> fields = new ArrayList<>();
        fields.add("name");
        fields.add("project_id");
        fields.add("status");
        fields.add("status_first");
        fields.add("project_create_time");
        fields.add("project_modify_time");
        fields.add("micro_promotion_type");

        ProjectListV30Filtering filtering = new ProjectListV30Filtering();

        // 这里直接写 100 就好了，巨量单个账户下的项目数，不会超过 100


        ProjectListV30Response projectListV30Response = null;
        int retryTimes = 5;
        while (retryTimes-- > 0) {
            projectListV30Response = api.openApiV30ProjectListGet(advertiserId, fields, filtering, 1L, 100L);
            Long code = projectListV30Response.getCode();
            if (code == null) {
                log.error("Get project failed. advertiser: {} resp:{}", advertiserId, projectListV30Response.toString());
                return;
            }
            if (code == 40100) {
                log.warn("Get project failed. advertiser: {} resp:{} 超频，重新请求。", advertiserId, projectListV30Response.toString());
                continue;
            }
            if (code == 0) {
                break;
            }
        }

        ProjectListV30ResponseData data = projectListV30Response.getData();
        assert data != null;
        List<ProjectListV30ResponseDataListInner> list = data.getList();
        assert list != null;
        ArrayList<OceanEngineProjectEntity> saveOrUpdate = new ArrayList<>();
        for (ProjectListV30ResponseDataListInner listInner : list) {
            String name = listInner.getName();
            Long projectId = listInner.getProjectId();
            String status = Objects.requireNonNull(listInner.getStatus()).getValue();
            String statusFirst = Objects.requireNonNull(listInner.getStatusFirst()).getValue();
            String projectCreateTime = listInner.getProjectCreateTime();
            String projectModifyTime = listInner.getProjectModifyTime();
            String microPromotionType = Objects.requireNonNull(listInner.getMicroPromotionType()).getValue();

            OceanEngineProjectEntity entity = new OceanEngineProjectEntity();
            entity.setProjectId(projectId);
            entity.setProjectName(name);
            entity.setProjectStatus(status);
            entity.setStatusFirst(statusFirst);
            entity.setProjectCreateTime(DateUtil.parseDateTime(projectCreateTime));
            entity.setProjectModifyTime(DateUtil.parseDateTime(projectModifyTime));
            entity.setMicroPromotionType(microPromotionType);
            entity.setAdvertiserId(advertiserId);

            saveOrUpdate.add(entity);
        }

        if (saveOrUpdate.isEmpty()) {
            return;
        }

        oceanEngineProjectService.insertOrUpdateBatch(saveOrUpdate);
    }

    @Nullable
    private String getAdvertiserToken(long advertiserId) {
        // get the advertiser token
        AdvertiserTokenEntity advertiserTokenEntity = agtAccountJuliangAdvertiserSelfMapper.getAdvertiserTokenEntity(String.valueOf(advertiserId));

        if (advertiserTokenEntity == null || advertiserTokenEntity.getAccessToken().isEmpty()) {
            // 巨量上报 **************** 这个账号，在数据库里面找不到，降低日志等级
            log.warn("广告主 {} 的组织 token 不存在", advertiserId);
            return "";
        }
        return advertiserTokenEntity.getAccessToken();
    }

    @SneakyThrows
    @Override
    public Pair<String, String> getProjectAppId(long advertiserId, long projectId) {

        String token = getAdvertiserToken(advertiserId);
        if (StringUtils.isEmpty(token)) {
            return new Pair<>("", "");
        }

        PromotionListV30Api api = new PromotionListV30Api(new ApiClient());
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", token);
        api.setApiClient(client);

        List<String> fields = new ArrayList<>();
        fields.add("promotion_materials");

        PromotionListV30Filtering filtering = new PromotionListV30Filtering();
        filtering.setProjectId(projectId);

        PromotionListV30Response response = null;
        int retryTimes = 5;
        while (retryTimes-- > 0) {
            response = api.openApiV30PromotionListGet(advertiserId, filtering, fields, null, 1L, 1L, null, null);
            Long code = response.getCode();
            if (code == null) {
                log.error("Get project failed. advertiser: {} resp:{}", advertiserId, response.toString());
                return new Pair<>("", "");
            }
            if (code == 40100) {
                log.warn("Get project failed. advertiser: {} resp:{} 超频，重新请求。", advertiserId, response.toString());
                continue;
            }
            if (code == 0) {
                break;
            }
        }

        Long code = response.getCode();
        if (code != 0) {
            log.error("Get project failed. advertiser: {} resp:{}", advertiserId, response.toString());
            return new Pair<>("", "");
        }

        String appId = Optional.ofNullable(response.getData())
                .map(PromotionListV30ResponseData::getList)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(PromotionListV30ResponseDataListInner::getPromotionMaterials)
                .map(PromotionListV30ResponseDataListInnerPromotionMaterials::getMiniProgramInfo)
                .map(PromotionListV30ResponseDataListInnerPromotionMaterialsMiniProgramInfo::getAppId)
                .orElse("");

        if (StringUtils.isEmpty(appId)) {
            log.warn("Get project failed. appId is empty. advertiser: {} resp:{}", advertiserId, response.toString());
        }

        // url demo: sslocal://microapp?app_id=ttff7a89c7604f7b4c01&bdp_log=%7B%22launch_from%22%3A%22ad%22%2C%22location%22%3A%22%22%7D&scene=0&start_page=pages%2Findex%2Findex%3Fadvertiser_id%3D__ADVERTISER_ID__%26albumid%3D7350542944874529318%26invite_code%3Da40fc074675951a8026cfa7459f230c7%26middleman_id%3D3fa9b3126617a6e206c06848766963cc%26path%3Dplayer%26series%3D1%26tfid%3Dab52283f677fa07100fde3603bad4f00%26tgPt%3Djl%26tt_album_id%3D7350542944874529318%26tt_episode_id%3D730552386274263590%26tv_id%3D63ca5b1366027bd70489641b2b016d2c&uniq_id=W20250109181237510cizrw804&version=v2&version_type=current&bdpsum=80a612f
        String url = Optional.ofNullable(response.getData())
                .map(PromotionListV30ResponseData::getList)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(PromotionListV30ResponseDataListInner::getPromotionMaterials)
                .map(PromotionListV30ResponseDataListInnerPromotionMaterials::getMiniProgramInfo)
                .map(PromotionListV30ResponseDataListInnerPromotionMaterialsMiniProgramInfo::getUrl)
                .orElse("");
        // decode the url
        String decodedUrl = URLDecoder.decode(url, Charset.defaultCharset());
        // extract the tv_id
        Pattern pattern = Pattern.compile("tv_id=(\\w+)");
        Matcher matcher = pattern.matcher(decodedUrl);

        if (matcher.find()) {
            return new Pair<>(appId, matcher.group(1));
        }
        return new Pair<>(appId, "");
    }

    @SneakyThrows
    @Override
    public void pullProjectData(long advertiserId, Date startDate, Date endDate) {
        String token = getAdvertiserToken(advertiserId);

        ReportCustomGetV30Response response = null;

        int retryTimes = 5;
        while (retryTimes-- > 0) {
            response = getAdvertiserProjectData(advertiserId, startDate, endDate, token);
            Long code = response.getCode();
            if (code == null) {
                log.error("pullProjectData failed. advertiser: {} resp:{}", advertiserId, response.toString());
            }
            if (code == 40100) {
                log.warn("pullProjectData failed. advertiser: {} resp:{} 超频，重新请求。", advertiserId, response.toString());
                continue;
            }
            if (code == 0) {
                break;
            }
        }


        // Get advertiser other info
        AgtAccountJuliangAdvertiserEntity entity = new AgtAccountJuliangAdvertiserEntity();
        entity.setAdvertiserId(advertiserId);
        List<AgtAccountJuliangAdvertiserEntity> list = juliangAdvertiserMapper.selectAgtAccountJuliangAdvertiserList(entity);
        if (list.isEmpty()) {
            log.error("pullProjectData failed. advertiser: {} resp:{}", advertiserId, response.toString());
            return;
        }

        AgtAccountJuliangAdvertiserEntity agtAccountJuliangAdvertiserEntity = list.get(0);

        AgtAccountJuliangEntity selectAccount = new AgtAccountJuliangEntity();
        selectAccount.setAccountId(agtAccountJuliangAdvertiserEntity.getAccountId());
        List<AgtAccountJuliangEntity> agtAccountJuliangList = juliangMapper.selectAgtAccountJuliangList(selectAccount);
        if (agtAccountJuliangList.isEmpty()) {
            log.error("pullProjectData failed. advertiser: {} resp:{}", advertiserId, response.toString());
            return;
        }

        AgtAccountJuliangEntity agtAccountJuliangEntity = agtAccountJuliangList.get(0);

        List<ReportCustomGetV30ResponseDataRowsInner> rows = Optional.ofNullable(response.getData())
                .map(ReportCustomGetV30ResponseData::getRows)
                .orElse(Collections.emptyList());

        ArrayList<OceanEngineProjectDateDataEntity> batchInsert = new ArrayList<>();
        for (ReportCustomGetV30ResponseDataRowsInner row : rows) {
            Map<String, String> rowDimensions = row.getDimensions();
            Map<String, String> rowMetrics = row.getMetrics();
            String advertiserName = agtAccountJuliangAdvertiserEntity.getAdvertiserName();

            OceanEngineProjectDateDataEntity projectDateDataEntity = new OceanEngineProjectDateDataEntity();
            projectDateDataEntity.setAdvertiserId(String.valueOf(advertiserId));
            Long cdpProjectId = Long.valueOf(rowDimensions.get("cdp_project_id"));
            projectDateDataEntity.setProjectId(cdpProjectId);
            projectDateDataEntity.setCurDate(DateUtil.parseDate(rowDimensions.get("stat_time_day")));
            projectDateDataEntity.setStatCost(new BigDecimal(rowMetrics.get("stat_cost")));
            // cash cost
            List<AgtAccountJuliangCompanyEntity> agtAccountJuliangCompanyEntities = getAccountInfo(agtAccountJuliangEntity.getAccountId());

            // 从账户名解析  诺禾短剧pj-80   武汉澎投高进信息技术有限公司llg-2
            // parse company name
            AgtAccountJuliangCompanyEntity company = parseCompanyName(advertiserName, agtAccountJuliangCompanyEntities);
            if (company == null) {
                company = new AgtAccountJuliangCompanyEntity();
                company.setCompanyName("未知");
                company.setAccountAgent("0");
                company.setCommission(BigDecimal.ZERO);
            }

            BigDecimal commission = company.getCommission();
            if (commission == null) {
                commission = BigDecimal.ZERO;
            }

            BigDecimal cashCost = NumberUtil.div(new BigDecimal(rowMetrics.get("stat_cost")), commission.add(BigDecimal.ONE), 2);
            projectDateDataEntity.setCashCost(cashCost);

            projectDateDataEntity.setShowCnt(Convert.toInt(rowMetrics.get("show_cnt")));
            projectDateDataEntity.setCpmPlatform(new BigDecimal(rowMetrics.get("cpm_platform")));
            projectDateDataEntity.setClickCnt(Convert.toInt(rowMetrics.get("click_cnt")));
            projectDateDataEntity.setCtr(new BigDecimal(rowMetrics.get("ctr")));
            projectDateDataEntity.setCpcPlatform(new BigDecimal(rowMetrics.get("cpc_platform")));
            projectDateDataEntity.setActive(Convert.toInt(rowMetrics.get("active")));
            projectDateDataEntity.setActiveCost(new BigDecimal(rowMetrics.get("active_cost")));
            projectDateDataEntity.setActiveRate(new BigDecimal(rowMetrics.get("active_rate")));
            projectDateDataEntity.setGameAddiction(Convert.toInt(rowMetrics.get("game_addiction")));
            projectDateDataEntity.setGameAddictionCost(new BigDecimal(rowMetrics.get("game_addiction_cost")));
            projectDateDataEntity.setGameAddictionRate(new BigDecimal(rowMetrics.get("game_addiction_rate")));
            projectDateDataEntity.setAttributionMicroGame0dLtv(new BigDecimal(rowMetrics.get("attribution_micro_game_0d_ltv")));
            projectDateDataEntity.setAttributionMicroGame0dRoi(new BigDecimal(rowMetrics.get("attribution_micro_game_0d_roi")));
            projectDateDataEntity.setAttributionMicroGameIaapLtv1day(new BigDecimal(rowMetrics.get("attribution_micro_game_iaap_ltv_1day")));
            projectDateDataEntity.setStatAttributionMicroGame24hAmount(new BigDecimal(rowMetrics.get("stat_attribution_micro_game_24h_amount")));
            projectDateDataEntity.setAttributionMicroGameIaapLtv7d(new BigDecimal(rowMetrics.get("attribution_micro_game_iaap_ltv_7d")));
            projectDateDataEntity.setAttributionMicroGameIaapLtv7dRoi(new BigDecimal(rowMetrics.get("attribution_micro_game_iaap_ltv_7d_roi")));
            projectDateDataEntity.setStatAttributionMicroGame7dAmount(new BigDecimal(rowMetrics.get("stat_attribution_micro_game_7d_amount")));
            projectDateDataEntity.setConversionCost(new BigDecimal(rowMetrics.get("conversion_cost")));
            projectDateDataEntity.setConvertCnt(Convert.toInt(rowMetrics.get("convert_cnt")));
            projectDateDataEntity.setConversionRate(new BigDecimal(rowMetrics.get("conversion_rate")));

            projectDateDataEntity.setAccountId(agtAccountJuliangEntity.getAccountId());
            projectDateDataEntity.setAccountNameCo(agtAccountJuliangEntity.getAccountName());
            projectDateDataEntity.setAccountAgent(company.getAccountAgent());
            projectDateDataEntity.setAdvertiserName(advertiserName);
            projectDateDataEntity.setProjectName(rowDimensions.get("cdp_project_name"));

            projectDateDataEntity.setUserNickname(parseNickName(advertiserName));

            batchInsert.add(projectDateDataEntity);
        }

        if (batchInsert.isEmpty()) {
            return;
        }

        List<Long> projectIds = batchInsert.stream().map(OceanEngineProjectDateDataEntity::getProjectId).collect(Collectors.toList());

        // get project app id
        List<OceanEngineProjectEntity> projectEntityList = oceanEngineProjectService.selectByProjectId(projectIds);
        if (CollectionUtils.isNotEmpty(projectEntityList)) {
          Map<Long,OceanEngineProjectEntity> collect = projectEntityList.stream().collect(Collectors.toMap(OceanEngineProjectEntity::getProjectId, Function.identity()));
            for (OceanEngineProjectDateDataEntity dataEntity : batchInsert) {
                OceanEngineProjectEntity projectEntity = collect.get(dataEntity.getProjectId());
                if (projectEntity != null) {
                    dataEntity.setAppId(projectEntity.getAppId());
                    dataEntity.setTvId(projectEntity.getTvId());
                }
                if (dataEntity.getAppId() == null) {
                    dataEntity.setAppId("");
                }
                if (dataEntity.getTvId() == null) {
                    dataEntity.setTvId("");
                }
            }
        }

//        log.info("pullProjectData success. advertiser: {} batchInsert.size:{} date:{}", advertiserId, batchInsert.size(), startDate);
        projectDateDataMapper.batchInsertOrUpdate(batchInsert);

    }

    /**
     * Extracts the code pattern that appears before a hyphen
     *
     * @param input The input string
     * @return The extracted code or empty string if not found
     */
    public static String parseNickName(String input) {
        // Pattern to match letters before a hyphen
        Pattern pattern = Pattern.compile("([a-zA-Z]+)(?:-[a-zA-Z]+)*-\\d+");
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return "";
    }

    public AgtAccountJuliangCompanyEntity parseCompanyName(String advertiserName, List<AgtAccountJuliangCompanyEntity> agtAccountJuliangCompanyEntities) {
        if (CollectionUtils.isEmpty(agtAccountJuliangCompanyEntities)) {
            return null;
        }
        // 先按照名字长度排个序，名字长的排到前面
        agtAccountJuliangCompanyEntities.sort((o1, o2) -> o2.getCompanyName().length() - o1.getCompanyName().length());
        for (AgtAccountJuliangCompanyEntity companyEntity : agtAccountJuliangCompanyEntities) {
            if (advertiserName.contains(companyEntity.getCompanyName())) {
                return companyEntity;
            }
        }
        return null;
    }

    @Override
    public void recalculateProjectDataByAccountJuliangCompany(DateTime dateTime) {

        // every day, the project record about 500, so we just query all
        OceanEngineProjectDateDataEntity projectDateDataEntity = new OceanEngineProjectDateDataEntity();
        projectDateDataEntity.setCurDate(dateTime);
        List<OceanEngineProjectDateDataEntity> projectDateDataEntities = projectDateDataMapper.selectOceanEngineProjectDateDataList(projectDateDataEntity);
        if (CollectionUtils.isEmpty(projectDateDataEntities)) {
            log.error("recalculateProjectDataByAccountJuliangCompany failed. projectDateDataEntities is empty. dateTime:{}", dateTime);
            return;
        }

        log.info("recalculateProjectDataByAccountJuliangCompany start. dateTime:{} projectDateDataEntities.size:{}", dateTime, projectDateDataEntities.size());

        for (OceanEngineProjectDateDataEntity dateDataEntity : projectDateDataEntities) {
            // cash cost
            List<AgtAccountJuliangCompanyEntity> agtAccountJuliangCompanyEntities = getAccountInfo(dateDataEntity.getAccountId());

            // 从账户名解析  诺禾短剧pj-80   武汉澎投高进信息技术有限公司llg-2
            // parse company name
            AgtAccountJuliangCompanyEntity company = parseCompanyName(dateDataEntity.getAdvertiserName(), agtAccountJuliangCompanyEntities);
            if (company == null) {
                company = new AgtAccountJuliangCompanyEntity();
                company.setCompanyName("未知");
                company.setAccountAgent("0");
                company.setCommission(BigDecimal.ZERO);
            }

            BigDecimal commission = company.getCommission();
            if (commission == null) {
                commission = BigDecimal.ZERO;
            }

            BigDecimal cashCost = NumberUtil.div(dateDataEntity.getStatCost(), commission.add(BigDecimal.ONE), 2);
            dateDataEntity.setCashCost(cashCost);

            dateDataEntity.setAccountAgent(company.getAccountAgent());


        }

        projectDateDataMapper.batchInsertOrUpdate(projectDateDataEntities);

    }

    public List<AgtAccountJuliangCompanyEntity> getAccountInfo(long accountId) {

        LoadingCache<Long, List<AgtAccountJuliangCompanyEntity>> accountCache = CacheBuilder.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build(new CacheLoader<Long, List<AgtAccountJuliangCompanyEntity>>() {
                    @Override
                    public List<AgtAccountJuliangCompanyEntity> load(@NotNull Long accountId) {
                        AgtAccountJuliangCompanyEntity company = new AgtAccountJuliangCompanyEntity();
                        company.setAccountId(accountId);
                        return agtAccountJuliangCompanyMapper.selectAgtAccountJuliangCompanyList(company);
                    }
                });

        List<AgtAccountJuliangCompanyEntity> accountCompanyList; // Fixed typo in variable name
        try {
            accountCompanyList = accountCache.get(accountId);
            if (accountCompanyList.isEmpty()) { // Added null check
                throw new RuntimeException("Account not found for id: " + accountId);
            }
        } catch (ExecutionException e) {
            log.error("Failed to get accountCompanyList from cache", e); // Fixed error message
            AgtAccountJuliangCompanyEntity company = new AgtAccountJuliangCompanyEntity();
            company.setAccountId(accountId);
            accountCompanyList = agtAccountJuliangCompanyMapper.selectAgtAccountJuliangCompanyList(company);
            if (accountCompanyList.isEmpty()) { // Added null check for direct DB query
                throw new RuntimeException("Account not found for id: " + accountId);
            }
        }

        return accountCompanyList;
    }

    private static ReportCustomGetV30Response getAdvertiserProjectData(long advertiserId, Date startDate, Date endDate, String token) throws ApiException {
        ReportCustomGetV30Api api = new ReportCustomGetV30Api(new ApiClient());
        ApiClient client = api.getApiClient();
        client.addDefaultHeader("Access-Token", token);
        api.setApiClient(client);


        List<String> dimensions = new ArrayList<>();
        dimensions.add("stat_time_day");
        dimensions.add("cdp_project_id");
        dimensions.add("cdp_project_name");

        List<String> metrics = new ArrayList<>();
        metrics.add("stat_cost");
        metrics.add("show_cnt");
        metrics.add("cpm_platform");
        metrics.add("click_cnt");
        metrics.add("ctr");
        metrics.add("cpc_platform");
        metrics.add("convert_cnt");
        metrics.add("conversion_cost");
        metrics.add("conversion_rate");
        metrics.add("active_rate");
        metrics.add("active");
        metrics.add("active_cost");
        metrics.add("game_addiction");
        metrics.add("game_addiction_cost");
        metrics.add("game_addiction_rate");
        metrics.add("attribution_micro_game_0d_roi");
        metrics.add("attribution_micro_game_0d_ltv");
        metrics.add("attribution_micro_game_iaap_ltv_1day");
        metrics.add("stat_attribution_micro_game_24h_amount");
        metrics.add("attribution_micro_game_iaap_ltv_7d");
        metrics.add("attribution_micro_game_iaap_ltv_7d_roi");
        metrics.add("stat_attribution_micro_game_7d_amount");

        List<ReportCustomGetV30FiltersInner> reportCustomGetV30FiltersInners = new ArrayList<>();

        List<ReportCustomGetV30OrderByInner> orderBy = new ArrayList<>();

        ReportCustomGetV30Response response = api.openApiV30ReportCustomGetGet(dimensions, advertiserId,
                metrics,
                reportCustomGetV30FiltersInners, DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), orderBy, 1, 100, null);
        return response;
    }

}
