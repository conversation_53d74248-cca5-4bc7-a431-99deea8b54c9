package com.ruoyi.system.service.novel;

import java.util.List;
import java.util.Map;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.ruoyi.system.entity.novel.NovelMiniappEntity;

/**
 * 小说小程序appService接口
 *
 * <AUTHOR>
 */
public interface NovelMiniappService {

    /**
     * 查询小说小程序app
     *
     * @param appId 小说小程序appId
     * @return 小说小程序app
     */
    NovelMiniappEntity selectByAppId(String appId);

    /**
     * 查询小程序名称
     */
    Map<String, String> selectNameMap();

    /**
     * 查询小说小程序app
     *
     * @param id 小说小程序app主键
     * @return 小说小程序app
     */
    public NovelMiniappEntity selectNovelMiniappById(String id);

    /**
     * 查询小说小程序app列表
     *
     * @param novelMiniapp 小说小程序app
     * @return 小说小程序app集合
     */
    public List<NovelMiniappEntity> selectNovelMiniappList(NovelMiniappEntity novelMiniapp);

    /**
     * 新增小说小程序app
     *
     * @param novelMiniapp 小说小程序app
     * @return 结果
     */
    public int insertNovelMiniapp(NovelMiniappEntity novelMiniapp);

    /**
     * 修改小说小程序app
     *
     * @param novelMiniapp 小说小程序app
     * @return 结果
     */
    public int updateNovelMiniapp(NovelMiniappEntity novelMiniapp);

    /**
     * 批量删除小说小程序app
     *
     * @param ids 需要删除的小说小程序app主键集合
     * @return 结果
     */
    public int deleteNovelMiniappByIds(String[] ids);

    /**
     * 删除小说小程序app信息
     *
     * @param id 小说小程序app主键
     * @return 结果
     */
    public int deleteNovelMiniappById(String id);

    /**
     * 根据appid查询微信小程序服务
     *
     * @param appId
     * @return
     */
    WxMaService getNovelWxMaService(String appId);

    /**
     * 根据小程序id获取小说小程序信息
     *
     * @param appId
     * @return
     */
    NovelMiniappEntity getNovelMiniappInfo(String appId);

    /**
     * @param appId
     * @return
     */
    WxMaService buildWxMaService(String appId);
}
