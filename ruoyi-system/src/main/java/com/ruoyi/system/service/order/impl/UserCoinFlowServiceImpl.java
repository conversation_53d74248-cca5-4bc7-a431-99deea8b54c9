package com.ruoyi.system.service.order.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.entity.order.UserCoinFlowEntity;
import com.ruoyi.system.mapper.order.UserCoinFlowMapper;
import com.ruoyi.system.service.order.UserCoinFlowService;
import com.ruoyi.system.req.playlet.param.CoinFlowParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 金币流水记录 Service
 *
 * <AUTHOR>
 * @date 2023-7-6 10:50:30
 */
@Service
public class UserCoinFlowServiceImpl implements UserCoinFlowService {

    @Autowired
    private UserCoinFlowMapper userCoinFlowMapper;

    @Override
    public Boolean insert(UserCoinFlowEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return userCoinFlowMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return userCoinFlowMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(UserCoinFlowEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return userCoinFlowMapper.updateById(entity) > 0;
    }

    @Override
    public UserCoinFlowEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return userCoinFlowMapper.selectById(id);
    }

    @Override
    public List<UserCoinFlowEntity> selectListByParam(CoinFlowParam param) {
        if(Objects.nonNull(param.getEndDate())){
            param.setEndDate(DateUtil.endOfDay(param.getEndDate()));
        }
        return userCoinFlowMapper.selectListByParam(param);
    }
}