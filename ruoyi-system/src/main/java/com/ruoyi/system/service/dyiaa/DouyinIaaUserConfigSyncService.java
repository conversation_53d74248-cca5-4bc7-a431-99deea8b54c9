package com.ruoyi.system.service.dyiaa;

import java.util.List;

public interface DouyinIaaUserConfigSyncService {

    /**
     * 同步用户
     */
    void syncUserByRegisterTime();

    /**
     * 同步用户
     */
    void syncUserByUpdateTime();

    /**
     * 同步配置
     */
    void syncConfig();

    /**
     * 同步广告播放记录
     */
    void syncAdExposureByEventTime();

    /**
     * 批量更新用户
     */
    void batchUpdateUserByOpenIds(List<String> openIds);
    /**
     * 批量更新用户
     */
    void batchUpdateUserByUserIds(List<String> userIds);
}
