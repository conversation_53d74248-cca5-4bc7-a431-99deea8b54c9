package com.ruoyi.system.listener.eventTrack;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.enums.eventTrack.TrackEventTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.system.bo.eventTrack.TrackDataBO;
import com.ruoyi.system.event.TrackEvent;
import com.ruoyi.system.handler.eventTrack.TrackEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class TrackEventListener implements InitializingBean {

    @Autowired
    private List<TrackEventHandler> handlers;

    private final Map<TrackEventTypeEnum, List<TrackEventHandler>> handlerMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        for (TrackEventHandler handler : handlers) {
            for (TrackEventTypeEnum type : TrackEventTypeEnum.values()) {
                if (handler.supports(type)) {
                    handlerMap.computeIfAbsent(type, k -> new ArrayList<>()).add(handler);
                }
            }
        }
    }

    @EventListener
    public void onTrackEvent(TrackEvent event) {
        GlobalThreadPool.trackEventExecutorService.execute(() -> {
            try {
                processTrackEvent(event);
            } catch (Exception e) {
                log.error("处理埋点事件异常", e);
            }
        });
    }

    private void processTrackEvent(TrackEvent event) {
        TrackDataBO bo = event.getSource();
        TrackEventTypeEnum type = bo.getEventType();
        if ( Objects.isNull(type) ) {
            log.error("埋点事件类型不存在, event={}", JSONObject.toJSONString(bo));
            return;
        }
        List<TrackEventHandler> matchedHandlers = handlerMap.getOrDefault(type, Collections.emptyList());
        for (TrackEventHandler handler : matchedHandlers) {
            try {
                if ( handler.validate(bo) ) {
                    handler.handle(bo);
                }else {
                    log.error("埋点事件参数校验失败, event={}", JSONObject.toJSONString(bo));
                }
            } catch (Exception e) {
               log.error("处理埋点事件异常, event={}", JSONObject.toJSONString(bo), e);
            }
        }
    }
}
