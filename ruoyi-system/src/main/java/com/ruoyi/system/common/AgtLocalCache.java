package com.ruoyi.system.common;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.*;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.entity.material.AgtDataMaterialEntity;
import com.ruoyi.system.entity.roiall.AgtDataTotalRoiEntity;
import com.ruoyi.system.entity.user.AgtAccountUserEntity;
import com.ruoyi.system.mapper.material.AgtDataMaterialMapper;
import com.ruoyi.system.mapper.material.AgtDataMaterialSelfMapper;
import com.ruoyi.system.service.juliangad.AgtAccountJuliangAdvertiserService;
import com.ruoyi.system.service.user.AgtAccountUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: keboom
 * @date: 2024/7/5
 */
@Slf4j
@Component
public class AgtLocalCache {

    @Autowired
    private AgtAccountJuliangAdvertiserService agtAccountJuliangAdvertiserService;
    @Autowired
    private AgtAccountUserService agtAccountUserService;
    @Autowired
    private AgtDataMaterialSelfMapper materialSelfMapper;
    @Autowired
    private AgtDataMaterialMapper materialMapper;

    public LoadingCache<Long, Optional<AgtAccountJuliangAdvertiserEntity>> advertiserCache;
    public LoadingCache<String, String> agtAccountUserCache;
    public LoadingCache<String, AgtDataMaterialEntity> materialNameCache;


    @PostConstruct
    public void init() {
        advertiserCache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(5, TimeUnit.DAYS)
                .refreshAfterWrite(2, TimeUnit.DAYS) // 刷新缓存时间
                .removalListener(new RemovalListener<Long, Optional<AgtAccountJuliangAdvertiserEntity>>() {
                    @Override
                    public void onRemoval(RemovalNotification<Long, Optional<AgtAccountJuliangAdvertiserEntity>> notification) {
                        log.info("Removed: " + notification.getKey() + " -> " + notification.getValue());
                    }
                })
                .build(new CacheLoader<Long, Optional<AgtAccountJuliangAdvertiserEntity>>() {
                    @Override
                    public Optional<AgtAccountJuliangAdvertiserEntity> load(Long advertiserId) throws Exception {
                        // 当缓存中没有值时，加载新值的逻辑
                        return loadFromDatabase(advertiserId);
                    }

                    @Override
                    public ListenableFuture<Optional<AgtAccountJuliangAdvertiserEntity>> reload(Long advertiserId, Optional<AgtAccountJuliangAdvertiserEntity> oldValue) throws Exception {
                        // 异步刷新缓存的逻辑
                        return Futures.immediateFuture(loadFromDatabase(advertiserId));
                    }
                });

        agtAccountUserCache = CacheBuilder.newBuilder()
                .maximumSize(50)
                .expireAfterWrite(5, TimeUnit.DAYS)
                .refreshAfterWrite(2, TimeUnit.DAYS) // 刷新缓存时间
                .removalListener(new RemovalListener<String, String>() {
                    @Override
                    public void onRemoval(RemovalNotification<String, String> notification) {
                        log.info("Removed: " + notification.getKey() + " -> " + notification.getValue());
                    }
                })
                .build(new CacheLoader<String, String>() {
                    @Override
                    public String load(String userNickname) throws Exception {
                        // 当缓存中没有值时，加载新值的逻辑
                        return loadFromDatabaseUserAccount(userNickname);
                    }

                    @Override
                    public ListenableFuture<String> reload(String userNickname, String oldValue) throws Exception {
                        // 异步刷新缓存的逻辑
                        return Futures.immediateFuture(loadFromDatabaseUserAccount(userNickname));
                    }
                });

        materialNameCache=CacheBuilder.newBuilder()
                .maximumSize(2000)
                .expireAfterWrite(12, TimeUnit.HOURS)
                .refreshAfterWrite(1, TimeUnit.HOURS) // 刷新缓存时间
                .removalListener(new RemovalListener<String, AgtDataMaterialEntity>() {
                    @Override
                    public void onRemoval(RemovalNotification<String, AgtDataMaterialEntity> notification) {
                        log.info("Removed: " + notification.getKey() + " -> " + notification.getValue());
                    }
                })
                .build(new CacheLoader<String, AgtDataMaterialEntity>() {
                    @Override
                    public AgtDataMaterialEntity load(String materialId) throws Exception {
                        // 当缓存中没有值时，加载新值的逻辑
                        return loadFromDatabaseMaterialName(materialId);
                    }

                    @Override
                    public ListenableFuture<AgtDataMaterialEntity> reload(String materialId, AgtDataMaterialEntity oldValue) throws Exception {
                        // 异步刷新缓存的逻辑
                        return Futures.immediateFuture(loadFromDatabaseMaterialName(materialId));
                    }
                });
    }

    private AgtDataMaterialEntity loadFromDatabaseMaterialName(String materialId) {
        if (materialNameCache.size() == 0) {
            // 查询所有的素材
            materialSelfMapper.selectMaterialIdAndAdPlatformMaterialName().forEach(item -> materialNameCache.put(item.getMaterialId(), item));
        }
        AgtDataMaterialEntity entity = materialSelfMapper.selectMaterialName(materialId);
        return entity;
    }

    private String loadFromDatabaseUserAccount(String userNickname) {
        if (agtAccountUserCache.size() == 0) {
            List<AgtAccountUserEntity> agtAccountUserEntities = agtAccountUserService.selectAgtAccountUserList(null);
            agtAccountUserEntities.forEach(
                    entity -> agtAccountUserCache.put(entity.getUserNickname(), entity.getUserRealname()));
        }
        String ifPresent = agtAccountUserCache.getIfPresent(userNickname);
        if (ifPresent != null) {
            return ifPresent;
        }
        AgtAccountUserEntity entity = new AgtAccountUserEntity();
        entity.setUserNickname(userNickname);
        List<AgtAccountUserEntity> entities = agtAccountUserService.selectAgtAccountUserList(entity);
        if (CollectionUtil.isEmpty(entities)) {
            return "未知";
        }
        return entities.get(0).getUserRealname();
    }

    private Optional<AgtAccountJuliangAdvertiserEntity> loadFromDatabase(Long advertiserId) {
        // 初始化缓存
        if (advertiserCache.size() == 0) {
            // 查询有消耗的广告主
            AgtAccountJuliangAdvertiserEntity entity = new AgtAccountJuliangAdvertiserEntity();
            entity.setCostStatus(1L);
            List<AgtAccountJuliangAdvertiserEntity> entityList = agtAccountJuliangAdvertiserService.selectAgtAccountJuliangAdvertiserList(entity);
            if (CollectionUtil.isNotEmpty(entityList)) {
                for (AgtAccountJuliangAdvertiserEntity agtAccountJuliangAdvertiserEntity : entityList) {
                    advertiserCache.put(agtAccountJuliangAdvertiserEntity.getAdvertiserId(), Optional.of(agtAccountJuliangAdvertiserEntity));
                }
            }
        }
        AgtAccountJuliangAdvertiserEntity entity = new AgtAccountJuliangAdvertiserEntity();
        entity.setAdvertiserId(advertiserId);
        List<AgtAccountJuliangAdvertiserEntity> entityList = agtAccountJuliangAdvertiserService.selectAgtAccountJuliangAdvertiserList(entity);
        if (CollectionUtil.isEmpty(entityList)) {
            log.info("advertiserId: {} not found in database", advertiserId);
            return Optional.ofNullable(null);
        }
        return Optional.of(entityList.get(0));
    }
}
