package com.ruoyi.system.bo.system;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告位分流跳转配置项
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class SlotConfigItemBo extends ShuntRatio implements Serializable {
    private static final long serialVersionUID = -8083204059019558433L;
    /** 主键 */
    private Long id;

    /** 广告位id */
    @Excel(name = "广告位id")
    private Long slotId;

    /** 配置id */
    @Excel(name = "配置id")
    private Long configId;
    /** 内容名称 */
    @Excel(name = "内容名称")
    private String contentName;

    /** 配置内容 */
    @Excel(name = "配置内容")
    private String content;
    /**
     * 分流比例
     */
    private Integer ratio;

    /** 内容类型,1文本 */
    @Excel(name = "内容类型,1文本")
    private Integer contentType;
}
