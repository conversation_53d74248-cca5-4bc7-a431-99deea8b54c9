package com.ruoyi.system.bo.matrix;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 爆量工具任务详情BO
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
public class MatrixGdtTaskInfoBo implements Serializable {
    private static final long serialVersionUID = -5735303719998770909L;

    /**
     * 目标广告账户ID列表
     */
    private List<Long> targetAdvertisementIds;

    /**
     * 地域混淆:0.不混淆,1.混淆
     */
    private Integer regionObfuscated;

    /**
     * 单户复制计划数
     */
    private Integer copyAdgroupCount = 1;

    /**
     * 目标创意ID列表
     */
    private List<Long> targetCreativeIds;

    /**
     * 单计划复制创意数
     */
    private Integer copyCreativeCount = 1;
    /**
     * 标签id
     */
    private Long tagId = 0L;
    /**
     * 标签素材数
     */
    private Integer tagNum = 0;
    /**
     * 模板素材数
     */
    private Integer templateNum = 3;


    /**
     * 素材自定义:0.关闭,1.开启
     */
    private Integer materialCustom;

    /**
     * 目标素材ID列表
     */
    private List<Long> targetMaterialIds;

    /**
     * 素材库:0.ADQ素材库,1.创量素材库
     */
    private Integer materialType;

    /**
     * 创量素材目录(选择创量素材库时使用)
     */
    private String clMaterialDir;

    /**
     * 创量素材数(选择创量素材库时使用)
     */
    private Integer clMaterialNum = 1;

    /**
     * 母版素材数(选择创量素材库时使用)
     */
    private Integer clTemplateNum = 0;

    /**
     * 新素材占比(选择创量素材库时使用)
     */
    private Integer newMaterialRatio;

    /**
     * 文案(选择创量素材库时使用)
     */
    private List<String> sloganList;
}
