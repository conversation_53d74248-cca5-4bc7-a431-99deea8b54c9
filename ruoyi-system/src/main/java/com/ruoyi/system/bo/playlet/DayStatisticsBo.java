package com.ruoyi.system.bo.playlet;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 每日数据请求参数
 * <AUTHOR>
 * @date 2023/7/7 13:41
 */
@Data
public class DayStatisticsBo implements Serializable {
    private static final long serialVersionUID = -6735036578212907886L;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 平台类型 由DataStatPlatformEnum 转换而来
     * @see cn.com.nuohe.playlet.manager.common.enums.LoginPlatformEnum
     */
    private List<Integer> platforms;
    /**
     *是否导出
     */
    private Boolean isExport;

    /**
     * 最后统计时间
     */
    private Date lastCurDate;
    /**
     * 页大小
     */
    private Integer pageSize;

}