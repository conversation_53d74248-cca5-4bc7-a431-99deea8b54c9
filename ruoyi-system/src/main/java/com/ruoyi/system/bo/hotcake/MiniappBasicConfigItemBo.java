package com.ruoyi.system.bo.hotcake;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 基础配置项BO
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@Data
public class MiniappBasicConfigItemBo implements Serializable {
    private static final long serialVersionUID = 7505043644319283891L;

    /**
     * 开关状态:1.开,0.关
     */
    private Integer switchStatus;

    /**
     * 返回拦截次数
     */
    private Integer retTimes;

    /**
     * 广告间隔(间隔n个视频出广告)
     */
    private Integer adInterval;

    /**
     * ADQ广告位ID
     */
    private String adqSlotId;
}
