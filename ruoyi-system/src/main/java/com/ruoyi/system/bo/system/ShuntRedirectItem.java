package com.ruoyi.system.bo.system;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位分流跳转配置项
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class ShuntRedirectItem extends ShuntRatio implements Serializable {
    private static final long serialVersionUID = -8083204059019558433L;

    /**
     * 跳转类型
     */
    private Integer redirectType;

    /**
     * 跳转目标
     */
    private String redirectValue;

    /**
     * 修改时间
     */
    private String modifyTime;
}
