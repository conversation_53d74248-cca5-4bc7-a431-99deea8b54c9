package com.ruoyi.system.bo.playlet;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 日渠道维度数据导出
 *
 * <AUTHOR>
 * @Date 2024/4/2 10:29
 */
@Data
public class DayChannelStatisticsExportBo implements Serializable {

    private static final long serialVersionUID = -189058729404352731L;

    /**
     * 日期
     */
    @ExcelProperty("日期")
    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(20)
    private Date curDate;

    /**
     * 推广链接
     */
    @ExcelProperty("推广链接")
    @ColumnWidth(20)
    private String promotionUrl;

    /**
     * 渠道号
     */
    @ExcelProperty("渠道号")
    @ColumnWidth(20)
    private String channel;

    /**
     * 新增用户数
     */
    @ExcelProperty("新增用户数")
    @ColumnWidth(20)
    private Integer register;

    /**
     * 在线用户数
     */
    @ExcelProperty("在线用户数")
    @ColumnWidth(20)
    private Integer onlineUserCount;

    /**
     * 充值用户数
     */
    @ExcelProperty("充值用户数")
    @ColumnWidth(20)
    private Integer rechargeUv;

    /**
     * 当日充值金额数
     */
    @ExcelProperty("充值金额(元)")
    @ColumnWidth(20)
    private String rechargeAmount;

    /**
     *人均充值金额(元)
     */
    @ExcelProperty("人均充值金额(元)")
    @ColumnWidth(20)
    private String avgRechargeAmount;

    /**
     * 笔单价
     */
    @ExcelProperty("笔单价")
    @ColumnWidth(20)
    private String orderRechargeAvg;

    /**
     * 退款笔数
     */
    @ExcelProperty("退款笔数")
    @ColumnWidth(20)
    private Integer refundPv;

    /**
     * 短剧观看人数
     */
    @ExcelProperty("短剧观看人数")
    @ColumnWidth(20)
    private Integer watchEpisodeUv;

    /**
     * 观看总集数
     */
    @ExcelProperty("观看总集数")
    @ColumnWidth(20)
    private Integer watchEpisodePv;

    /**
     * 解锁集数
     */
    @ExcelProperty("解锁总集数")
    @ColumnWidth(20)
    private Integer unlockEpisodePv;

    /**
     * 人均观看集数
     */
    @ExcelProperty("人均观看集数")
    @ColumnWidth(20)
    private String avgWatchEpisode;

    /**
     * 广告曝光次数
     */
    @ExcelProperty("广告观看次数")
    @ColumnWidth(20)
    private Integer advertExposureCount;

    /**
     * 观看广告数
     */
    @ExcelProperty("广告解锁次数")
    @ColumnWidth(20)
    private Integer advertCount;

    /**
     * 广告解锁人数
     */
    @ExcelProperty("广告解锁人数")
    @ColumnWidth(20)
    private Integer advertCountUv;

    /**
     * 消费金币数
     */
    @ExcelProperty("消费金币数")
    @ColumnWidth(20)
    private Integer consumeCoin;

    /**
     * 当日账户金币余额数
     */
    @ExcelProperty("账户余额(金币)")
    @ColumnWidth(20)
    private Integer coinSum;
}