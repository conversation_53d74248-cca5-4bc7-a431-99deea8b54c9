package com.ruoyi.system.bo.shortplay;

import lombok.Data;

import java.io.Serializable;

@Data
public class VirtualPaymentSignDataBo implements Serializable {
    private static final long serialVersionUID = 4399548495715284006L;

    /**
     * 在米大师侧申请的应用 id, mp-支付基础配置中的offerid
     */
    private String offerId;

    /**
     * 购买数量
     */
    private Integer buyQuantity;

    /**
     * 环境配置:0.米大师正式环境,1.米大师沙箱环境,默认为0
     */
    private Integer env;

    /**
     * 币种:CNY.人民币
     */
    private String currencyType;

    /**
     * 业务订单号
     */
    private String outTradeNo;

    /**
     * 透传数据, 发货通知时会透传给开发者
     */
    private String attach;
}
