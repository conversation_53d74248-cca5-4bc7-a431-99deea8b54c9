package com.ruoyi.system.dao.juliangad;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserSelfMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * {@code @author:} keboom
 * {@code @date:} 2024/6/28
 */
@Repository
public class AgtAccountJuliangAdvertiserDao extends ServiceImpl<AgtAccountJuliangAdvertiserSelfMapper, AgtAccountJuliangAdvertiserEntity> {

    @Autowired
    private AgtAccountJuliangAdvertiserSelfMapper mapper;

    public AgtAccountJuliangAdvertiserEntity selectByAdvertiserId(long advertiserId) {
        LambdaQueryWrapper<AgtAccountJuliangAdvertiserEntity> query = Wrappers.lambdaQuery();
        query.eq(AgtAccountJuliangAdvertiserEntity::getAdvertiserId, advertiserId);
        return mapper.selectOne(query);
    }

    public List<AgtAccountJuliangAdvertiserEntity> selectListByAccountId(long accountId) {
        LambdaQueryWrapper<AgtAccountJuliangAdvertiserEntity> query = Wrappers.lambdaQuery();
        query.eq(AgtAccountJuliangAdvertiserEntity::getAccountId, accountId);
        return mapper.selectList(query);
    }

    public List<AgtAccountJuliangAdvertiserEntity> selectPlayletNameByPlayletName(String playletName) {
            LambdaQueryWrapper<AgtAccountJuliangAdvertiserEntity> query = Wrappers.lambdaQuery();
        query.select(AgtAccountJuliangAdvertiserEntity::getPlayletName)
                .like(AgtAccountJuliangAdvertiserEntity::getPlayletName, playletName)
                .groupBy(AgtAccountJuliangAdvertiserEntity::getPlayletName);
        return mapper.selectList(query);
    }

    /**
     * 查询消耗大于0的广告主
     * @param advertiserIds
     * @return
     */
    public List<Long> getCostGtZeroAdvertiserId(ArrayList<Long> advertiserIds) {
        LambdaQueryWrapper<AgtAccountJuliangAdvertiserEntity> query = Wrappers.lambdaQuery();
        query.select(AgtAccountJuliangAdvertiserEntity::getAdvertiserId)
                .eq(AgtAccountJuliangAdvertiserEntity::getCostStatus, 1)
                .in(AgtAccountJuliangAdvertiserEntity::getAdvertiserId, advertiserIds);
        return mapper.selectList(query).stream().map(AgtAccountJuliangAdvertiserEntity::getAdvertiserId).collect(Collectors.toList());
    }

    public List<Long> getCostGtZero() {
        LambdaQueryWrapper<AgtAccountJuliangAdvertiserEntity> query = Wrappers.lambdaQuery();
        query.select(AgtAccountJuliangAdvertiserEntity::getAdvertiserId)
                .eq(AgtAccountJuliangAdvertiserEntity::getCostStatus, 1);
        return mapper.selectList(query).stream().map(AgtAccountJuliangAdvertiserEntity::getAdvertiserId).collect(Collectors.toList());
    }

    public Map<Long,Long> getAllAdvertiserIdAndAccountId() {
        LambdaQueryWrapper<AgtAccountJuliangAdvertiserEntity> query = Wrappers.lambdaQuery();
        query.select(AgtAccountJuliangAdvertiserEntity::getAdvertiserId,AgtAccountJuliangAdvertiserEntity::getAccountId);
        return mapper.selectList(query).stream().collect(Collectors.toMap(AgtAccountJuliangAdvertiserEntity::getAdvertiserId, AgtAccountJuliangAdvertiserEntity::getAccountId));
    }
}
