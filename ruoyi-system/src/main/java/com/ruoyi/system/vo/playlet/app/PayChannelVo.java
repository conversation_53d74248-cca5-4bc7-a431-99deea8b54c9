package com.ruoyi.system.vo.playlet.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 充值通道列表Vo
 *
 * <AUTHOR>
 * @Date 2023/10/12 15:22
 */
@Data
public class PayChannelVo implements Serializable {

    private static final long serialVersionUID = -6080726785004309906L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 通道名称
     */
    private String name;

    /**
     * 支付通道账号
     */
    private String account;

    /**
     * 支付通道主体
     */
    private String company;

    /**
     * 通道类型 1支付宝app支付 2支付宝续费订阅(周期扣款) 3支付宝续费订阅(商家扣款)
     * @see cn.com.nuohe.playlet.manager.common.enums.PayChannelTypeEnum
     */
    private Integer type;

    /**
     * 状态 0封禁 1正常
     * @see cn.com.nuohe.playlet.manager.common.enums.PayChannelStateEnum
     */
    private Integer state;

    /**
     * 关联的应用列表
     */
    private List<String> correlationAppList;

    /**
     * 申请人
     */
    private String applicant;


    /**
     * 备注
     */
    private String remark;

    /**
     * 商户应用id
     */
    private String mchAppId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 修改时间
     */
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

}
