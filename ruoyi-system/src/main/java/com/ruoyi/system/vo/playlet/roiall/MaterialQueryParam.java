package com.ruoyi.system.vo.playlet.roiall;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: keboom
 * @date: 2024/7/15
 */
@Data
public class MaterialQueryParam {


    List<Long> filterAdvertiserId;
    List<String> filterSupplierName;
    List<String> filterUserNickname;
    List<String> filterVideoEditor;
    String materialName;

    @NotNull
    String filterStartDate;

    @NotNull
    String filterEndDate;

    /**
     * advertiser_id, playlet_name, user_nickname, supplier_name_short, platform_dy_wx, cur_date, video_editor, video_creation_type, material_id,material_create_time
     */
    List<String> dimension;

    List<DetailQueryParam.DetailOrderItem> orderBy;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailOrderItem {
        String field;
        /**
         * asc, desc
         */
        String type;
    }
}
