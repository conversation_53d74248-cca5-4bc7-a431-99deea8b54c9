package com.ruoyi.system.vo.slotdata;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位数据vo
 * <AUTHOR>
 * @date 2025/5/2 22:40
 */
@Data
public class SlotDataVO implements Serializable {
    private static final long serialVersionUID = -4558038788596645536L;
    /** 主键 */
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /** 广告位id */
    @Excel(name = "广告位id")
    private Long slotId;
    /** 广告位名称 */
    @Excel(name = "广告位名称")
    private String slotName;

    /** 配置id */
    @Excel(name = "配置id")
    private Long configId;
    /** 配置名称 */
    @Excel(name = "配置名称")
    private String configName;

    /** 内容id */
    @Excel(name = "内容id")
    private Long configItemId;
    /**
     * 内容名称
     */
    @Excel(name = "内容名称")
    private String contentName;

    /** 访问pv */
    @Excel(name = "访问pv")
    private Integer pv;

    /** 访问uv */
    @Excel(name = "访问uv")
    private Integer uv;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    private Date gmtModified;
}
