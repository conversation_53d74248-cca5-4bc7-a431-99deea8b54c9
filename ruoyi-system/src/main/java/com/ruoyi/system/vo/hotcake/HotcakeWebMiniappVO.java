package com.ruoyi.system.vo.hotcake;

import com.ruoyi.system.bo.hotcake.MiniappBasicConfigBo;
import com.ruoyi.system.bo.hotcake.MiniappSlotConfigBo;
import lombok.Data;

import java.io.Serializable;

/**
 * 爆款小程序配置VO(小程序使用)
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Data
public class HotcakeWebMiniappVO implements Serializable {
    private static final long serialVersionUID = -623239865189401108L;

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 小程序名称
     */
    private String appName;

    /**
     * 广告位配置
     */
    private MiniappSlotConfigBo slotConfig;

    /**
     * 基础配置
     */
    private MiniappBasicConfigBo basicConfig;
}
