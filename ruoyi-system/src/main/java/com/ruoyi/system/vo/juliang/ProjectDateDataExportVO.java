package com.ruoyi.system.vo.juliang;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: keboom
 * @date: 2025/2/27
 */
@Data
public class ProjectDateDataExportVO {

    /** 主键id */
    private Long id;

    /** 广告主id */
    @Excel(name = "广告主id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String advertiserId;

    /** 项目id */
    @Excel(name = "项目id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectId;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /** 巨量消耗 */
    @Excel(name = "巨量消耗")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal statCost;

    /**
     * 现金消耗
     */
    @Excel(name = "现金消耗")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal cashCost;

    /** 展示数 */
    @Excel(name = "展示数")
    private Integer showCnt;

    /** cpm */
    @Excel(name = "cpm")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal cpmPlatform;

    /** 点击数 */
    @Excel(name = "点击数")
    private Integer clickCnt;

    /** 点击率 */
    @Excel(name = "点击率")
    private String ctr;

    /** 平均点击单价(元) */
    @Excel(name = "平均点击单价(元)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal cpcPlatform;

    /** 激活数 */
    @Excel(name = "激活数")
    private Integer active;

    /** 激活成本 */
    @Excel(name = "激活成本")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal activeCost;

    /** 激活率 */
    @Excel(name = "激活率")
    private String activeRate;

    /** 关键行为数 */
    @Excel(name = "关键行为数")
    private Integer gameAddiction;

    /** 关键行为成本 */
    @Excel(name = "关键行为成本")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal gameAddictionCost;

    /** 关键行为率 */
    @Excel(name = "关键行为率")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal gameAddictionRate;

    /** 转化数 */
    @Excel(name = "转化数")
    private Integer convertCnt;

    /** 平均转化成本 */
    @Excel(name = "平均转化成本")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal conversionCost;

    /** 转化率 */
    @Excel(name = "转化率")
    private String conversionRate;

    /** 当日广告变现ROI */
    @Excel(name = "当日广告变现ROI")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal attributionMicroGame0dRoi;

    /** 当日变现金额(激活时间) */
    @Excel(name = "当日变现金额(激活时间)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal attributionMicroGameIaapLtv1day;

    /** 24小时变现金额（激活时间） */
    @Excel(name = "24小时变现金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal statAttributionMicroGame24hAmount;

    /** 7日综合ROI（激活时间） */
    @Excel(name = "7日综合ROI")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal attributionMicroGameIaapLtv7dRoi;

    /** 7日变现金额（激活时间） */
    @Excel(name = "7日变现金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal statAttributionMicroGame7dAmount;

    /** 主体名称 */
    @Excel(name = "主体名称")
    private String accountNameCo;

    /** 代理商 */
    @Excel(name = "代理商")
    private String accountAgent;

    /** 账户名 */
    @Excel(name = "账户名")
    private String advertiserName;

    /** 项目名 */
    @Excel(name = "项目名")
    private String projectName;

    /** 投手 */
    @Excel(name = "投手")
    private String userNickname;

    /** 小程序 */
    @Excel(name = "小程序")
    private String appId;

    /**
     * 真实姓名
     */
    @Excel(name = "真实姓名")
    private String userRealname;

    @Excel(name = "小程序名")
    private String appName;
    
    /** 短剧id */
    @Excel(name = "短剧id")
    private String tvId;
    
    /**
     * 短剧名称
     */
    @Excel(name = "短剧名称")
    private String tvName;
}
