package com.ruoyi.system.vo.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 巨量数据vo
 * <AUTHOR>
 * @date 2024/4/11 10:27
 */
@Data
public class JlDateDataVo implements Serializable {
    private static final long serialVersionUID = -340462943701200130L;
    /** 主键id */
    private String id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /** 账户id */
    @Excel(name = "账户id")
    private String advertiserId;
    /**
     * 账户名
     */
    private String advertiserName;
    /**
     * 备注
     */
    private String remark;

    /** 广告id */
    @Excel(name = "广告id")
    private String adId;

    /**
     * 广告名称
     */
    private String adName;

    /** 消耗 */
    @Excel(name = "消耗")
    private String statCostStr;
    /**
     * 广告收入
     */
    @Excel(name = "广告收入")
    private String adIncomeStr;
    /**
     * 首日roi
     * 首日广告收入/今日广告消耗
     */
    @Excel(name = "首日roi")
    private String firstDayRoi;
    /**
     * 今日roi
     * 今日广告收入/今日广告消耗
     */
    @Excel(name = "今日roi")
    private String todayRoi;

    /**
     * 现金roi
     * 今日广告收入/0.7/当日广告消耗
     */
    @Excel(name = "现金roi")
    private String cashRoi;
    /**
     * 毛利
     * 广告收入/0.7-广告消耗
     */
    @Excel(name = "毛利")
    private String profit;

    /** 转化数 */
    @Excel(name = "转化数")
    private Integer convertCnt;

    /** 转化成本 */
    @Excel(name = "转化成本")
    private String convertCostStr;

    /** 展示数 */
    @Excel(name = "展示数")
    private Integer showCnt;

    /** 点击数 */
    @Excel(name = "点击数")
    private Integer clickCnt;

    /** 点击率 */
    @Excel(name = "点击率")
    private Double ctr;

    /**
     * 广告播放次数
     */
    @Excel(name = "广告播放次数")
    private Integer adWatchPv;
    /**
     * 广告播放人数
     */
    @Excel(name = "广告播放人数")
    private Integer adWatchUv;
    /**
     * 首日广告收入 单位为：十万分之一元
     */
    @Excel(name = "首日广告收入")
    private Long firstDayAdIncome;
    /**
     * 今日广告收入 单位为：十万分之一元
     */
    @Excel(name = "今日广告收入")
    private Long todayAdIncome;

    /**
     * 首日ipu
     */
    @Excel(name = "首日ipu")
    private String firstDayIpu;
    /**
     * 首日付费arpu
     */
    @Excel(name = "首日付费arpu")
    private String firstDayArpu;
    /**
     * 首日ecpm
     */
    @Excel(name = "首日ecpm")
    private String firstDayEcpm;


}
