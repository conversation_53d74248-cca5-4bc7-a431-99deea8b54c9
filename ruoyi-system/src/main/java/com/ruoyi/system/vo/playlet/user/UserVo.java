package com.ruoyi.system.vo.playlet.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息vo
 * <AUTHOR>
 * @date 2023/7/4 18:39
 */
@Data
public class UserVo implements Serializable {
    private static final long serialVersionUID = -7575159955219539902L;
    /**
     * id
     */
    private String id;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 充值总金额
     */
    private Integer chargeSum;

    /**
     * 金币余额
     */
    private Integer coinSum;

    /**
     * 消费总金币
     */
    private Integer consumerSum;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

}
