package com.ruoyi.system.vo.playlet.aiaa;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告数据
 *
 * <AUTHOR>
 * @date 2023年7月11日 下午5:44:53
 */
@Data
public class ListAiaaAdDataVO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告曝光pv
     */
    private Long adShowPv;

    /**
     * 广告曝光uv,redis
     */
    private Long adShowUv;

    /**
     * 消耗（分）
     */
    private Long consume;

    /**
     * arpu消耗/广告曝光uv
     */
    private Long arpu;

}

