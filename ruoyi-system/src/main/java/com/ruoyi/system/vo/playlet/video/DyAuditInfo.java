package com.ruoyi.system.vo.playlet.video;

import lombok.Data;

import java.io.Serializable;

/**
 * 抖音审核信息
 * <AUTHOR>
 * @date 2024/2/7 10:40
 */
@Data
public class DyAuditInfo implements Serializable {
    private static final long serialVersionUID = 4773695483310388988L;
    /**
     * 小程序appid
     */
    private String appId;

    /**
     * 抖音短剧ID
     */
    private String dyAlbumId;

    /**
     * 抖音短剧版本号
     */
    private Integer dyVersion;
    /**
     * 抖音审核状态
     * @see cn.com.nuohe.playlet.manager.common.enums.DyAuditStatusEnum
     */
    private Integer dyAuditStatus;
    /**
     * 抖音审核失败原因
     */
    private String dyAuditErrorMsg;
    /**
     * 抖音上线状态
     * true上线 false下线
     */
    private Boolean dyOnlineStatus;
}
