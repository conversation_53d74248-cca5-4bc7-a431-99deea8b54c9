package com.ruoyi.system.vo.shortplay;

import lombok.Data;

import java.io.Serializable;

/**
 * 短剧剧集VO
 *
 * <AUTHOR>
 */
@Data
public class ShortplayTvSeriesVo implements Serializable {
    private static final long serialVersionUID = -6558505872577648239L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 源表主键id
     */
    private String tbId;

    /**
     * 添加时间戳
     */
    private Long AddTime;

    /**
     * 添加时间字符串
     */
    private String AddTimeStr;

    /**
     * 小程序ID
     */
    private String appid;

    /**
     * 小程序名称
     */
    private String appname;

    /**
     * 是否推荐
     */
    private Integer isRecommend;

    /**
     * 被点赞数
     */
    private Integer likeNum;

    /**
     * 微信媒体ID
     */
    private Long mediaId;

    /**
     * 视频CRM链接
     */
    private String originalVideoSrc;

    /**
     * 金豆
     */
    private Integer pay;

    /**
     * 第几集
     */
    private Integer series;

    /**
     * 标题
     */
    private String title;

    /**
     * 短剧ID
     */
    private String tvId;

    /**
     * 封面图
     */
    private String tvImage;

    /**
     * 短剧名称
     */
    private String tvName;

    /**
     * 抖音短剧剧集ID
     */
    private String ttSeriesId;

    /**
     * 抖音短剧ID
     */
    private String ttVideoId;

    /**
     * 视频链接
     */
    private String videoSrc;

    /**
     * 是否可播放
     */
    private Boolean playable;
}
