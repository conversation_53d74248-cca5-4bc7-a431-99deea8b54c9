package com.ruoyi.system.vo.juliang;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: keboom
 * @date: 2024/7/15
 */
@Data
public class AccountVO {

    /**
     * 主键id
     */
    private String id;

    /**
     * 组织id
     */
    @Excel(name = "组织id")
    private Long accountId;

    /**
     * 组织名称
     */
    @Excel(name = "组织名称")
    private String accountName;

    /**
     * 公司名称，主体名称
     */
    @Excel(name = "公司名称，主体名称")
    private String accountNameCo;

    /**
     * 代理商名称
     */
    @Excel(name = "代理商名称")
    private String accountAgent;

    /**
     * 返点
     */
    @Excel(name = "返点")
    private BigDecimal commission;

    /**
     * 广告账户数量
     */
    @Excel(name = "广告账户数量")
    private int advertiserNum;

    /**
     * 主体数量
     */
    @Excel(name = "主体数量")
    private int companyNum;

    /**
     * 巨量token
     */
    @Excel(name = "巨量token")
    private String accessToken;

    /**
     * 刷新token
     */
    @Excel(name = "刷新token")
    private String refreshToken;

    /**
     * access token过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "access token过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiresIn;

    /**
     * refresh token 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "refresh token 过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiresInRefresh;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;
}
