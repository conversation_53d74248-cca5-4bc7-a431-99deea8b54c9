package com.ruoyi.system.vo.hotcake;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 爆款小程序渠道VO
 *
 * <AUTHOR>
 */
@Data
public class HotcakeChannelVO implements Serializable {
    private static final long serialVersionUID = -1081274659899544953L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 推广名称
     */
    private String promotionName;

    /**
     * 推广链接
     */
    private String promotionUrl;

    /**
     * 渠道号
     */
    private String channelCode;

    /**
     * 推广视频ID
     */
    private Long videoId;

    /**
     * 视频标题
     */
    private String videoTitle;
    /**
     * 投流免广，1免广，2有广
     */
    private Integer freeAd;

    /**
     * 分享ipu，即分享几个人进来则转化
     */
    private Integer shareIpu;
    /**
     * 点击广告ipu，即点击几次广告则转发
     */
    private Integer clickAdIpu;

    /**
     * 首页视频ID列表
     */
    private List<String> feedPageVideoIds;

    /**
     * 推荐视频ID列表
     */
    private List<String> playPageVideoIds;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
