package com.ruoyi.system.mapper.complain;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.ruoyi.system.entity.complain.ComplainRecordEntity;

/**
 * 投诉反馈Mapper接口
 *
 * <AUTHOR>
 */
public interface ComplainRecordMapper extends BaseMapper<ComplainRecordEntity>{
    /**
     * 查询投诉反馈
     *
     * @param id 投诉反馈主键
     * @return 投诉反馈
     */
    public ComplainRecordEntity selectComplainRecordById(Long id);

    /**
     * 查询投诉反馈列表
     *
     * @param complainRecord 投诉反馈
     * @return 投诉反馈集合
     */
    public List<ComplainRecordEntity> selectComplainRecordList(ComplainRecordEntity complainRecord);

    /**
     * 新增投诉反馈
     *
     * @param complainRecord 投诉反馈
     * @return 结果
     */
    public int insertComplainRecord(ComplainRecordEntity complainRecord);

    /**
     * 修改投诉反馈
     *
     * @param complainRecord 投诉反馈
     * @return 结果
     */
    public int updateComplainRecord(ComplainRecordEntity complainRecord);

    /**
     * 删除投诉反馈
     *
     * @param id 投诉反馈主键
     * @return 结果
     */
    public int deleteComplainRecordById(Long id);

    /**
     * 批量删除投诉反馈
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteComplainRecordByIds(Long[] ids);
}
