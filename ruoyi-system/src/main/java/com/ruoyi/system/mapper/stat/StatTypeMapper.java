package com.ruoyi.system.mapper.stat;

import java.util.List;
import com.ruoyi.system.entity.stat.StatTypeEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 埋点数值标识Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface StatTypeMapper {
    /**
     * 查询埋点数值标识
     *
     * @param id 埋点数值标识主键
     * @return 埋点数值标识
     */
    public StatTypeEntity selectStatTypeById(Integer id);

    /**
     * 查询埋点数值标识列表
     *
     * @param statType 埋点数值标识
     * @return 埋点数值标识集合
     */
    public List<StatTypeEntity> selectStatTypeList(StatTypeEntity statType);

    /**
     * 新增埋点数值标识
     *
     * @param statType 埋点数值标识
     * @return 结果
     */
    public int insertStatType(StatTypeEntity statType);

    /**
     * 修改埋点数值标识
     *
     * @param statType 埋点数值标识
     * @return 结果
     */
    public int updateStatType(StatTypeEntity statType);

    /**
     * 删除埋点数值标识
     *
     * @param id 埋点数值标识主键
     * @return 结果
     */
    public int deleteStatTypeById(Integer id);

    /**
     * 批量删除埋点数值标识
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStatTypeByIds(Integer[] ids);
    /**
     * 根据层级查询当前层级最大id
     * @param level
     * @return
     */
    Integer selectMaxIdByLevel(Integer level);

    List<StatTypeEntity> selectListByTypeIds(@Param("ids") List<Integer> ids, @Param("typeLevel") Integer typeLevel);

    /**
     * 根据备注查询埋点数值标识
     *
     * @param remark
     * @return
     */
    StatTypeEntity selectByRemark(String remark);
}
