package com.ruoyi.system.mapper.shortplay.app;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.ruoyi.system.entity.shortplay.app.ShortplayWxAppEntity;

/**
 * 短剧小程序微信应用Mapper接口
 *
 * <AUTHOR>
 */
public interface ShortplayWxAppMapper extends BaseMapper<ShortplayWxAppEntity> {

    /**
     * 查询短剧小程序微信应用
     *
     * @param appId 应用ID
     * @return 短剧小程序微信应用
     */
    ShortplayWxAppEntity selectByAppId(String appId);

    /**
     * 查询短剧小程序微信应用列表
     *
     * @param app 短剧小程序微信应用
     * @return 短剧小程序微信应用集合
     */
    List<ShortplayWxAppEntity> selectList(ShortplayWxAppEntity app);

    /**
     * 新增短剧小程序微信应用
     *
     * @param app 短剧小程序微信应用
     * @return 结果
     */
    int insert(ShortplayWxAppEntity app);

    /**
     * 修改短剧小程序微信应用
     *
     * @param app 短剧小程序微信应用
     * @return 结果
     */
    int update(ShortplayWxAppEntity app);
}
