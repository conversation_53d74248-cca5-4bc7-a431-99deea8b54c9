package com.ruoyi.system.mapper.dianzhong;

import com.ruoyi.system.entity.dianzhong.AgtDataSupplierSnapshotDianzhongEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: keboom
 * @date: 2024/8/20
 */
public interface DianzhongMapper {

    void saveOrUpdateBatch(List<AgtDataSupplierSnapshotDianzhongEntity> list);

    int selectPayCount(@Param("adId") String adId, @Param("dateReq") Date dateReq, @Param("hourReq") Integer hourReq);

    Long selectOrderCount(@Param("adId") String adId, @Param("dateReq") Date dateReq, @Param("hourReq") String hourReq);

    List<AgtDataSupplierSnapshotDianzhongEntity> selectIncomeByDay(@Param("start") String start,@Param("end") String end);

}
