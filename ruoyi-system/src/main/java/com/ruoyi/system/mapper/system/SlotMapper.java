package com.ruoyi.system.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.entity.system.SlotEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告位Mapper接口
 *
 * <AUTHOR>
 */
public interface SlotMapper extends BaseMapper<SlotEntity> {
    /**
     * 查询广告位
     *
     * @param id 广告位主键
     * @return 广告位
     */
    public SlotEntity selectSlotById(Long id);

    /**
     * 查询广告位
     *
     * @param id 广告位主键
     * @return 广告位
     */
    SlotEntity selectByKey(String key);

    /**
     * 查询广告位列表
     *
     * @param slot 广告位
     * @return 广告位集合
     */
    public List<SlotEntity> selectSlotList(SlotEntity slot);

    /**
     * 新增广告位
     *
     * @param slot 广告位
     * @return 结果
     */
    public int insertSlot(SlotEntity slot);

    /**
     * 修改广告位
     *
     * @param slot 广告位
     * @return 结果
     */
    public int updateSlot(SlotEntity slot);

    /**
     * 根据广告位id列表
     * @param ids
     * @return
     */
    List<SlotEntity> selectByIds(@Param("ids") List<Long> ids);

}
