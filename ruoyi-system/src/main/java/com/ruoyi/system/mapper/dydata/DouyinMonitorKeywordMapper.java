package com.ruoyi.system.mapper.dydata;

import java.util.List;
import com.ruoyi.system.entity.dydata.DouyinMonitorKeywordEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 抖音监测关键词Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface DouyinMonitorKeywordMapper {

    /**
     * 查询抖音监测关键词
     *
     * @param id 抖音监测关键词主键
     * @return 抖音监测关键词
     */
    DouyinMonitorKeywordEntity selectById(Long id);

    /**
     * 查询抖音监测关键词
     *
     * @param type 短剧类型
     * @return 抖音监测关键词
     */
    List<DouyinMonitorKeywordEntity> selectEnableKeywords(@Param("type") Integer type);

    /**
     * 查询抖音监测关键词列表
     *
     * @param douyinMonitorKeyword 抖音监测关键词
     * @return 抖音监测关键词集合
     */
    List<DouyinMonitorKeywordEntity> selectList(DouyinMonitorKeywordEntity douyinMonitorKeyword);

    /**
     * 新增抖音监测关键词
     *
     * @param entity 抖音监测关键词
     * @return 结果
     */
    int insert(DouyinMonitorKeywordEntity entity);

    /**
     * 批量新增抖音监测关键词
     *
     * @param entities 抖音监测关键词列表
     * @return 结果
     */
    int batchInsert(@Param("entities") List<DouyinMonitorKeywordEntity> entities);

    /**
     * 修改抖音监测关键词
     *
     * @param entity 抖音监测关键词
     * @return 结果
     */
    int update(DouyinMonitorKeywordEntity entity);

    /**
     * 修改抖音监测关键词
     *
     * @param ids ID列表
     * @param monitorStatus 状态
     * @return 结果
     */
    int batchUpdate(@Param("ids") List<Long> ids, @Param("monitorStatus") Integer monitorStatus);

    /**
     * 根据关键词称判断是否存在
     *
     * @param keyword 关键词
     * @param id 排除的ID
     * @return 是否存在
     */
    Integer existByKeyword(@Param("keyword") String keyword, @Param("id") Long id);

    /**
     * 将要开启的新剧数量
     */
    int countOpenKeywords(@Param("ids") List<Long> ids);
}
