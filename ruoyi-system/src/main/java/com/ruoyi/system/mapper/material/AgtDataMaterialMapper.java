package com.ruoyi.system.mapper.material;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.ruoyi.system.entity.material.AgtDataMaterialEntity;

/**
 * 巨量视频素材Mapper接口
 *
 * <AUTHOR>
 */
public interface AgtDataMaterialMapper extends BaseMapper<AgtDataMaterialEntity>{
    /**
     * 查询巨量视频素材
     *
     * @param id 巨量视频素材主键
     * @return 巨量视频素材
     */
    public AgtDataMaterialEntity selectAgtDataMaterialById(Long id);

    /**
     * 查询巨量视频素材列表
     *
     * @param agtDataMaterial 巨量视频素材
     * @return 巨量视频素材集合
     */
    public List<AgtDataMaterialEntity> selectAgtDataMaterialList(AgtDataMaterialEntity agtDataMaterial);

    /**
     * 新增巨量视频素材
     *
     * @param agtDataMaterial 巨量视频素材
     * @return 结果
     */
    public int insertAgtDataMaterial(AgtDataMaterialEntity agtDataMaterial);

    /**
     * 修改巨量视频素材
     *
     * @param agtDataMaterial 巨量视频素材
     * @return 结果
     */
    public int updateAgtDataMaterial(AgtDataMaterialEntity agtDataMaterial);

    /**
     * 删除巨量视频素材
     *
     * @param id 巨量视频素材主键
     * @return 结果
     */
    public int deleteAgtDataMaterialById(Long id);

    /**
     * 批量删除巨量视频素材
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAgtDataMaterialByIds(Long[] ids);
}
