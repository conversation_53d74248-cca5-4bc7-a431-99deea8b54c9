package com.ruoyi.system.mapper.shortplay.tf;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.ruoyi.system.entity.shortplay.tf.ShortplayDyTfurlEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 短剧投放设置Mapper接口
 *
 * <AUTHOR>
 */
public interface ShortplayDyTfurlMapper extends BaseMapper<ShortplayDyTfurlEntity>{

    ShortplayDyTfurlEntity selectByTfid(@Param("tfid") String tfid);

    /**
     * 查询短剧投放设置
     *
     * @param id 短剧投放设置主键
     * @return 短剧投放设置
     */
    public ShortplayDyTfurlEntity selectShortplayDyTfurlById(String id);

    /**
     * 查询短剧投放设置列表
     *
     * @param shortplayDyTfurl 短剧投放设置
     * @return 短剧投放设置集合
     */
    public List<ShortplayDyTfurlEntity> selectShortplayDyTfurlList(ShortplayDyTfurlEntity shortplayDyTfurl);

    /**
     * 新增短剧投放设置
     *
     * @param shortplayDyTfurl 短剧投放设置
     * @return 结果
     */
    public int insertShortplayDyTfurl(ShortplayDyTfurlEntity shortplayDyTfurl);

    /**
     * 修改短剧投放设置
     *
     * @param shortplayDyTfurl 短剧投放设置
     * @return 结果
     */
    public int updateShortplayDyTfurl(ShortplayDyTfurlEntity shortplayDyTfurl);

    /**
     * 删除短剧投放设置
     *
     * @param id 短剧投放设置主键
     * @return 结果
     */
    public int deleteShortplayDyTfurlById(String id);
    public int deleteShortplayDyTfurlByTbId(String id);

    /**
     * 批量删除短剧投放设置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortplayDyTfurlByIds(String[] ids);
}
