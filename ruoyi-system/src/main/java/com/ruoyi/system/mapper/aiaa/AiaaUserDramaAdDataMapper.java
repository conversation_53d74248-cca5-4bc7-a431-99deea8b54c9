package com.ruoyi.system.mapper.aiaa;


import com.ruoyi.system.entity.aiaa.AiaaUserDramaAdDataEntity;
import com.ruoyi.system.req.playlet.aiaa.ListAiaaUserDramaAdDataReq;

import java.util.List;

/**
 * 用户查看广告历史 Mapper
 *
 * <AUTHOR>
 * @date 2023年7月11日 下午5:46:44
 */
public interface AiaaUserDramaAdDataMapper {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(AiaaUserDramaAdDataEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    int updateById(AiaaUserDramaAdDataEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    AiaaUserDramaAdDataEntity selectById(Long id);
    List<AiaaUserDramaAdDataEntity> selectList(ListAiaaUserDramaAdDataReq req);

}
