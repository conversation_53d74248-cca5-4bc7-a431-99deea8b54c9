package com.ruoyi.system.mapper.novel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.entity.novel.NovelDayStatisticsEntity;
import com.ruoyi.system.req.novel.manage.NovelDayStatisticsReq;
import com.ruoyi.system.req.novel.web.NovelChannelDayStatAddReq;
import com.ruoyi.system.req.novel.web.NovelDayStatAddReq;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 小说日统计数据Mapper接口
 *
 * <AUTHOR>
 */
public interface NovelDayStatisticsMapper extends BaseMapper<NovelDayStatisticsEntity>{
    /**
     * 查询小说日统计数据
     *
     * @param id 小说日统计数据主键
     * @return 小说日统计数据
     */
    public NovelDayStatisticsEntity selectNovelDayStatisticsById(Long id);

    /**
     * 查询小说日统计数据列表
     *
     * @param req 小说日统计数据
     * @return 小说日统计数据集合
     */
    List<NovelDayStatisticsEntity> selectNovelDayStatisticsList(NovelDayStatisticsReq req);

    /**
     * 新增小说日统计数据
     *
     * @param novelDayStatistics 小说日统计数据
     * @return 结果
     */
    public int insertNovelDayStatistics(NovelDayStatisticsEntity novelDayStatistics);

    /**
     * 修改小说日统计数据
     *
     * @param novelDayStatistics 小说日统计数据
     * @return 结果
     */
    public int updateNovelDayStatistics(NovelDayStatisticsEntity novelDayStatistics);

    /**
     * 删除小说日统计数据
     *
     * @param id 小说日统计数据主键
     * @return 结果
     */
    public int deleteNovelDayStatisticsById(Long id);

    /**
     * 批量删除小说日统计数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNovelDayStatisticsByIds(Long[] ids);

    /**
     * 根据日期查询小说渠道日统计数据
     * @param today
     * @return
     */
    NovelDayStatisticsEntity selectByDateAndAppId(@Param("today") Date today, @Param("appId") String appId);

    /**
     * 统计当日日数据
     * @param dayReq
     * @return
     */
    int addNovelStat(NovelDayStatAddReq dayReq);
}
