package com.ruoyi.system.mapper.shortplay.record;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.system.entity.shortplay.record.ShortplayUnlockRecordEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 短剧小程序解锁记录Mapper接口
 *
 * <AUTHOR>
 */
public interface ShortplayUnlockRecordMapper extends BaseMapper<ShortplayUnlockRecordEntity> {

    /**
     * 查询短剧小程序解锁记录列表
     *
     * @param userId 用户ID
     * @param tvId 短剧ID
     * @return 短剧小程序解锁记录集合
     */
    List<Integer> selectSeriesByUserIdAndTvId(@Param("userId") Long userId, @Param("tvId") Long tvId);

    /**
     * 根据用户ID和时间范围统计解锁次数
     *
     * @param userId 用户ID
     * @param startDate 起始时间
     * @param endDate 结束时间
     * @return 解锁次数
     */
    Integer countByUserIdAndDate(@Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询短剧小程序解锁记录列表
     *
     * @param param 短剧小程序解锁记录
     * @return 短剧小程序解锁记录集合
     */
    List<ShortplayUnlockRecordEntity> selectList(ShortplayUnlockRecordEntity param);

    /**
     * 新增短剧小程序解锁记录
     *
     * @param record 短剧小程序解锁记录
     * @return 结果
     */
    int insert(ShortplayUnlockRecordEntity record);

    /**
     * 修改短剧小程序解锁记录
     *
     * @param record 短剧小程序解锁记录
     * @return 结果
     */
    int update(ShortplayUnlockRecordEntity record);

    /**
     * 批量新增短剧小程序解锁记录
     *
     * @param records 短剧小程序解锁记录列表
     * @return 结果
     */
    int batchInsert(@Param("records") List<ShortplayUnlockRecordEntity> records);

    /**
     * 清空表
     * @return
     */
    int truncateTable();
}
