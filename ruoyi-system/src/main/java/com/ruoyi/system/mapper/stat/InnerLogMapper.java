package com.ruoyi.system.mapper.stat;

import java.util.List;
import com.ruoyi.system.entity.stat.InnerLog;

/**
 * 埋点日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-09
 */
public interface InnerLogMapper 
{
    /**
     * 查询埋点日志
     * 
     * @param id 埋点日志主键
     * @return 埋点日志
     */
    public InnerLog selectInnerLogById(String id);

    /**
     * 查询埋点日志列表
     * 
     * @param innerLog 埋点日志
     * @return 埋点日志集合
     */
    public List<InnerLog> selectInnerLogList(InnerLog innerLog);

    /**
     * 新增埋点日志
     * 
     * @param innerLog 埋点日志
     * @return 结果
     */
    public int insertInnerLog(InnerLog innerLog);

    /**
     * 修改埋点日志
     * 
     * @param innerLog 埋点日志
     * @return 结果
     */
    public int updateInnerLog(InnerLog innerLog);

    /**
     * 删除埋点日志
     * 
     * @param id 埋点日志主键
     * @return 结果
     */
    public int deleteInnerLogById(String id);

    /**
     * 批量删除埋点日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInnerLogByIds(String[] ids);
}
