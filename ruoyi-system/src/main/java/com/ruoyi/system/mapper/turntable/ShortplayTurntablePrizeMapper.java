package com.ruoyi.system.mapper.turntable;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.ruoyi.system.entity.turntable.ShortplayTurntablePrizeEntity;

/**
 * 短剧转盘奖品Mapper接口
 *
 * <AUTHOR>
 */
public interface ShortplayTurntablePrizeMapper extends BaseMapper<ShortplayTurntablePrizeEntity>{
    /**
     * 查询短剧转盘奖品
     *
     * @param id 短剧转盘奖品主键
     * @return 短剧转盘奖品
     */
    public ShortplayTurntablePrizeEntity selectShortplayTurntablePrizeById(Long id);

    /**
     * 查询短剧转盘奖品列表
     *
     * @param shortplayTurntablePrize 短剧转盘奖品
     * @return 短剧转盘奖品集合
     */
    public List<ShortplayTurntablePrizeEntity> selectShortplayTurntablePrizeList(ShortplayTurntablePrizeEntity shortplayTurntablePrize);

    /**
     * 新增短剧转盘奖品
     *
     * @param shortplayTurntablePrize 短剧转盘奖品
     * @return 结果
     */
    public int insertShortplayTurntablePrize(ShortplayTurntablePrizeEntity shortplayTurntablePrize);

    /**
     * 修改短剧转盘奖品
     *
     * @param shortplayTurntablePrize 短剧转盘奖品
     * @return 结果
     */
    public int updateShortplayTurntablePrize(ShortplayTurntablePrizeEntity shortplayTurntablePrize);

    /**
     * 删除短剧转盘奖品
     *
     * @param id 短剧转盘奖品主键
     * @return 结果
     */
    public int deleteShortplayTurntablePrizeById(Long id);

    /**
     * 批量删除短剧转盘奖品
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortplayTurntablePrizeByIds(Long[] ids);

    /**
     * 获取大转盘配置
     * @return
     */
    List<ShortplayTurntablePrizeEntity> selectTurntableList();

}
