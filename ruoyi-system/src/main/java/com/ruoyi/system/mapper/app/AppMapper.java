package com.ruoyi.system.mapper.app;

import com.ruoyi.system.entity.app.AppEntity;
import com.ruoyi.system.req.playlet.app.AppReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_app(app信息)】的数据库操作Mapper
 * @createDate 2023-09-20 19:58:30
 * @Entity cn.com.nuohe.playlet.manager.entity.app.AppEntity
 */
public interface AppMapper {


    /**
     * 根据条件查询app
     *
     * @param req
     * @return
     */
    List<AppEntity> selectAppByReq(AppReq req);

    /**
     * 新增app信息
     *
     * @param entity
     * @return
     */
    Boolean insert(AppEntity entity);

    /**
     * 根据id查询app信息
     *
     * @param ids
     * @return
     */
    List<AppEntity> selectAppByIds(@Param("ids") List<Long> ids);


    /**
     * 根据app名搜索app列表
     * @param appName app名称
     * @return app列表
     */
    List<AppEntity> selectAppByName(@Param("appName") String appName);
}




