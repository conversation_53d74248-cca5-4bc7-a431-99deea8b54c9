package com.ruoyi.system.mapper.douyin;

import com.ruoyi.system.entity.douyin.TbDouyinPublishVideoBill;

import java.util.List;

/**
 * 抖音小程序推广视频数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface TbDouyinPublishVideoBillMapper
{
    /**
     * 查询抖音小程序推广视频数据
     *
     * @param id 抖音小程序推广视频数据主键
     * @return 抖音小程序推广视频数据
     */
    public TbDouyinPublishVideoBill selectTbDouyinPublishVideoBillById(String id);

    /**
     * 查询抖音小程序推广视频数据列表
     *
     * @param tbDouyinPublishVideoBill 抖音小程序推广视频数据
     * @return 抖音小程序推广视频数据集合
     */
    public List<TbDouyinPublishVideoBill> selectTbDouyinPublishVideoBillList(TbDouyinPublishVideoBill tbDouyinPublishVideoBill);

    /**
     * 新增抖音小程序推广视频数据
     *
     * @param tbDouyinPublishVideoBill 抖音小程序推广视频数据
     * @return 结果
     */
    public int insertTbDouyinPublishVideoBill(TbDouyinPublishVideoBill tbDouyinPublishVideoBill);

    /**
     * 修改抖音小程序推广视频数据
     *
     * @param tbDouyinPublishVideoBill 抖音小程序推广视频数据
     * @return 结果
     */
    public int updateTbDouyinPublishVideoBill(TbDouyinPublishVideoBill tbDouyinPublishVideoBill);

    /**
     * 删除抖音小程序推广视频数据
     *
     * @param id 抖音小程序推广视频数据主键
     * @return 结果
     */
    public int deleteTbDouyinPublishVideoBillById(String id);

    /**
     * 批量删除抖音小程序推广视频数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbDouyinPublishVideoBillByIds(String[] ids);
}
