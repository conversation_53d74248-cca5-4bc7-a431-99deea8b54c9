package com.ruoyi.system.mapper.shortplay.record;

import com.ruoyi.system.entity.shortplay.record.ShortplayIaaConversionRecord;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 抖音IAA回传记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface ShortplayIaaConversionRecordMapper {

    /**
     * 查询抖音IAA回传记录
     *
     * @param id 抖音IAA回传记录主键
     * @return 抖音IAA回传记录
     */
    ShortplayIaaConversionRecord selectShortplayIaaConversionRecordById(Long id);

    /**
     * 查询抖音IAA回传记录列表
     *
     * @param record 抖音IAA回传记录
     * @return 抖音IAA回传记录集合
     */
    List<ShortplayIaaConversionRecord> selectShortplayIaaConversionRecordList(ShortplayIaaConversionRecord record);

    /**
     * 查询抖音IAA回传记录列表
     */
    List<ShortplayIaaConversionRecord> selectListByConfigId(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("configId") String configId, @Param("promotionId") String promotionId);

    /**
     * 新增抖音IAA回传记录
     *
     * @param record 抖音IAA回传记录
     * @return 结果
     */
    int insertShortplayIaaConversionRecord(ShortplayIaaConversionRecord record);

    /**
     * 修改抖音IAA回传记录
     *
     * @param record 抖音IAA回传记录
     * @return 结果
     */
    int updateShortplayIaaConversionRecord(ShortplayIaaConversionRecord record);

    BigDecimal sumConvCostForFixedCost(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("configId") String configId, @Param("promotionId") String promotionId);
}
