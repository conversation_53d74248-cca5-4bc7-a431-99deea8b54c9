package com.ruoyi.system.mapper.tag;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.ruoyi.system.entity.tag.MatrixMaterialTagEntity;

/**
 * 爆量工具标签Mapper接口
 *
 * <AUTHOR>
 */
public interface MatrixMaterialTagMapper extends BaseMapper<MatrixMaterialTagEntity>{
    /**
     * 查询爆量工具标签
     *
     * @param id 爆量工具标签主键
     * @return 爆量工具标签
     */
    public MatrixMaterialTagEntity selectMatrixMaterialTagById(Long id);

    /**
     * 查询爆量工具标签列表
     *
     * @param matrixMaterialTag 爆量工具标签
     * @return 爆量工具标签集合
     */
    public List<MatrixMaterialTagEntity> selectMatrixMaterialTagList(MatrixMaterialTagEntity matrixMaterialTag);

    /**
     * 新增爆量工具标签
     *
     * @param matrixMaterialTag 爆量工具标签
     * @return 结果
     */
    public int insertMatrixMaterialTag(MatrixMaterialTagEntity matrixMaterialTag);

    /**
     * 修改爆量工具标签
     *
     * @param matrixMaterialTag 爆量工具标签
     * @return 结果
     */
    public int updateMatrixMaterialTag(MatrixMaterialTagEntity matrixMaterialTag);

    /**
     * 删除爆量工具标签
     *
     * @param id 爆量工具标签主键
     * @return 结果
     */
    public int deleteMatrixMaterialTagById(Long id);

    /**
     * 批量删除爆量工具标签
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMatrixMaterialTagByIds(Long[] ids);
}
