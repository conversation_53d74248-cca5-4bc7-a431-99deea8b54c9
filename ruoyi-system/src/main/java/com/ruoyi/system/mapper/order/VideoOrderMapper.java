package com.ruoyi.system.mapper.order;

import com.ruoyi.system.bo.playlet.ConsumerSumBo;
import com.ruoyi.system.entity.order.VideoOrderEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单表 Mapper
 *
 * <AUTHOR>
 * @date 2023-7-4 19:35:49
 */
public interface VideoOrderMapper {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(VideoOrderEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    int updateById(VideoOrderEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    VideoOrderEntity selectById(Long id);


    /**
     * 查询消费总金额
     * @param userIds
     * @return
     */
    List<ConsumerSumBo> selectConsumerSum(@Param("userIds") List<Long> userIds);

    /**
     * 根据id列表查询
     * @param ids
     * @return
     */
    List<VideoOrderEntity> selectListByIds(@Param("ids") List<Long> ids);
}
