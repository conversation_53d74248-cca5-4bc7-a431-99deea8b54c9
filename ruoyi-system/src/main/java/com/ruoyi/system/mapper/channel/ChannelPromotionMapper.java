package com.ruoyi.system.mapper.channel;

import com.ruoyi.system.entity.channel.ChannelPromotionEntity;
import com.ruoyi.system.req.playlet.channel.ChannelListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 渠道推广表 Mapper
 *
 * <AUTHOR>
 * @date 2023-7-6 16:21:02
 */
public interface ChannelPromotionMapper {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(ChannelPromotionEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    int updateById(ChannelPromotionEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    ChannelPromotionEntity selectById(Long id);

    /**
     * 获取渠道列表
     */
    List<ChannelPromotionEntity> selectList(@Param("req") ChannelListReq req);

    /**
     * 根据id列表查询渠道信息
     * @param ids
     * @return
     */
    List<ChannelPromotionEntity> selectListByIds(@Param("ids") List<Long> ids);
}
