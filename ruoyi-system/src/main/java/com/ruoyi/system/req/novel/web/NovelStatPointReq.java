package com.ruoyi.system.req.novel.web;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;

/**
 * 埋点请求
 * <AUTHOR>
 * @date 2024/5/30 18:18
 */
@Data
public class NovelStatPointReq implements Serializable {
    private static final long serialVersionUID = 5316650238796180531L;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 埋点类型，1广告曝光
     * @see com.ruoyi.system.constant.enums.NovelStatTypeEnum
     */
    private Integer type;

    /**
     * 完整请求数据
     * 当有用户区分时，data中可以增加 userId属性
     */
    private JSONObject data;
}
