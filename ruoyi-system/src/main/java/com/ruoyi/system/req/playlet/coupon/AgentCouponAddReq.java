package com.ruoyi.system.req.playlet.coupon;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 短剧小程序媒体权益添加参数
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Data
public class AgentCouponAddReq implements Serializable {
    private static final long serialVersionUID = -7062385438124898012L;

    /**
     * 代理商ID
     */
    @NotEmpty(message = "无效的代理商ID")
    private String agentId;

    /**
     * 代理商昵称
     */
    private String agentNickname;

    /**
     * 代理商用户名
     */
    private String agentUsername;

    /**
     * 机构ID
     */
    private String inviteCode;

    /**
     * 权益类型:1.日度会员,2.周度会员,3.月度会员,4.季度会员,5.年度会员,6.解锁全集
     */
    @NotNull(message = "无效的权益类型")
    private Integer couponType;

    /**
     * 权益名称
     */
    @NotEmpty(message = "权益名称")
    private String couponName;

    /**
     * 权益说明
     */
    @NotEmpty(message = "权益说明")
    private String couponDesc;

    /**
     * 权益数
     */
    @NotNull(message = "无效的权益类型")
    private Integer couponNum;

    /**
     * 权益领取有效期(天)
     */
    @NotNull(message = "权益领取有效期")
    private Integer couponClaimExpire;

    /**
     * 权益使用有效期(天)
     */
    private Integer couponUseExpire;

    /**
     * 操作人名称
     */
    @NotEmpty(message = "操作人名称")
    private String operatorName;
}
