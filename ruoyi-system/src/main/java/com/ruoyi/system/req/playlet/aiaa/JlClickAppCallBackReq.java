package com.ruoyi.system.req.playlet.aiaa;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> Yang
 * @Date 2023/7/31 14:19
 * @Description 巨量点击检测接口参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JlClickAppCallBackReq implements Serializable {


    private static final long serialVersionUID = 7387553299291401507L;
    /**
     * ⽤户客户端的浏览器原始IP
     */
    private String ip;
    /**
     * ⽤户客户端的浏览器原始UserAgent
     */
    private String userAgent;

    /**
     * 平台类型
     *
     * @see cn.com.nuohe.playlet.manager.constant.PlatformType
     */
    @JsonIgnore
    private Integer platformType;
    /**
     * ⼴告平台发券唯⼀订单标识
     */
    @NotNull
    private String nadKey;
    /**
     * 安卓⽤户oaid设备号32位，⼩写md5
     */
    private String oaidMd5;

    /**
     * 安卓⽤户imei设备号32位，⼩写md5
     */
    private String imeiMd5;
    /**
     * 苹果⽤户idfa设备号32位，⼩写md5
     */
    private String idfaMd5;
    /**
     * 观看广告次数上报阈值
     */
    private int videoTimes=1;
    /**
     * pecpm
     */
    private double pecpm=0;

    /**
     * 看广告次数
     */
    private int viewAdCount;

    /**
     * 渠道
     */
    private String channel;
}
