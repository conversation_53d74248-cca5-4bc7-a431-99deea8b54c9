package com.ruoyi.system.req.hotcake;

import com.ruoyi.system.bo.hotcake.MiniappBasicConfigBo;
import com.ruoyi.system.bo.hotcake.MiniappSlotConfigBo;
import lombok.Data;

import java.io.Serializable;

/**
 * 爆款小程序配置
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@Data
public class HotcakeMiniappReq implements Serializable {
    private static final long serialVersionUID = 3844803174476494672L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 小程序appSecret
     */
    private String appSecret;

    /**
     * 小程序名称
     */
    private String appName;

    /**
     * 广告位配置
     */
    private MiniappSlotConfigBo slotConfig;

    /**
     * 基础配置
     */
    private MiniappBasicConfigBo basicConfig;
}
