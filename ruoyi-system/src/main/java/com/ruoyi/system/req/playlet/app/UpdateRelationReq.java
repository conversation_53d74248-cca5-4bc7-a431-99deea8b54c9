package com.ruoyi.system.req.playlet.app;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 修改支付通道和应用关联关系
 *
 * <AUTHOR>
 * @Date 2023/10/12 16:22
 */
@Data
public class UpdateRelationReq implements Serializable {

    private static final long serialVersionUID = 6794384168896207105L;


    /**
     * 支付通道Id
     */
    @NotNull(message = "支付通道id不能为空")
    private Long payChannelId;
    /**
     * 关联的app列表
     */
    private List<Long> appList;

}
