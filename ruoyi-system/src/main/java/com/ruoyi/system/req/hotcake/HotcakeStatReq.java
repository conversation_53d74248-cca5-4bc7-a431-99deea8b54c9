package com.ruoyi.system.req.hotcake;

import lombok.Data;

/**
 * 爆款小程序埋点
 */
@Data
public class HotcakeStatReq {

    /**
     * 用户ID，未登录时前端传随机值
     */
    private String userId;

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 渠道码
     */
    private String channelCode;

    /**
     * 类型:1.注册(后端处理),2.登录(后端处理),3.播放,4.广告曝光,5.广告点击
     */
    private Integer type;

    /**
     * 页面:1.列表页,2.播放页
     */
    private String page;

    /**
     * 广告位置: 1.首屏,2.插屏,3.列表页,4.播放页,5.视频解锁,6.左上角返回,7.物理返回,8.关闭
     */
    private String position;

    /**
     * 视频ID(播放时传)
     */
    private Long videoId;
}
