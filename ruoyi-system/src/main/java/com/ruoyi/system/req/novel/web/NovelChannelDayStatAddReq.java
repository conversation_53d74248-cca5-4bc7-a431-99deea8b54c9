package com.ruoyi.system.req.novel.web;

import lombok.Data;

import java.io.Serializable;

/**
 * 小说日数据增量数据
 * <AUTHOR>
 * @date 2024/10/11 16:46
 */
@Data
public class NovelChannelDayStatAddReq implements Serializable {
    private static final long serialVersionUID = -223441672565266124L;

    private Long id;
    /** 新增用户数 */
    private Integer newUserCountAdd;

    /** 小说阅读人数 */
    private Integer watchUserCountAdd;

    /** 阅读总章数 */
    private Integer watchChapterSumAdd;

    /** 广告曝光次数 */
    private Integer adExposureAdd;

    /** 广告解锁次数 */
    private Integer adUnlockPvAdd;

    /** 广告解锁人数 */
    private Integer adUnlockUvAdd;

}
