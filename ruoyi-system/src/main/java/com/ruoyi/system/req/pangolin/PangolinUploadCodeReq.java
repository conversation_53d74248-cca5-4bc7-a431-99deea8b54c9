package com.ruoyi.system.req.pangolin;

import lombok.Data;

import java.io.Serializable;

/**
 * 抖音授权小程序提交代码
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Data
public class PangolinUploadCodeReq implements Serializable {
    private static final long serialVersionUID = 2485345713705114588L;

    /**
     * 授权小程序应用 appid
     */
    private String appId;

    /**
     * ext.json 配置的字符串形式
     */
    private String extJson;

    /**
     * 模板 id，取自第三方小程序应用【开发】-【模板库】
     */
    private Long templateId;

    /**
     * 提交描述
     */
    private String userDesc;

    /**
     * 提交版本,如 1.1.1
     */
    private String userVersion;

    /**
     * 上传测试版本至哪个通道:空.默认通道,1.通道1
     */
    private String tag;
}
