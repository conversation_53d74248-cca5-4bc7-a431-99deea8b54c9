package com.ruoyi.system.req.playlet.aiaa;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增iaa用户
 *
 * @date 2023/7/6 17:00
 */
@Data
public class AddUserReq implements Serializable {
    /**
     * 用户昵称
     */
    private String name;
    private String ua;

    /**
     * 渠道id
     */
    @NotNull(message = "渠道id，不能为空")
    private Long channelId;

    /**
     * imei_md5
     */
    private String imeiMd5;
    private String imei;

    /**
     * oaid_md5
     */
    @NotNull(message = "oaidMd5，不能为空")
    private String oaidMd5;
    private String oaid;
}
