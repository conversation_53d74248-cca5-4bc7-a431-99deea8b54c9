package com.ruoyi.system.req.playlet.video;

import lombok.Data;

import java.io.Serializable;

/**
 * 短剧搜索req
 * <AUTHOR>
 * @date 2023/7/4 16:07
 */
@Data
public class VideoGroupSearchReq implements Serializable {
    private static final long serialVersionUID = -5195036912195146946L;
    /**
     * 短剧id
     */
    private Long id;

    /**
     * 短剧标题
     */
    private String title;

    /**
     * 短剧上架状态
     * @see cn.com.nuohe.playlet.manager.common.enums.PublishStatusEnum
     */
    private Integer status;

    /**
     * ssp客户id
     */
    private Long sspAccountId;

    /**
     * 微信审核状态
     * @see cn.com.nuohe.playlet.manager.common.enums.WxAuditStatusEnum
     */
    private Integer wxAuditStatus;
    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方式 升序:asc 降序:desc
     */
    private String sortType;
}
