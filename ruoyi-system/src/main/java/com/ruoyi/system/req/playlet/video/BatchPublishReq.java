package com.ruoyi.system.req.playlet.video;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 批量上下架短剧
 * <AUTHOR>
 * @date 2024/3/21 10:45
 */
@Data
public class BatchPublishReq implements Serializable {
    private static final long serialVersionUID = 7584701338220855387L;
    /**
     * 短剧id列表
     */
    @NotEmpty(message = "id列表不能为空")
    private List<Long> ids;
    /**
     * 上下架状态，0下架 1上架
     * @see cn.com.nuohe.playlet.manager.common.enums.PublishStatusEnum
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}
