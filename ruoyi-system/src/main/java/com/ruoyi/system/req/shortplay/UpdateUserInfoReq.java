package com.ruoyi.system.req.shortplay;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新用户信息请求参数
 * <AUTHOR>
 * @date 2024/11/28 10:47
 */
@Data
public class UpdateUserInfoReq implements Serializable {
    private static final long serialVersionUID = -8055649505742760905L;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 昵称
     */
    private String nickname;

    /**
     * 用户id
     */
    @NotNull(message = "参数错误")
    private Long userId;
}
