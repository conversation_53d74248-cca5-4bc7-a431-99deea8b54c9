package com.ruoyi.system.req.playlet.video;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 上传短剧req
 * <AUTHOR>
 * @date 2023/7/5 20:35
 */
@Data
public class UploadVideoInfoReq implements Serializable {

    private static final long serialVersionUID = -2273915753933826528L;
    /**
     * 视频标题
     */
    @NotNull(message = "视频标题不能为空")
    private String title;
    /**
     * 剧集
     */
    @NotNull(message = "剧集不能为空")
    private Integer episode;

    /**
     * 视频URL地址
     */
    @NotNull(message = "视频URL地址不能为空")
    private String videoUrl;
    /**
     * 视频时长
     */
    @NotNull(message = "视频时长不能为空")
    private Integer duration;
}
