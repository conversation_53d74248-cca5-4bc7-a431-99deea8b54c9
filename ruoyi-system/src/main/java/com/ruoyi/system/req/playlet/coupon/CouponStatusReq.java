package com.ruoyi.system.req.playlet.coupon;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 券申请参数
 * <AUTHOR>
 * @date 2024/4/22 15:00
 */
@Data
public class CouponStatusReq implements Serializable {
    private static final long serialVersionUID = -5011441587557508211L;

    /**
     * 数字签名
     */
    @NotEmpty(message = "无效签名")
    private String sign;

    /**
     * 订单标识，唯一
     */
    @NotEmpty(message = "无效订单号")
    private String orderId;
}
