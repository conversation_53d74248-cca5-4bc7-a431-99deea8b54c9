package com.ruoyi.system.req.novel.wx;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 微信小说授权请求参数
 *
 * <AUTHOR>
 * @date 2023/7/13 17:23
 */
@Data
public class WxMaNovelAuthBookRequest implements Serializable {

    private static final long serialVersionUID = -5118832228210273684L;
    /**
     * 分页id 初始为0
     */
    @SerializedName("book_id")
    private String book_id;
    /**
     * 授权小程序id
     */
    @SerializedName("grantee_appid")
    private String grantee_appid;
    /**
     * 授权时间
     */
    @SerializedName("expire_time")
    private long expire_time = 2147483646L;

}
