package com.ruoyi.system.req.playlet.wx;

import com.google.gson.annotations.SerializedName;
import com.ruoyi.system.bo.playlet.VideoGroupActorInfoListBo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 微信剧目提审请求参数
 * <AUTHOR>
 * @date 2023/7/13 17:23
 */
@Data
public class WxMaSecAuditDramaRequest implements Serializable {

    private static final long serialVersionUID = -5293960825067132791L;

    /**
     * 剧目id
     */
    @SerializedName("drama_id")
    private Long dramaId;

    /**
     * 剧名
     */
    @SerializedName("name")
    private String name;

    /**
     * 剧集数
     */
    @SerializedName("media_count")
    private Integer mediaCount;
    /**
     * 剧集媒资media_id 列表
     */
    @SerializedName("media_id_list")
    private List<Long> mediaIdList;
    /**
     * 简介
     */
    private String description;
    /**
     * 封面图片临时media_id
     */
    @SerializedName("cover_material_id")
    private String coverMaterialId;
    /**
     * 制作方
     */
    @SerializedName("producer")
    private String producer;
    /**
     * 剧目授权材料material_id
     */
    @SerializedName("authorized_material_id")
    private String authorizedMaterialId;

    /**
     * 演员列表
     */
    @SerializedName("actor_list")
    private VideoGroupActorInfoListBo actorList;
    /**
     * 剧目备案号
     */
    @SerializedName("registration_number")
    private String registrationNumber;
    /**
     * 网络剧片发行许可证编号 以上二选一
     */
    @SerializedName("publish_license")
    private String publishLicense;
    /**
     * 网络剧片发行许可证编号图片id ，许可证编号为非空，则该字段必填
     */
    @SerializedName("publish_license_material_id")
    private String publishLicenseMaterialId;

    /**
     * 剧目资质
     */
    @SerializedName("qualification_type")
    private Integer qualificationType;

    /**
     * 剧目制作成本（单位：万元），当qualification_type=2时必填
     */
    @SerializedName("cost_of_production")
    private Integer costOfProduction;
    /**
     * 《成本配置比例情况报告》material_id
     */
    @SerializedName("cost_commitment_letter_material_id")
    private String costCommitmentLetterMaterialId;
}