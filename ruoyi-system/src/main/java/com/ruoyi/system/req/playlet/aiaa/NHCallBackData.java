package com.ruoyi.system.req.playlet.aiaa;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/7/31 15:00
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NHCallBackData {

    /**
     * 订单号，从落地⻚获取nadKey参数的值
     */
    private String orderNo;
    /**
     * OAID的MD5
     */
    private String oaidMd5;
    /**
     * IMEI的MD5
     */
    private String imeiMd5;
    /**
     * IDFA的MD5
     */
    private String idfaMd5;
    /**
     * 转化类型
     */
    private Integer status;
    /**
     * 诺禾平台accessKey
     */
    private String accesskey;

    /**
     * 当前时间(毫秒时间戳)
     */
    private Long timestamp;
    /**
     * ip
     */
    private String ip;
    /**
     * ua
     */
    private String userAgent;

}
