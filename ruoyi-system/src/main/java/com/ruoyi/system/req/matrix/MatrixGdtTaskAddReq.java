package com.ruoyi.system.req.matrix;

import com.ruoyi.system.bo.matrix.MatrixGdtTaskInfoBo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 爆量工具任务新增请求参数
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
public class MatrixGdtTaskAddReq implements Serializable {
    private static final long serialVersionUID = -4824506584009396801L;

    /**
     * 复制的广告账户ID
     */
    private Long advertisementId;

    /**
     * 复制的计划id列表
     */
    private List<Long> adgroupIds;

    /**
     * 任务详情
     */
    private MatrixGdtTaskInfoBo taskInfo;
}
