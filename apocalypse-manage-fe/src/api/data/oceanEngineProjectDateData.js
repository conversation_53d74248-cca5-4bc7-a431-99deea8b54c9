import request from '@/utils/request'

// 查询巨量广告账号项目维度日数据列表
export function listOceanEngineProjectDateData(params) {
  const { pageNum, pageSize, ...data } = params;
  return request({
    url: '/data/oceanEngineProjectDateData/dataDetail',
    method: 'post',
    headers: {
      repeatSubmit: false,
    },
    params: {
      pageNum,
      pageSize,
    },
    data,
  })
}

// 查询巨量广告账号项目维度日数据详细
export function getOceanEngineProjectDateData(id) {
  return request({
    url: '/data/oceanEngineProjectDateData/' + id,
    method: 'get'
  })
}

// 新增巨量广告账号项目维度日数据
export function addOceanEngineProjectDateData(data) {
  return request({
    url: '/data/oceanEngineProjectDateData/add',
    method: 'post',
    data: data
  })
}

// 修改巨量广告账号项目维度日数据
export function updateOceanEngineProjectDateData(data) {
  return request({
    url: '/data/oceanEngineProjectDateData/update',
    method: 'post',
    data: data
  })
}

// 删除巨量广告账号项目维度日数据
export function delOceanEngineProjectDateData(id) {
  return request({
    url: '/data/oceanEngineProjectDateData/' + id,
    method: 'delete'
  })
}
