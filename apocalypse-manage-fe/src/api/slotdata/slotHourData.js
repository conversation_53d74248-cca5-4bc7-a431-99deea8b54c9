import request from '@/utils/request'

// 查询广告位地域分流日小时数据列表
export function listSlotHourData(query) {
  return request({
    url: '/slotdata/slotHourData/list',
    method: 'get',
    params: query
  })
}

// 查询广告位地域分流日小时数据详细
export function getSlotHourData(id) {
  return request({
    url: '/slotdata/slotHourData/' + id,
    method: 'get'
  })
}

// 新增广告位地域分流日小时数据
export function addSlotHourData(data) {
  return request({
    url: '/slotdata/slotHourData/add',
    method: 'post',
    data: data
  })
}

// 修改广告位地域分流日小时数据
export function updateSlotHourData(data) {
  return request({
    url: '/slotdata/slotHourData/update',
    method: 'post',
    data: data
  })
}

// 删除广告位地域分流日小时数据
export function delSlotHourData(id) {
  return request({
    url: '/slotdata/slotHourData/' + id,
    method: 'delete'
  })
}
