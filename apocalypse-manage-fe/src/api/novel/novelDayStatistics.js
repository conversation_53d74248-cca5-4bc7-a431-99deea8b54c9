import request from '@/utils/request'

// 查询小说日统计数据列表
export function listNovelDayStatistics(query) {
  return request({
    url: '/novel/novelDayStatistics/list',
    method: 'get',
    params: query
  })
}

// 查询小说日统计数据详细
export function getNovelDayStatistics(id) {
  return request({
    url: '/novel/novelDayStatistics/' + id,
    method: 'get'
  })
}

// 新增小说日统计数据
export function addNovelDayStatistics(data) {
  return request({
    url: '/novel/novelDayStatistics/add',
    method: 'post',
    data: data
  })
}

// 修改小说日统计数据
export function updateNovelDayStatistics(data) {
  return request({
    url: '/novel/novelDayStatistics/update',
    method: 'post',
    data: data
  })
}

// 删除小说日统计数据
export function delNovelDayStatistics(id) {
  return request({
    url: '/novel/novelDayStatistics/' + id,
    method: 'delete'
  })
}
