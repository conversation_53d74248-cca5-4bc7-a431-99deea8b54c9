import request from '@/utils/request'

// 查询小说列表
export function listNovel(query) {
  return request({
    url: '/novel/novel/list',
    method: 'get',
    params: query
  })
}
export function listAll(novelAppChannelType) {
  return request({
    url: '/novel/novel/listAll?novelAppChannelType=' + novelAppChannelType,
    method: 'get',
  })
}
export function getMiniappPullDownList() {
  return request({
    url: '/novel/novelMiniapp/getPullDownList',
    method: 'get',
  })
}
// 查询小说详细
export function getNovel(id) {
  return request({
    url: '/novel/novel/' + id,
    method: 'get'
  })
}

// 新增小说
export function addNovel(data) {
  return request({
    url: '/novel/novel/add',
    method: 'post',
    data: data
  })
}

// 修改小说
export function updateNovel(data) {
  return request({
    url: '/novel/novel/update',
    method: 'post',
    data: data
  })
}
// 修改小说
export function updateOnline(data) {
  return request({
    url: '/novel/novel/updateOnline',
    method: 'post',
    data: data
  })
}
export function syncWxNovel() {
  return request({
    url: '/novel/novel/syncWxNovel',
    method: 'get'
  })
}

// 同步穿山甲小说
export function syncDyNovel() {
  return request({
    url: '/novel/novel/syncDyNovel',
    method: 'get'
  })
}

// 删除小说
export function delNovel(id) {
  return request({
    url: '/novel/novel/' + id,
    method: 'delete'
  })
}
