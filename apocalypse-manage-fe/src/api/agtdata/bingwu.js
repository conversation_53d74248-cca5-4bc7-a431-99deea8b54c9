import request from '@/utils/request'

// 查询炳午快照列表
export function listBingwu(query) {
  return request({
    url: '/agtdata/bingwu/list',
    method: 'get',
    params: query
  })
}

// 查询炳午快照详细
export function getBingwu(id) {
  return request({
    url: '/agtdata/bingwu/' + id,
    method: 'get'
  })
}

// 新增炳午快照
export function addBingwu(data) {
  return request({
    url: '/agtdata/bingwu/add',
    method: 'post',
    data: data
  })
}

// 修改炳午快照
export function updateBingwu(data) {
  return request({
    url: '/agtdata/bingwu/update',
    method: 'post',
    data: data
  })
}

// 删除炳午快照
export function delBingwu(id) {
  return request({
    url: '/agtdata/bingwu/' + id,
    method: 'delete'
  })
}
