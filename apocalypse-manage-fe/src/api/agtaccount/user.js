import request from '@/utils/request'

// 查询短剧分销人员列表
export function listUser(query) {
  return request({
    url: '/agtaccount/user/list',
    method: 'get',
    params: query
  })
}

// 查询短剧分销人员详细
export function getUser(id) {
  return request({
    url: '/agtaccount/user/' + id,
    method: 'get'
  })
}

// 新增短剧分销人员
export function addUser(data) {
  return request({
    url: '/agtaccount/user/add',
    method: 'post',
    data: data
  })
}

// 修改短剧分销人员
export function updateUser(data) {
  return request({
    url: '/agtaccount/user/update',
    method: 'post',
    data: data
  })
}

// 删除短剧分销人员
export function delUser(id) {
  return request({
    url: '/agtaccount/user/' + id,
    method: 'delete'
  })
}
