import request from '@/utils/request'

// 查询小程序日数据列表
export function listGdtMiniappDayData(query) {
  return request({
    url: '/wxiaa/gdtMiniappDayData/list',
    method: 'get',
    params: query
  })
}

// 查询小程序日数据详细
export function getGdtMiniappDayData(id) {
  return request({
    url: '/wxiaa/gdtMiniappDayData/' + id,
    method: 'get'
  })
}

// 新增小程序日数据
export function addGdtMiniappDayData(data) {
  return request({
    url: '/wxiaa/gdtMiniappDayData/add',
    method: 'post',
    data: data
  })
}

// 修改小程序日数据
export function updateGdtMiniappDayData(data) {
  return request({
    url: '/wxiaa/gdtMiniappDayData/update',
    method: 'post',
    data: data
  })
}

// 删除小程序日数据
export function delGdtMiniappDayData(id) {
  return request({
    url: '/wxiaa/gdtMiniappDayData/' + id,
    method: 'delete'
  })
}
