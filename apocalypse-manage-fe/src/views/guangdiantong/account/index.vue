<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="组织id" prop="accountId">
        <el-input
          v-model="queryParams.accountId"
          placeholder="请输入组织id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主体名" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入主体名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['guangdiantong:account:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['guangdiantong:account:edit']"
          >修改</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['guangdiantong:account:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          icon="el-icon-document-copy"
          size="mini"
          v-clipboard:copy="
            'https://developers.e.qq.com/oauth/authorize?client_id=**********&redirect_uri=http://tq.nuohe.com.cn/api/guangdiantong/callback/auth%3FclientId%3D**********&state=&scope=&account_type=ACCOUNT_TYPE_WECHAT'
          "
          v-clipboard:success="clipboardSuccess"
          >复制授权链接</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="accountList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="组织id" align="center" prop="accountId" />
      <el-table-column label="主体名" align="center" prop="accountName" />
      <el-table-column label="返点" align="center" prop="rebate">
        <template slot-scope="scope">
          <span>{{ scope.row.rebate }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="应用appid" align="center" prop="appId" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="gmtCreate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtCreate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['guangdiantong:account:edit']"
            >修改</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改广点通主体对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="主体名" prop="accountName">
          <el-input v-model="form.accountName" placeholder="请输入主体名" />
        </el-form-item>
        <el-form-item label="返点" prop="rebate">
          <el-input v-model="form.rebate" placeholder="请输入返点" suffix="%">
            <template #suffix>
              <span>%</span>
            </template></el-input
          >
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAccount,
  getAccount,
  delAccount,
  addAccount,
  updateAccount,
} from "@/api/guangdiantong/account";

export default {
  name: "Account",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 广点通主体表格数据
      accountList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountId: null,
        accountName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountName: [
          { required: true, message: "主体名不能为空", trigger: "blur" },
        ],
        rebate: [
          { required: true, message: "返点不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^\d+(\.\d+)?$/.test(value)) {
                callback("返点只能是数字");
              }
              callback();
            },
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询广点通主体列表 */
    getList() {
      this.loading = true;
      listAccount(this.queryParams).then((response) => {
        this.accountList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountId: null,
        accountName: null,
        appId: null,
        appSecret: null,
        accessToken: null,
        refreshToken: null,
        refreshTokenExp: null,
        expiresIn: null,
        gmtCreate: null,
        gmtModified: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    clipboardSuccess() {
      this.$modal.msgSuccess("复制成功");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加广点通主体";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAccount(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改返点";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAccount(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAccount(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除广点通主体编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAccount(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "guangdiantong/account/export",
        {
          ...this.queryParams,
        },
        `account_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
