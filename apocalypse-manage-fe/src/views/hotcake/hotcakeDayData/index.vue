<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="日期范围" prop="dateRange">
        <DatePickerRange
          clearable
          v-model="queryParams.dateRange"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </DatePickerRange>
      </el-form-item>
      <el-form-item label="小程序" prop="appSearch">
        <el-input
          v-model="queryParams.appSearch"
          placeholder="小程序appId/名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="渠道号" prop="channelCode">-->
<!--        <el-input-->
<!--          v-model="queryParams.channelCode"-->
<!--          placeholder="请输入渠道号"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['hotcake:hotcakeDayData:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['hotcake:hotcakeDayData:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['hotcake:hotcakeDayData:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hotcake:hotcakeDayData:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="hotcakeDayDataList" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="日期" align="center" prop="curDate" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.curDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="小程序" align="center" prop="appName" min-width="100" />

      <el-table-column label="注册(PV/UV)" align="center" prop="registerPv" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.registerPv +' / '+ scope.row.registerUv}}</span>
        </template>
      </el-table-column>
      <el-table-column label="登录(PV/UV)" align="center" prop="loginPv" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.loginPv +' / '+ scope.row.loginUv}}</span>
        </template>
      </el-table-column>
      <el-table-column label="次日留存UV" align="center" prop="retentionDay1Uv" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.retentionDay1Uv }}</span>
        </template>
      </el-table-column>
      <el-table-column label="广告位(PV/UV)" align="center" width="120">
        <template slot-scope="scope">
          <span>曝光</span>
          <br/>
          <span>点击</span>
          <br/>
          <span>CTR</span>
        </template>
      </el-table-column>
      <el-table-column label="首屏广告" align="center" prop="splashExposurePv" width="110">
        <template slot-scope="scope">
          <span>{{ scope.row.splashExposurePv +' / '+ scope.row.splashExposureUv}}</span>
          <br/>
          <span>{{ scope.row.splashClickPv +' / '+ scope.row.splashClickUv}}</span>
          <br/>
          <span>{{ calculatePercentage(scope.row.splashClickPv, scope.row.splashExposurePv) +' / '+ calculatePercentage(scope.row.splashClickUv, scope.row.splashExposureUv)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="插屏广告" align="center" prop="interstitialExposurePv" width="110">
        <template slot-scope="scope">
          <span>{{ scope.row.interstitialExposurePv +' / '+ scope.row.interstitialExposureUv}}</span>
          <br/>
          <span>{{ scope.row.interstitialClickPv +' / '+ scope.row.interstitialClickUv}}</span>
          <br/>
          <span>{{ calculatePercentage(scope.row.interstitialClickPv, scope.row.interstitialExposurePv) +' / '+ calculatePercentage(scope.row.interstitialClickUv, scope.row.interstitialExposureUv)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="列表页广告" align="center" prop="feedPageExposurePv" width="125">
        <template slot-scope="scope">
          <span>{{ scope.row.feedPageExposurePv +' / '+ scope.row.feedPageExposureUv}}</span>
          <br/>
          <span>{{ scope.row.feedPageClickPv +' / '+ scope.row.feedPageClickUv}}</span>
          <br/>
          <span>{{ calculatePercentage(scope.row.feedPageClickPv, scope.row.feedPageExposurePv) +' / '+ calculatePercentage(scope.row.feedPageClickUv, scope.row.feedPageExposureUv)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="播放页广告" align="center" prop="playPageExposurePv" width="125">
        <template slot-scope="scope">
          <span>{{ scope.row.playPageExposurePv +' / '+ scope.row.playPageExposureUv}}</span>
          <br/>
          <span>{{ scope.row.playPageClickPv +' / '+ scope.row.playPageClickUv}}</span>
          <br/>
          <span>{{ calculatePercentage(scope.row.playPageClickPv, scope.row.playPageExposurePv) +' / '+ calculatePercentage(scope.row.playPageClickUv, scope.row.playPageExposureUv)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="视频解锁广告" align="center" prop="videoUnlockExposurePv" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.videoUnlockExposurePv +' / '+ scope.row.videoUnlockExposureUv}}</span>
          <br/>
          <span>{{ scope.row.videoUnlockClickPv +' / '+ scope.row.videoUnlockClickUv}}</span>
          <br/>
          <span>{{ calculatePercentage(scope.row.videoUnlockClickPv, scope.row.videoUnlockExposurePv) +' / '+ calculatePercentage(scope.row.videoUnlockClickUv, scope.row.videoUnlockExposureUv)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="左上角返回广告" align="center" prop="leftRetExposurePv" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.leftRetExposurePv +' / '+ scope.row.leftRetExposureUv}}</span>
          <br/>
          <span>{{ scope.row.leftRetClickPv +' / '+ scope.row.leftRetClickUv}}</span>
          <br/>
          <span>{{ calculatePercentage(scope.row.leftRetClickPv, scope.row.leftRetExposurePv) +' / '+ calculatePercentage(scope.row.leftRetClickUv, scope.row.leftRetExposureUv)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="物理返回广告" align="center" prop="physicalRetExposurePv" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.physicalRetExposurePv +' / '+ scope.row.physicalRetExposureUv}}</span>
          <br/>
          <span>{{ scope.row.physicalRetClickPv +' / '+ scope.row.physicalRetClickUv}}</span>
          <br/>
          <span>{{ calculatePercentage(scope.row.physicalRetClickPv, scope.row.physicalRetExposurePv) +' / '+ calculatePercentage(scope.row.physicalRetClickUv, scope.row.physicalRetExposureUv)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="关闭广告" align="center" prop="closeExposurePv" width="110">
        <template slot-scope="scope">
          <span>{{ scope.row.closeExposurePv +' / '+ scope.row.closeExposureUv}}</span>
          <br/>
          <span>{{ scope.row.closeClickPv +' / '+ scope.row.closeClickUv}}</span>
          <br/>
          <span>{{ calculatePercentage(scope.row.closeClickPv, scope.row.closeExposurePv) +' / '+ calculatePercentage(scope.row.closeClickUv, scope.row.closeExposureUv)}}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['hotcake:hotcakeDayData:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['hotcake:hotcakeDayData:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改爆款小程序日数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="日期" prop="curDate">
          <el-date-picker clearable
            v-model="form.curDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="小程序appId" prop="appId">
          <el-input v-model="form.appId" placeholder="请输入小程序appId" />
        </el-form-item>
        <el-form-item label="渠道号" prop="channelCode">
          <el-input v-model="form.channelCode" placeholder="请输入渠道号" />
        </el-form-item>
        <el-form-item label="注册PV" prop="registerPv">
          <el-input v-model="form.registerPv" placeholder="请输入注册PV" />
        </el-form-item>
        <el-form-item label="注册UV" prop="registerUv">
          <el-input v-model="form.registerUv" placeholder="请输入注册UV" />
        </el-form-item>
        <el-form-item label="登录PV" prop="loginPv">
          <el-input v-model="form.loginPv" placeholder="请输入登录PV" />
        </el-form-item>
        <el-form-item label="登录UV" prop="loginUv">
          <el-input v-model="form.loginUv" placeholder="请输入登录UV" />
        </el-form-item>
        <el-form-item label="首屏广告PV" prop="splashPv">
          <el-input v-model="form.splashPv" placeholder="请输入首屏广告PV" />
        </el-form-item>
        <el-form-item label="首屏广告PV" prop="splashUv">
          <el-input v-model="form.splashUv" placeholder="请输入首屏广告PV" />
        </el-form-item>
        <el-form-item label="插屏广告PV" prop="interstitialPv">
          <el-input v-model="form.interstitialPv" placeholder="请输入插屏广告PV" />
        </el-form-item>
        <el-form-item label="插屏广告PV" prop="interstitialUv">
          <el-input v-model="form.interstitialUv" placeholder="请输入插屏广告PV" />
        </el-form-item>
        <el-form-item label="列表页广告PV" prop="feedPagePv">
          <el-input v-model="form.feedPagePv" placeholder="请输入列表页广告PV" />
        </el-form-item>
        <el-form-item label="列表页广告PV" prop="feedPageUv">
          <el-input v-model="form.feedPageUv" placeholder="请输入列表页广告PV" />
        </el-form-item>
        <el-form-item label="播放页PV" prop="playPagePv">
          <el-input v-model="form.playPagePv" placeholder="请输入播放页PV" />
        </el-form-item>
        <el-form-item label="播放页PV" prop="playPageUv">
          <el-input v-model="form.playPageUv" placeholder="请输入播放页PV" />
        </el-form-item>
        <el-form-item label="视频解锁广告PV" prop="videoUnlockPv">
          <el-input v-model="form.videoUnlockPv" placeholder="请输入视频解锁广告PV" />
        </el-form-item>
        <el-form-item label="视频解锁广告PV" prop="videoUnlockUv">
          <el-input v-model="form.videoUnlockUv" placeholder="请输入视频解锁广告PV" />
        </el-form-item>
        <el-form-item label="左上角返回广告PV" prop="leftRetPv">
          <el-input v-model="form.leftRetPv" placeholder="请输入左上角返回广告PV" />
        </el-form-item>
        <el-form-item label="左上角返回广告PV" prop="leftRetUv">
          <el-input v-model="form.leftRetUv" placeholder="请输入左上角返回广告PV" />
        </el-form-item>
        <el-form-item label="物理返回广告PV" prop="physicalRetPv">
          <el-input v-model="form.physicalRetPv" placeholder="请输入物理返回广告PV" />
        </el-form-item>
        <el-form-item label="物理返回广告PV" prop="physicalRetUv">
          <el-input v-model="form.physicalRetUv" placeholder="请输入物理返回广告PV" />
        </el-form-item>
        <el-form-item label="关闭广告PV" prop="closePv">
          <el-input v-model="form.closePv" placeholder="请输入关闭广告PV" />
        </el-form-item>
        <el-form-item label="关闭广告PV" prop="closeUv">
          <el-input v-model="form.closeUv" placeholder="请输入关闭广告PV" />
        </el-form-item>
        <el-form-item label="创建时间" prop="gmtCreate">
          <el-date-picker clearable
            v-model="form.gmtCreate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="修改时间" prop="gmtModified">
          <el-date-picker clearable
            v-model="form.gmtModified"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择修改时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHotcakeDayData, getHotcakeDayData, delHotcakeDayData, addHotcakeDayData, updateHotcakeDayData } from "@/api/hotcake/hotcakeDayData";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "HotcakeDayData",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 爆款小程序日数据表格数据
      hotcakeDayDataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 修改时间时间范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [
          parseTime(Date.now() - 7 * 24 * 60 * 60 * 1000, "{y}-{m}-{d}"),
          parseTime(Date.now(), "{y}-{m}-{d}"),
        ],
        curDate: null,
        appId: null,
        appSearch: null,
        channelCode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        curDate: [
          { required: true, message: "日期不能为空", trigger: "blur" }
        ],
        appId: [
          { required: true, message: "小程序appId不能为空", trigger: "blur" }
        ],
        channelCode: [
          { required: true, message: "渠道号不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询爆款小程序日数据列表 */
    getList() {
      const { dateRange, ...params } = this.queryParams;
      this.loading = true;
      listHotcakeDayData({
        ...params,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
      }).then(response => {
        this.hotcakeDayDataList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        curDate: null,
        appId: null,
        appSearch: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加爆款小程序日数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getHotcakeDayData(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改爆款小程序日数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateHotcakeDayData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHotcakeDayData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除爆款小程序日数据编号为"' + ids + '"的数据项？').then(function() {
        return delHotcakeDayData(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const { dateRange, ...params } = this.queryParams;
      this.download('hotcake/hotcakeDayData/export', {
        ...params,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
      }, `hotcakeDayData_${new Date().getTime()}.xlsx`)
    },
    calculatePercentage(numerator, denominator) {
      if (denominator === 0 || isNaN(numerator) || isNaN(denominator)) {
        return '-';
      }
      return (numerator / denominator * 100).toFixed(0) + '%';
    }
  }
};
</script>
