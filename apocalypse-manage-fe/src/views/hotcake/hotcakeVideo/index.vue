<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="视频标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入视频标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="queryParams.tags"
          filterable
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in tagOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作者昵称" prop="authorNickname">
        <el-input
          v-model="queryParams.authorNickname"
          placeholder="请输入作者昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="创建时间" prop="gmtCreate">-->
<!--        <el-date-picker-->
<!--          clearable-->
<!--          v-model="queryParams.gmtCreate"-->
<!--          type="date"-->
<!--          value-format="yyyy-MM-dd"-->
<!--          placeholder="请选择创建时间"-->
<!--        >-->
<!--        </el-date-picker>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="修改时间" prop="gmtModified">-->
<!--        <el-date-picker-->
<!--          clearable-->
<!--          v-model="queryParams.gmtModified"-->
<!--          type="date"-->
<!--          value-format="yyyy-MM-dd"-->
<!--          placeholder="请选择修改时间"-->
<!--        >-->
<!--        </el-date-picker>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['hotcake:hotcakeVideo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['hotcake:hotcakeVideo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['hotcake:hotcakeVideo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hotcake:hotcakeVideo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="hotcakeVideoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键id" align="center" prop="id" />
      <el-table-column label="视频标题" align="center" prop="title" />
      <el-table-column label="视频封面图" width="200" align="center" prop="coverImg" >
        <template slot-scope="scope">
          <el-popover placement="top-start" trigger="hover">
            <div slot="reference" style="width: 200px; height: auto">
              <el-image
                v-if="scope.row.coverImg"
                style="width: 200px; height: auto"
                :src="scope.row.coverImg"
                fit="contain"
              />

            </div>
            <el-image
              v-if="scope.row.coverImg"
              style="width: auto; height: 365px"
              :src="scope.row.coverImg"
              fit="cover"
            />
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="视频链接" align="center" prop="videoUrl" />
      <el-table-column label="标签" align="center" prop="tags" />
      <el-table-column label="权重" align="center" prop="weight" />
      <el-table-column label="作者头像" align="center" prop="authorAvatar" >
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.authorAvatar "
            style="width: 80px; height: 80px"
            :src="scope.row.authorAvatar"
            fit="contain"
          />
        </template>
      </el-table-column>
      <el-table-column label="作者昵称" align="center" prop="authorNickname" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="gmtCreate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtCreate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="修改时间"
        align="center"
        prop="gmtModified"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtModified, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['hotcake:hotcakeVideo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['hotcake:hotcakeVideo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改视频内容管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="视频标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入视频标题"
            @change="handleVideoTitleChange"
          />
        </el-form-item>
        <el-form-item label="视频封面图" prop="coverImg">
          <image-upload v-model="form.coverImg" oss :limit="1" />
        </el-form-item>
        <el-form-item label="视频链接" prop="videoUrl">
          <video-upload
            v-model="form.videoUrl"
            oss
            :limit="1"
            @upload-success="handleVideoUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input
            v-model="form.tags"
            placeholder="请输入标签:常规、男频、女频、猎奇、下线"
          />
        </el-form-item>
        <el-form-item label="作者头像" prop="authorAvatar">
          <image-upload v-model="form.authorAvatar" oss :limit="1" />
        </el-form-item>
        <el-form-item label="作者昵称" prop="authorNickname">
          <el-input
            v-model="form.authorNickname"
            placeholder="请输入作者昵称"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHotcakeVideo,
  getHotcakeVideo,
  delHotcakeVideo,
  addHotcakeVideo,
  updateHotcakeVideo,
} from "@/api/hotcake/hotcakeVideo";

export default {
  name: "HotcakeVideo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 视频内容管理表格数据
      hotcakeVideoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        coverImg: null,
        videoUrl: null,
        tags: null,
        weight: null,
        authorAvatar: null,
        authorNickname: null,
        gmtCreate: null,
        gmtModified: null,
      },
      tagOptions: [
        {
          value: "常规",
          label: "常规",
        },
        {
          value: "男频",
          label: "男频",
        },
        {
          value: "女频",
          label: "女频",
        },
        {
          value: "猎奇",
          label: "猎奇",
        },
        {
          value: "下线",
          label: "下线",
        },
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "视频标题不能为空", trigger: "blur" },
        ],
        coverImg: [
          { required: true, message: "视频封面图不能为空", trigger: "blur" },
        ],
        videoUrl: [
          { required: true, message: "视频链接不能为空", trigger: "blur" },
        ],
        tags: [
          {
            required: true,
            message: "标签不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 视频标题修改 */
    handleVideoTitleChange(value) {
      this.videoTitleChangeByManual = !!value;
    },
    /** 视频上传成功 */
    handleVideoUploadSuccess(fileList) {
      const [file] = fileList;
      // 未主动修改标题时，通过视频名称填充
      if (file && file.name && !this.videoTitleChangeByManual) {
        this.form.title = file.name.replace(/(.*)\.[^.]+$/, "$1");
      }
    },
    /** 查询视频内容管理列表 */
    getList() {
      this.loading = true;
      listHotcakeVideo(this.queryParams).then((response) => {
        this.hotcakeVideoList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        coverImg: null,
        videoUrl: null,
        tags: null,
        weight: null,
        authorAvatar: null,
        authorNickname: null,
        gmtCreate: null,
        gmtModified: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加视频内容管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getHotcakeVideo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改视频内容管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const { tags, ...data } = this.form;
          const formData = {
            ...data,
            tags: Array.isArray(tags) ? tags : tags.split(/,|，|、/),
          };
          if (this.form.id != null) {
            updateHotcakeVideo(formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHotcakeVideo(formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除视频内容管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delHotcakeVideo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "hotcake/hotcakeVideo/export",
        {
          ...this.queryParams,
        },
        `hotcakeVideo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
