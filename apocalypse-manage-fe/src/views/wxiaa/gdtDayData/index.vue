<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="85px">
      <el-form-item label="日期" prop="dateRange">
        <DatePickerRange clearable v-model="queryParams.dateRange" type="daterange" value-format="yyyy-MM-dd"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleQuery">
        </DatePickerRange>
      </el-form-item>

      <el-form-item label="广告账户Id" prop="advertisementId">
        <el-input v-model="queryParams.advertisementId" placeholder="请输入广告账户Id" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="组织" prop="accountId">
        <el-select v-model="queryParams.accountIds" filterable multiple clearable>
          <el-option v-for="item in organization" :key="item.accountId" :label="item.accountName"
            :value="item.accountId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="投手" prop="operators">
        <el-select v-model="queryParams.operators" filterable multiple clearable>
          <el-option v-for="item in operatorList" :key="item.id" :label="item.userRealname"
            :value="item.userNickname">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="小程序" prop="miniappNames">
        <el-select v-model="queryParams.miniappNames" filterable multiple clearable>
          <el-option v-for="item in miniappList" :key="item.id" :label="item.appName"
            :value="item.appName">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="短剧名" prop="videoName">
        <el-input v-model="queryParams.videoName" placeholder="请输入短剧名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" class="mt4">
        <DropdownCheckboxGroup v-model="queryParams.dimension" :options="dimensionOptions"
          @change="handleDimensionChange" :default-value="dimensionDefaultValue">
          <el-link type="primary" :underline="false">维度选择<i class="el-icon-arrow-down el-icon--right"></i></el-link>
        </DropdownCheckboxGroup>
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"-->
      <!--          v-hasPermi="['wxiaa:gdtDayData:add']">新增</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"-->
      <!--          v-hasPermi="['wxiaa:gdtDayData:edit']">修改</el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['wxiaa:gdtDayData:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['wxiaa:gdtDayData:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-alert class="size-small" :title="`今日数据，上次更新时间：${lastSyncTime || '--'}（1小时更新一次）`" type="warning" show-icon
          :closable="false">
        </el-alert></el-col>
      <right-toolbar :name="columnsStorageName" :showSearch.sync="showSearch" @queryTable="getList"
        :columns.sync="columns" :default-selected-column-keys="Object.keys(columns)"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="gdtDayDataList"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      :key="tableKey"
    >
      <el-table-column
        label="日期"
        align="center"
        prop="curDate"
        width="100"
        fixed
        v-if="queryParams.dimension.includes('cur_date')"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.curDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="投手"
        align="center"
        prop="operator"
        fixed
        v-if="queryParams.dimension.includes('operator')"
      />
      <el-table-column
        label="小程序"
        align="center"
        prop="miniappName"
        fixed
        width="110"
        v-if="queryParams.dimension.includes('miniapp_name')"
      />
      <el-table-column
        label="账户Id"
        align="center"
        prop="advertisementId"
        width="90"
        fixed
        v-if="queryParams.dimension.includes('advertisement_id')"
      />
      <el-table-column
        label="剧名"
        align="center"
        prop="videoName"
        width="130"
        fixed
        v-if="queryParams.dimension.includes('video_name')"
      />
      <el-table-column
        label="组织"
        align="center"
        prop="accountName"
        width="190"
        v-if="queryParams.dimension.includes('account_id')"
      />
      <el-table-column
        label="账面消耗"
        align="center"
        prop="cost"
        sortable="custom"
        width="100"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.cost.visible"
      />
      <el-table-column
        label="现金消耗"
        align="center"
        prop="cashCost"
        width="120"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.cashCost.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">现金消耗</span>
          <el-tooltip class="item" effect="dark" content="预估现金消耗=账面消耗/(1+返点)" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="广告变现金额"
        align="center"
        prop="adMonetizationAmount"
        width="130"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.adMonetizationAmount.visible"
      />
      <el-table-column
        label="曝光"
        align="center"
        prop="viewCount"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.viewCount.visible"
      />
      <el-table-column
        label="点击"
        align="center"
        prop="validClickCount"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.validClickCount.visible"
      />
      <el-table-column
        label="点击率"
        align="center"
        prop="ctr"
        v-if="this.columns.ctr.visible"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ctr }}%</span>
        </template>
      </el-table-column>
      <el-table-column
        label="点击均价"
        align="center"
        prop="cpc"
        sortable="custom"
        width="100"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.cpc.visible"
      />
      <el-table-column
        label="广告变现arpu"
        align="center"
        prop="adMonetizationArppu"
        width="120"
        v-if="this.columns.adMonetizationArppu.visible"
      />
      <el-table-column
        label="千展均价"
        align="center"
        prop="thousandDisplayPrice"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="100"
        v-if="this.columns.thousandDisplayPrice.visible"
      />
      <el-table-column
        label="目标转化率"
        align="center"
        prop="conversionsRate"
        width="90"
        v-if="this.columns.conversionsRate.visible"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.conversionsRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column
        label="广告变现ROI"
        align="center"
        prop="adMonetizationRoi"
        width="130"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.adMonetizationRoi.visible"
      />
      <el-table-column
        label="激活首日广告变现ROI"
        align="center"
        prop="incomeRoi1"
        width="180"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.incomeRoi1.visible"
      />

      <el-table-column
        label="实际广告变现金额（元）"
        align="center"
        prop="realAdMonetizationAmount"
        width="210"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.realAdMonetizationAmount.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">实际广告变现金额(元)</span>
          <el-tooltip class="item" effect="dark" content="实际广告变现金额(元)=账面广告变现金额/0.7*现金分成" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="预估广告金（元）"
        align="center"
        prop="adMoney"
        width="170"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.adMoney.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">预估广告金(元)</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="预估广告金=实际广告变现金额/小程序现金分成*小程序广告金比例"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="投流利润（未加返点）"
        align="center"
        prop="feedAdProfit"
        width="200"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.feedAdProfit.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">投流利润(未加返点)</span>
          <el-tooltip class="item" effect="dark" content="投流利润(未加返点)=实际广告变现金额+预估广告金-账面消耗" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="投流利润（加返点）"
        align="center"
        prop="feedAdProfitB"
        width="180"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.feedAdProfitB.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">投流利润(加返点)</span>
          <el-tooltip class="item" effect="dark" content="投流利润(加返点)=实际广告变现金额+预估广告金-现金消耗" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-edit"-->
      <!--            @click="handleUpdate(scope.row)"-->
      <!--            v-hasPermi="['wxiaa:gdtDayData:edit']"-->
      <!--          >修改</el-button>-->
      <!--          <el-button-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-delete"-->
      <!--            @click="handleDelete(scope.row)"-->
      <!--            v-hasPermi="['wxiaa:gdtDayData:remove']"-->
      <!--          >删除</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改广点通iaa日数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="当前日期" prop="curDate">
          <el-date-picker clearable v-model="form.curDate" type="date" value-format="yyyy-MM-dd" placeholder="请选择当前日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="广告账户Id" prop="advertisementId">
          <el-input v-model="form.advertisementId" placeholder="请输入广告账户Id" />
        </el-form-item>
        <el-form-item label="广告id" prop="adgroupId">
          <el-input v-model="form.adgroupId" placeholder="请输入广告id" />
        </el-form-item>
        <el-form-item label="广告名" prop="adgroupName">
          <el-input v-model="form.adgroupName" placeholder="请输入广告名" />
        </el-form-item>
        <el-form-item label="投手" prop="operator">
          <el-input v-model="form.operator" placeholder="请输入投手" />
        </el-form-item>
        <el-form-item label="小程序应用名称" prop="miniappName">
          <el-input v-model="form.miniappName" placeholder="请输入小程序应用名称" />
        </el-form-item>
        <el-form-item label="短剧名" prop="videoName">
          <el-input v-model="form.videoName" placeholder="请输入短剧名" />
        </el-form-item>
        <el-form-item label="账面消耗" prop="cost">
          <el-input v-model="form.cost" placeholder="请输入账面消耗(分)" />
        </el-form-item>
        <el-form-item label="现金消耗" prop="cashCost">
          <el-input v-model="form.cashCost" placeholder="请输入现金消耗" />
        </el-form-item>
        <el-form-item label="广告变现金额" prop="adMonetizationAmount">
          <el-input v-model="form.adMonetizationAmount" placeholder="请输入广告变现金额" />
        </el-form-item>
        <el-form-item label="曝光pv" prop="viewCount">
          <el-input v-model="form.viewCount" placeholder="请输入曝光pv" />
        </el-form-item>
        <el-form-item label="点击pv" prop="validClickCount">
          <el-input v-model="form.validClickCount" placeholder="请输入点击pv" />
        </el-form-item>
        <el-form-item label="目标转化量" prop="conversionsCount">
          <el-input v-model="form.conversionsCount" placeholder="请输入目标转化量" />
        </el-form-item>
        <el-form-item label="广告变现人数" prop="appAdPayingUsers">
          <el-input v-model="form.appAdPayingUsers" placeholder="请输入广告变现人数" />
        </el-form-item>
        <el-form-item label="激活首日广告变现金额" prop="incomeVal1">
          <el-input v-model="form.incomeVal1" placeholder="请输入激活首日广告变现金额" />
        </el-form-item>
        <el-form-item label="创建时间" prop="gmtCreate">
          <el-date-picker clearable v-model="form.gmtCreate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="修改时间" prop="gmtModified">
          <el-date-picker clearable v-model="form.gmtModified" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择修改时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listGdtDayData,
  getGdtDayData,
  delGdtDayData,
  addGdtDayData,
  updateGdtDayData,
  fetchLastSyncTime,
} from "@/api/wxiaa/gdtDayData";
import { parseTime } from "@/utils/ruoyi";
import storage from "@/utils/storage";
import { createUniqueString } from "@/utils";
import { listAccount } from "@/api/guangdiantong/account";
import { listGdtMiniapp } from "@/api/wxiaa/gdtMiniapp";
import { listUser } from "@/api/agtaccount/user";

// 维度选择默认值
const dimensionDefaultValue = [
  "cur_date",
  "operator",
  "miniapp_name",
  "advertisement_id",
  "video_name",
  "account_id",
];

// 维度选择本地存储名称
const DIMENSION_STORAGE_NAME = "wxiaa-data-list-dimension";

// 显隐列本地存储名称，最后数字为版本号，为避免修改对应数据后线上不生效，需要更新版本号
const COLUMNS_STORAGE_NAME = "wxiaa-data-list-columns_1";

export default {
  name: "GdtDayData",
  data() {
    return {
      // 上次数据更新时间
      lastSyncTime: null,
      // 表格key
      tableKey: createUniqueString(),
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 广点通iaa日数据表格数据
      gdtDayDataList: [],
      organization: [], //组织列表
      miniappList: [], //小程序列表
      operatorList: [], //投手列表
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 维度选择
      dimensionOptions: [
        { label: "日期", value: "cur_date" },
        { label: "投手", value: "operator" },
        { label: "小程序", value: "miniapp_name" },
        { label: "账户Id", value: "advertisement_id" },
        { label: "剧名", value: "video_name" },
        { label: "组织", value: "account_id" },
      ],
      // 维度默认数据
      dimensionDefaultValue,
      // 显隐列本地存储
      columnsStorageName: COLUMNS_STORAGE_NAME,
      // 表格列显隐控制
      columns: Object.assign(
        {
          cost: {
            label: "账面消耗",
            key: "cost",
            visible: true,
          },
          cashCost: {
            label: "现金消耗",
            key: "cashCost",
            visible: true,
          },
          adMonetizationAmount: {
            label: "广告变现金额",
            key: "adMonetizationAmount",
            visible: true,
          },
          viewCount: {
            label: "曝光",
            key: "viewCount",
            visible: true,
          },
          validClickCount: {
            label: "点击",
            key: "validClickCount",
            visible: true,
          },
          ctr: {
            label: "点击率",
            key: "ctr",
            visible: true,
          },
          cpc: {
            label: "点击均价",
            key: "cpc",
            visible: true,
          },
          adMonetizationArppu: {
            label: "广告变现arpu",
            key: "adMonetizationArppu",
            visible: true,
          },
          thousandDisplayPrice: {
            label: "千展均价",
            key: "thousandDisplayPrice",
            visible: true,
          },
          conversionsRate: {
            label: "目标转化率",
            key: "conversionsRate",
            visible: true,
          },
          adMonetizationRoi: {
            label: "广告变现ROI",
            key: "adMonetizationRoi",
            visible: true,
          },
          incomeRoi1: {
            label: "激活首日广告变现ROI",
            key: "incomeRoi1",
            visible: true,
          },

          realAdMonetizationAmount: {
            label: "实际广告变现金额（元）",
            key: "realAdMonetizationAmount",
            visible: true,
          },
          adMoney: {
            label: "预估广告金（元）",
            key: "adMoney",
            visible: true,
          },
          feedAdProfit: {
            label: "投流利润（未加返点）",
            key: "feedAdProfit",
            visible: true,
          },
          feedAdProfitB: {
            label: "投流利润（加返点）",
            key: "feedAdProfitB",
            visible: true,
          },
        },
        storage.local.getByUser(COLUMNS_STORAGE_NAME)
      ),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [
          parseTime(Date.now(), "{y}-{m}-{d}"),
          parseTime(Date.now(), "{y}-{m}-{d}"),
        ],
        curDate: null,
        advertisementId: null,
        accountId: null,
        operators: [],
        miniappNames: [],
        videoName: null,
        accountIds: [],
        dimension: storage.local.getByUser(DIMENSION_STORAGE_NAME) || [
          ...dimensionDefaultValue,
        ],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        curDate: [
          { required: true, message: "当前日期不能为空", trigger: "blur" },
        ],
        advertisementId: [
          { required: true, message: "广告账户id不能为空", trigger: "blur" },
        ],
        adgroupId: [
          { required: true, message: "广告id不能为空", trigger: "blur" },
        ],
        adgroupName: [
          { required: true, message: "广告名不能为空", trigger: "blur" },
        ],
        operator: [
          { required: true, message: "投手不能为空", trigger: "blur" },
        ],
        miniappName: [
          {
            required: true,
            message: "小程序应用名称不能为空",
            trigger: "blur",
          },
        ],
        videoName: [
          { required: true, message: "短剧名不能为空", trigger: "blur" },
        ],
        cost: [
          { required: true, message: "账面消耗(分)不能为空", trigger: "blur" },
        ],
        cashCost: [
          { required: true, message: "现金消耗不能为空", trigger: "blur" },
        ],
        adMonetizationAmount: [
          { required: true, message: "广告变现金额不能为空", trigger: "blur" },
        ],
        viewCount: [
          { required: true, message: "曝光pv不能为空", trigger: "blur" },
        ],
        validClickCount: [
          { required: true, message: "点击pv不能为空", trigger: "blur" },
        ],
        conversionsCount: [
          { required: true, message: "目标转化量不能为空", trigger: "blur" },
        ],
        appAdPayingUsers: [
          { required: true, message: "广告变现人数不能为空", trigger: "blur" },
        ],
        incomeVal1: [
          {
            required: true,
            message: "激活首日广告变现金额不能为空",
            trigger: "blur",
          },
        ],
        gmtCreate: [
          { required: true, message: "创建时间不能为空", trigger: "blur" },
        ],
        gmtModified: [
          { required: true, message: "修改时间不能为空", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    columns: {
      handler() {
        this.tableKey = createUniqueString();
      },
      deep: true,
    },
  },
  created() {
    this.getList();
    this.getOrganization();
    this.getMiniapp();
    this.getOperator();
    this.handleFetchLastSyncTime();
  },
  methods: {
    /** 维度变更 */
    handleDimensionChange(value) {
      storage.local.setByUser(DIMENSION_STORAGE_NAME, value);
      this.handleQuery();
      this.tableKey = createUniqueString();
    },
    /** 排序变更 */
    handleSortChange(column) {
      this.queryParams.orderColumn = column.order ? column.prop : null;
      this.queryParams.isAsc = column.order
        ? Boolean(column.order === "ascending")
        : null;
      this.getList();
    },
    /** 获取组织列表 */
    async getOrganization() {
      const { list } = await listAccount({
        pageSize: 10e6,
        pageNum: 1,
      });
      this.organization = list;
    },
    /** 获取小程序列表 */
    async getMiniapp() {
      const { list } = await listGdtMiniapp({
        pageSize: 10e6,
        pageNum: 1,
      });
      this.miniappList = list;
    },
    /** 获取投手列表 */
    async getOperator() {
      const { list } = await listUser({
        pageSize: 10e6,
        pageNum: 1,
        post: 2
      });
      this.operatorList = list;
    },
    /** 查询广点通iaa日数据列表 */
    getList() {
      const { dateRange, ...params } = this.queryParams;
      this.loading = true;
      listGdtDayData({
        ...params,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
      }).then((response) => {
        this.gdtDayDataList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        curDate: null,
        advertisementId: null,
        accountId: null,
        adgroupId: null,
        adgroupName: null,
        operator: null,
        miniappName: null,
        videoName: null,
        cost: null,
        cashCost: null,
        adMonetizationAmount: null,
        viewCount: null,
        validClickCount: null,
        conversionsCount: null,
        appAdPayingUsers: null,
        incomeVal1: null,
        gmtCreate: null,
        gmtModified: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加广点通iaa日数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getGdtDayData(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改广点通iaa日数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateGdtDayData(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGdtDayData(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除广点通iaa日数据编号为"' + ids + '"的数据项？')
        .then(function () {
          return delGdtDayData(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      const { dateRange, ...params } = this.queryParams;
      this.download(
        "wxiaa/gdtDayData/export",
        {
          ...params,
          startDate: dateRange?.[0],
          endDate: dateRange?.[1],
        },
        `广点通iaa数据_${new Date().getTime()}.xlsx`
      );
    },
    async handleFetchLastSyncTime() {
      const { data } = await fetchLastSyncTime();
      this.lastSyncTime = data;
    },
  },
};
</script>
