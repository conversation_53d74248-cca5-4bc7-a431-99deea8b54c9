<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['wxiaa:gdtAdTransfer:add']"
          type="primary"
          plain
          size="small"
          class="action-button"
          @click="handleAdd"
        >广告金转出</el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="gdtAdTransferList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="小程序名称" align="center" prop="appName" />
      <el-table-column
        label="转出账户"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.transferOutAccountId }} {{ scope.row.transferOutAccountName }}
        </template>
      </el-table-column>
      <el-table-column label="金额（元）" align="center">
        <template slot-scope="scope">
          {{ scope.row.transferAmount / 100 }}
        </template>
      </el-table-column>
      <el-table-column label="转出状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.transferStatus === 1 ? '成功' : '失败' }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作时间"
        align="center"
        prop="gmtCreate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtCreate, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 广告金转出对话框 -->
    <el-dialog
      title="广告金转出"
      :visible.sync="open"
      width="600px"
      append-to-body
      custom-class="transfer-dialog"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="微信小程序:" prop="appName">
          <el-select
            v-model="form.appName"
            placeholder="请选择小程序"
            style="width: 100%"
            @change="handleAppChange"
          >
            <el-option
              v-for="item in appOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="转出金额:" prop="transferAmount">
          <el-input v-model="form.transferAmount" placeholder="请输入转出金额">
            <template slot="append">元</template>
          </el-input>
          <div class="balance-info">当前可用余额: {{ availableBalance }}元</div>
        </el-form-item>

        <el-form-item label="转出账户:" prop="transferOutAccountId">
          <el-select
            v-model="form.transferOutAccountId"
            placeholder="输入账号名称或ID搜索"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in accountOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listGdtAdTransfer,
  getGdtAdTransfer,
  delGdtAdTransfer,
  addGdtAdTransfer,
  updateGdtAdTransfer,
  getBindList,
  getBalance,
} from "@/api/wxiaa/gdtAdTransfer";
import { listGdtMiniapp } from "@/api/wxiaa/gdtMiniapp";

export default {
  name: "GdtAdTransfer",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 广告金转出记录表格数据
      gdtAdTransferList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        appName: null,
        transferOutAccountId: null,
        transferOutAccountName: null,
        transferAmount: null,
        transferStatus: null,
        gmtCreate: null,
        gmtModified: null,
      },
      // 表单参数
      form: {
        id: null,
        appId: null,
        appName: null,
        transferOutAccountId: null,
        transferOutAccountName: null,
        transferAmount: null,
        transferStatus: null,
        gmtCreate: null,
        gmtModified: null,
      },
      // 表单校验
      rules: {
        appName: [
          { required: true, message: "请选择小程序", trigger: "change" },
        ],
        transferAmount: [
          { required: true, message: "请输入转出金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value && (isNaN(value) || value <= 0)) {
                callback(new Error("请输入有效的转出金额"));
              } else if (value > this.availableBalance) {
                callback(new Error("转出金额不能大于可用余额"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        transferOutAccountId: [
          { required: true, message: "请选择转出账户", trigger: "change" },
        ],
      },
      // 可用余额
      availableBalance: 0,
      // 小程序选项列表
      appOptions: [],
      // 账户选项列表
      accountOptions: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询广告金转出记录列表 */
    getList() {
      this.loading = true;
      listGdtAdTransfer(this.queryParams).then((response) => {
        this.gdtAdTransferList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        appId: null,
        appName: null,
        transferOutAccountId: null,
        transferOutAccountName: null,
        transferAmount: null,
        transferStatus: null,
        gmtCreate: null,
        gmtModified: null,
      };
      this.resetForm("form");
      this.accountOptions = [];
      this.availableBalance = 0;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    async handleAdd() {
      this.reset();
      await this.loadAppOptions();
      this.open = true;
      this.title = "广告金转出";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getGdtAdTransfer(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改广告金转出记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const selectedAccount = this.accountOptions.find(
            (opt) => opt.value === this.form.transferOutAccountId
          );

          // Find selected app to get app name
          const selectedApp = this.appOptions.find(
            (opt) => opt.value === this.form.appId
          );

          const formData = {
            ...this.form,
            appName: selectedApp ? selectedApp.label : '',  // Use app label as name
            transferOutAccountName: selectedAccount ? selectedAccount.accountName : '',
            transferAmount: Number(this.form.transferAmount) * 100,
          };

          if (this.form.id != null) {
            updateGdtAdTransfer(formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGdtAdTransfer(formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除广告金转出记录编号为"' + ids + '"的数据项？')
        .then(function () {
          return delGdtAdTransfer(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "wxiaa/gdtAdTransfer/export",
        {
          ...this.queryParams,
        },
        `gdtAdTransfer_${new Date().getTime()}.xlsx`
      );
    },
    /** 加载小程序列表 */
    async loadAppOptions() {
      try {
        // 设置查询参数，设置pageSize为100，防止分页查询
        this.queryParams.pageSize = 100;
        const response = await listGdtMiniapp(this.queryParams);
        this.appOptions = (response.list || []).map((item) => ({
          label: item.appName,
          value: item.appId,
        }));
      } catch (error) {
        console.error("Failed to load app options:", error);
        this.$message.error("加载小程序列表失败");
      }
    },
    /** 加载可转出账户列表 */
    async loadAccountOptions(appId) {
      if (!appId) return;
      try {
        const response = await getBindList(appId);
        if (response.code === 200) {
          this.accountOptions = (response.data || []).map((item) => ({
            label: item.name + ' ' + item.id,
            value: item.id
          }));
        } else {
          this.$message.error(response.msg || "加载账户列表失败");
        }
      } catch (error) {
        console.error("Failed to load account options:", error);
        this.$message.error("加载账户列表失败");
      }
    },
    /** 加载可用余额 */
    async loadBalance(appId) {
      if (!appId) return;
      try {
        const response = await getBalance(appId);
        this.availableBalance = response.data / 100; // 假设后端返回的金额单位是分
      } catch (error) {
        console.error("Failed to load balance:", error);
        this.$message.error("加载余额失败");
      }
    },
    /** 监听小程序选择变化 */
    handleAppChange(appId) {
      if (!appId) {
        this.form.appId = null;
        this.form.transferOutAccountId = null;
        this.accountOptions = [];
        this.availableBalance = 0;
        return;
      }

      // Only proceed if appId has changed
      if (this.form.appId === appId) {
        return;
      }

      this.form.appId = appId;
      this.form.transferOutAccountId = null;
      this.accountOptions = [];
      this.availableBalance = 0;

      // Load account options and balance in parallel
      Promise.all([
        this.loadAccountOptions(appId),
        this.loadBalance(appId)
      ]).catch(error => {
        console.error('Failed to load account data:', error);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.mb8 {
  margin-bottom: 18px;
}

.balance-info {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
