<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['turntable:shortplayTurntablePrize:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['turntable:shortplayTurntablePrize:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['turntable:shortplayTurntablePrize:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['turntable:shortplayTurntablePrize:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="shortplayTurntablePrizeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="奖品名" align="center" prop="name" />
      <el-table-column label="奖品类型,1集数解锁 2全集解锁 3立减金" align="center" prop="type" />
      <el-table-column label="奖品数量" align="center" prop="num" />
      <el-table-column label="概率" align="center" prop="probability" />
      <el-table-column label="奖品图" align="center" prop="img" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.img" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="小程序appid" align="center" prop="appId" />
      <el-table-column label="跳转路径" align="center" prop="path" />
      <el-table-column label="删除状态0未删除1删除" align="center" prop="isDeleted" />
      <el-table-column label="创建时间" align="center" prop="gmtCreate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtCreate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['turntable:shortplayTurntablePrize:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['turntable:shortplayTurntablePrize:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改短剧转盘奖品对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="奖品名" prop="name">
          <el-input v-model="form.name" placeholder="请输入奖品名" />
        </el-form-item>
        <el-form-item label="奖品类型,1集数解锁 2全集解锁 3立减金" prop="type">
          <el-input v-model="form.type" placeholder="请输入奖品类型,1集数解锁 2全集解锁 3立减金" />
        </el-form-item>
        <el-form-item label="奖品数量" prop="num">
          <el-input v-model="form.num" placeholder="请输入奖品数量" />
        </el-form-item>
        <el-form-item label="概率" prop="probability">
          <el-input v-model="form.probability" placeholder="请输入概率" />
        </el-form-item>
        <el-form-item label="奖品图" prop="img">
          <image-upload v-model="form.img"/>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="小程序appid" prop="appId">
          <el-input v-model="form.appId" placeholder="请输入小程序appid" />
        </el-form-item>
        <el-form-item label="跳转路径" prop="path">
          <el-input v-model="form.path" placeholder="请输入跳转路径" />
        </el-form-item>
        <el-form-item label="删除状态0未删除1删除" prop="isDeleted">
          <el-input v-model="form.isDeleted" placeholder="请输入删除状态0未删除1删除" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listShortplayTurntablePrize, getShortplayTurntablePrize, delShortplayTurntablePrize, addShortplayTurntablePrize, updateShortplayTurntablePrize } from "@/api/turntable/shortplayTurntablePrize";

export default {
  name: "ShortplayTurntablePrize",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短剧转盘奖品表格数据
      shortplayTurntablePrizeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "奖品名不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "奖品类型,1集数解锁 2全集解锁 3立减金不能为空", trigger: "blur" }
        ],
        num: [
          { required: true, message: "奖品数量不能为空", trigger: "blur" }
        ],
        probability: [
          { required: true, message: "概率不能为空", trigger: "blur" }
        ],
        img: [
          { required: true, message: "奖品图不能为空", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "排序不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询短剧转盘奖品列表 */
    getList() {
      this.loading = true;
      listShortplayTurntablePrize(this.queryParams).then(response => {
        this.shortplayTurntablePrizeList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        type: null,
        num: null,
        probability: null,
        img: null,
        sort: null,
        appId: null,
        path: null,
        isDeleted: null,
        gmtCreate: null,
        gmtModified: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加短剧转盘奖品";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getShortplayTurntablePrize(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改短剧转盘奖品";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateShortplayTurntablePrize(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addShortplayTurntablePrize(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除短剧转盘奖品编号为"' + ids + '"的数据项？').then(function() {
        return delShortplayTurntablePrize(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('turntable/shortplayTurntablePrize/export', {
        ...this.queryParams
      }, `shortplayTurntablePrize_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
