<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="广告账户id，如1739341090482190" prop="advertiserId">
        <el-input
          v-model="queryParams.advertiserId"
          placeholder="请输入广告账户id，如1739341090482190"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认0，安卓1，ios2" prop="androidIos">
        <el-input
          v-model="queryParams.androidIos"
          placeholder="请输入默认0，安卓1，ios2"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="日期，解析自create_time" prop="curDate">
        <el-date-picker clearable
          v-model="queryParams.curDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择日期，解析自create_time">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="对方系统返回" prop="adId">
        <el-input
          v-model="queryParams.adId"
          placeholder="请输入对方系统返回"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="小程序id" prop="wechatId">
        <el-input
          v-model="queryParams.wechatId"
          placeholder="请输入小程序id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="推广id" prop="promotionsId">
        <el-input
          v-model="queryParams.promotionsId"
          placeholder="请输入推广id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单金额" prop="totalFee">
        <el-input
          v-model="queryParams.totalFee"
          placeholder="请输入订单金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="后台账户id" prop="accountId">
        <el-input
          v-model="queryParams.accountId"
          placeholder="请输入后台账户id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="后台账户名称" prop="accountNickname">
        <el-input
          v-model="queryParams.accountNickname"
          placeholder="请输入后台账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否首充，1-是，0-否" prop="isFirstCharge">
        <el-input
          v-model="queryParams.isFirstCharge"
          placeholder="请输入是否首充，1-是，0-否"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否为新用户，1-是，0-否" prop="isNewUser">
        <el-input
          v-model="queryParams.isNewUser"
          placeholder="请输入是否为新用户，1-是，0-否"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第三方订单信息" prop="thirdPartyInfo">
        <el-input
          v-model="queryParams.thirdPartyInfo"
          placeholder="请输入第三方订单信息"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付完成时间，如2021-03-30 00:37:11" prop="finishTime">
        <el-input
          v-model="queryParams.finishTime"
          placeholder="请输入支付完成时间，如2021-03-30 00:37:11"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作品id" prop="bookId">
        <el-input
          v-model="queryParams.bookId"
          placeholder="请输入作品id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作品名称" prop="bookName">
        <el-input
          v-model="queryParams.bookName"
          placeholder="请输入作品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单创建时间" prop="createTimeStr">
        <el-input
          v-model="queryParams.createTimeStr"
          placeholder="请输入订单创建时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户注册时间" prop="userRegTime">
        <el-input
          v-model="queryParams.userRegTime"
          placeholder="请输入用户注册时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告创意id" prop="creativeId">
        <el-input
          v-model="queryParams.creativeId"
          placeholder="请输入广告创意id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="推广链接名称" prop="promotionsName">
        <el-input
          v-model="queryParams.promotionsName"
          placeholder="请输入推广链接名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户识别码" prop="openId">
        <el-input
          v-model="queryParams.openId"
          placeholder="请输入用户识别码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ua" prop="ua">
        <el-input
          v-model="queryParams.ua"
          placeholder="请输入用户ua"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户注册ip" prop="userRegIp">
        <el-input
          v-model="queryParams.userRegIp"
          placeholder="请输入用户注册ip"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="染色时间" prop="dyeingTime">
        <el-input
          v-model="queryParams.dyeingTime"
          placeholder="请输入染色时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付渠道," prop="orderChannel">
        <el-input
          v-model="queryParams.orderChannel"
          placeholder="请输入支付渠道,"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="gmtCreate">
        <el-date-picker clearable
          v-model="queryParams.gmtCreate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="修改时间" prop="gmtModified">
        <el-date-picker clearable
          v-model="queryParams.gmtModified"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择修改时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['agtdata:youhe:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['agtdata:youhe:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['agtdata:youhe:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['agtdata:youhe:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="youheList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键 id" align="center" prop="id" />
      <el-table-column label="广告账户id，如1739341090482190" align="center" prop="advertiserId" />
      <el-table-column label="默认0，安卓1，ios2" align="center" prop="androidIos" />
      <el-table-column label="日期，解析自create_time" align="center" prop="curDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.curDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="小时0~23 ——————" align="center" prop="curHour" />
      <el-table-column label="对方系统返回" align="center" prop="adId" />
      <el-table-column label="用户id" align="center" prop="userId" />
      <el-table-column label="小程序id" align="center" prop="wechatId" />
      <el-table-column label="推广id" align="center" prop="promotionsId" />
      <el-table-column label="订单金额" align="center" prop="totalFee" />
      <el-table-column label="后台账户id" align="center" prop="accountId" />
      <el-table-column label="后台账户名称" align="center" prop="accountNickname" />
      <el-table-column label="是否首充，1-是，0-否" align="center" prop="isFirstCharge" />
      <el-table-column label="是否为新用户，1-是，0-否" align="center" prop="isNewUser" />
      <el-table-column label="订单号" align="center" prop="orderNo" />
      <el-table-column label="订单状态" align="center" prop="orderStatus" />
      <el-table-column label="第三方订单信息" align="center" prop="thirdPartyInfo" />
      <el-table-column label="支付完成时间，如2021-03-30 00:37:11" align="center" prop="finishTime" />
      <el-table-column label="作品id" align="center" prop="bookId" />
      <el-table-column label="作品名称" align="center" prop="bookName" />
      <el-table-column label="订单创建时间" align="center" prop="createTimeStr" />
      <el-table-column label="用户注册时间" align="center" prop="userRegTime" />
      <el-table-column label="广告创意id" align="center" prop="creativeId" />
      <el-table-column label="推广链接名称" align="center" prop="promotionsName" />
      <el-table-column label="用户识别码" align="center" prop="openId" />
      <el-table-column label="用户ua" align="center" prop="ua" />
      <el-table-column label="用户注册ip" align="center" prop="userRegIp" />
      <el-table-column label="染色时间" align="center" prop="dyeingTime" />
      <el-table-column label="支付渠道," align="center" prop="orderChannel" />
      <el-table-column label="创建时间" align="center" prop="gmtCreate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtCreate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center" prop="gmtModified" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtModified, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['agtdata:youhe:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['agtdata:youhe:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改短剧供应商友和对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="广告账户id，如1739341090482190" prop="advertiserId">
          <el-input v-model="form.advertiserId" placeholder="请输入广告账户id，如1739341090482190" />
        </el-form-item>
        <el-form-item label="默认0，安卓1，ios2" prop="androidIos">
          <el-input v-model="form.androidIos" placeholder="请输入默认0，安卓1，ios2" />
        </el-form-item>
        <el-form-item label="日期，解析自create_time" prop="curDate">
          <el-date-picker clearable
            v-model="form.curDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期，解析自create_time">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="对方系统返回" prop="adId">
          <el-input v-model="form.adId" placeholder="请输入对方系统返回" />
        </el-form-item>
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="小程序id" prop="wechatId">
          <el-input v-model="form.wechatId" placeholder="请输入小程序id" />
        </el-form-item>
        <el-form-item label="推广id" prop="promotionsId">
          <el-input v-model="form.promotionsId" placeholder="请输入推广id" />
        </el-form-item>
        <el-form-item label="订单金额" prop="totalFee">
          <el-input v-model="form.totalFee" placeholder="请输入订单金额" />
        </el-form-item>
        <el-form-item label="后台账户id" prop="accountId">
          <el-input v-model="form.accountId" placeholder="请输入后台账户id" />
        </el-form-item>
        <el-form-item label="后台账户名称" prop="accountNickname">
          <el-input v-model="form.accountNickname" placeholder="请输入后台账户名称" />
        </el-form-item>
        <el-form-item label="是否首充，1-是，0-否" prop="isFirstCharge">
          <el-input v-model="form.isFirstCharge" placeholder="请输入是否首充，1-是，0-否" />
        </el-form-item>
        <el-form-item label="是否为新用户，1-是，0-否" prop="isNewUser">
          <el-input v-model="form.isNewUser" placeholder="请输入是否为新用户，1-是，0-否" />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="第三方订单信息" prop="thirdPartyInfo">
          <el-input v-model="form.thirdPartyInfo" placeholder="请输入第三方订单信息" />
        </el-form-item>
        <el-form-item label="支付完成时间，如2021-03-30 00:37:11" prop="finishTime">
          <el-input v-model="form.finishTime" placeholder="请输入支付完成时间，如2021-03-30 00:37:11" />
        </el-form-item>
        <el-form-item label="作品id" prop="bookId">
          <el-input v-model="form.bookId" placeholder="请输入作品id" />
        </el-form-item>
        <el-form-item label="作品名称" prop="bookName">
          <el-input v-model="form.bookName" placeholder="请输入作品名称" />
        </el-form-item>
        <el-form-item label="订单创建时间" prop="createTimeStr">
          <el-input v-model="form.createTimeStr" placeholder="请输入订单创建时间" />
        </el-form-item>
        <el-form-item label="用户注册时间" prop="userRegTime">
          <el-input v-model="form.userRegTime" placeholder="请输入用户注册时间" />
        </el-form-item>
        <el-form-item label="广告创意id" prop="creativeId">
          <el-input v-model="form.creativeId" placeholder="请输入广告创意id" />
        </el-form-item>
        <el-form-item label="推广链接名称" prop="promotionsName">
          <el-input v-model="form.promotionsName" placeholder="请输入推广链接名称" />
        </el-form-item>
        <el-form-item label="用户识别码" prop="openId">
          <el-input v-model="form.openId" placeholder="请输入用户识别码" />
        </el-form-item>
        <el-form-item label="用户ua" prop="ua">
          <el-input v-model="form.ua" placeholder="请输入用户ua" />
        </el-form-item>
        <el-form-item label="用户注册ip" prop="userRegIp">
          <el-input v-model="form.userRegIp" placeholder="请输入用户注册ip" />
        </el-form-item>
        <el-form-item label="染色时间" prop="dyeingTime">
          <el-input v-model="form.dyeingTime" placeholder="请输入染色时间" />
        </el-form-item>
        <el-form-item label="支付渠道," prop="orderChannel">
          <el-input v-model="form.orderChannel" placeholder="请输入支付渠道," />
        </el-form-item>
        <el-form-item label="创建时间" prop="gmtCreate">
          <el-date-picker clearable
            v-model="form.gmtCreate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="修改时间" prop="gmtModified">
          <el-date-picker clearable
            v-model="form.gmtModified"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择修改时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listYouhe, getYouhe, delYouhe, addYouhe, updateYouhe } from "@/api/agtdata/youhe";

export default {
  name: "Youhe",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短剧供应商友和表格数据
      youheList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        advertiserId: null,
        androidIos: null,
        curDate: null,
        curHour: null,
        adId: null,
        userId: null,
        wechatId: null,
        promotionsId: null,
        totalFee: null,
        accountId: null,
        accountNickname: null,
        isFirstCharge: null,
        isNewUser: null,
        orderNo: null,
        orderStatus: null,
        thirdPartyInfo: null,
        finishTime: null,
        bookId: null,
        bookName: null,
        createTimeStr: null,
        userRegTime: null,
        creativeId: null,
        promotionsName: null,
        openId: null,
        ua: null,
        userRegIp: null,
        dyeingTime: null,
        orderChannel: null,
        gmtCreate: null,
        gmtModified: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户id不能为空", trigger: "blur" }
        ],
        wechatId: [
          { required: true, message: "小程序id不能为空", trigger: "blur" }
        ],
        promotionsId: [
          { required: true, message: "推广id不能为空", trigger: "blur" }
        ],
        totalFee: [
          { required: true, message: "订单金额不能为空", trigger: "blur" }
        ],
        accountId: [
          { required: true, message: "后台账户id不能为空", trigger: "blur" }
        ],
        accountNickname: [
          { required: true, message: "后台账户名称不能为空", trigger: "blur" }
        ],
        isFirstCharge: [
          { required: true, message: "是否首充，1-是，0-否不能为空", trigger: "blur" }
        ],
        isNewUser: [
          { required: true, message: "是否为新用户，1-是，0-否不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询短剧供应商友和列表 */
    getList() {
      this.loading = true;
      listYouhe(this.queryParams).then(response => {
        this.youheList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        advertiserId: null,
        androidIos: null,
        curDate: null,
        curHour: null,
        adId: null,
        userId: null,
        wechatId: null,
        promotionsId: null,
        totalFee: null,
        accountId: null,
        accountNickname: null,
        isFirstCharge: null,
        isNewUser: null,
        orderNo: null,
        orderStatus: null,
        thirdPartyInfo: null,
        finishTime: null,
        bookId: null,
        bookName: null,
        createTimeStr: null,
        userRegTime: null,
        creativeId: null,
        promotionsName: null,
        openId: null,
        ua: null,
        userRegIp: null,
        dyeingTime: null,
        orderChannel: null,
        gmtCreate: null,
        gmtModified: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加短剧供应商友和";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getYouhe(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改短剧供应商友和";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateYouhe(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addYouhe(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除短剧供应商友和编号为"' + ids + '"的数据项？').then(function() {
        return delYouhe(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('agtdata/youhe/export', {
        ...this.queryParams
      }, `youhe_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
