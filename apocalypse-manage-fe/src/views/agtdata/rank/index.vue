<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item prop="type">
        <el-radio-group v-model="queryParams.type" @input="handleBdRadioChange">
          <el-radio-button label="1">日榜</el-radio-button>
          <el-radio-button label="2">周榜</el-radio-button>
          <el-radio-button label="3">月榜</el-radio-button>
          <el-radio-button label="4">总榜</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="dateStr" v-if="queryParams.type !== '4'">
        <el-date-picker
          v-model="queryParams.dateStr"
          :type="valueFormatType[queryParams.type]"
          :clearable="false"
          :picker-options="{ firstDayOfWeek: 1 }"
          placeholder="请选择日期"
          :format="format[queryParams.type]"
          value-format="yyyy-MM-dd"
          @change="getList"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="剧名" prop="playletName">
        <el-input
          v-model="queryParams.playletName"
          placeholder="请输入剧名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联方" prop="relatedParty">
        <el-input
          v-model="queryParams.relatedParty"
          placeholder="请输入关联方"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="承制方" prop="contractor">
        <el-input
          v-model="queryParams.contractor"
          placeholder="请输入承制方"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleAdd"
          >导入热力榜</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="rankList">
      <el-table-column label="排行" align="center" prop="ranking" />
      <el-table-column
        label="剧名"
        align="center"
        width="280px"
        prop="playletName"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.playletName }}
            <el-tag
              v-if="queryParams.type === '1' && scope.row.hint"
              size="mini"
              type="danger"
              effect="dark"
              >{{ scope.row.hint }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" prop="tag" />
      <el-table-column label="关联方" align="center" prop="relatedParty" />
      <el-table-column label="承制方" align="center" prop="contractor" />
      <el-table-column label="素材数" align="center" prop="materialCount" />
      <el-table-column
        label="累计素材数"
        align="center"
        prop="totalMaterialCount"
      />
      <el-table-column label="热力值" align="center" prop="consumeNum" />
      <el-table-column
        label="累计热力值"
        align="center"
        prop="totalConsumeNum"
      />
      <el-table-column label="计划数" align="center" prop="promotionCount" />
      <el-table-column
        label="累计计划数"
        align="center"
        prop="totalPromotionCount"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改短剧热力榜日榜对话框 -->
    <el-dialog
      title="导入热力榜"
      :visible.sync="open"
      width="500px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="日期" prop="dateStr">
          <el-date-picker
            v-model="form.dateStr"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="file">
          <el-upload
            ref="upload"
            action=""
            :auto-upload="false"
            :on-change="uploadChange"
            :before-upload="handleBeforeUpload"
            :file-list="fileList"
            accept=".xls,.xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
          >
            <el-button type="primary" id="upload-btn"> 点击上传 </el-button>
            <div slot="tip">
              <a
                style="color: #1890ff"
                href="https://nh-static.elinks.cn/web-static/static-other-resource/%E7%9F%AD%E5%89%A7%E7%83%AD%E5%8A%9B%E6%A6%9Cxxxx-xx-xx.xlsx"
                >下载热力榜导入模板</a
              >
              <div>只能上传.xls/xlsx格式的文件，且不超过10M</div>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      class="error-dialog"
      title="失败提示"
      :visible.sync="errorOpen"
      width="500px"
      append-to-body
    >
      <div style="margin-bottom: 10px" class="error-title">
        以下短剧导入失败，请重新上传!
      </div>
      <el-row :gutter="20">
        <el-col :span="4"><div class="error-title">行数</div></el-col>
        <el-col :span="10"><div class="error-title">短剧名</div></el-col>
        <el-col :span="10"><div class="error-title">错误原因</div></el-col>
      </el-row>

      <el-row
        :gutter="20"
        style="margin-top: 10px"
        v-for="(err, index) in errorList"
        :key="index"
      >
        <el-col :span="4">第{{ err.row }}行</el-col>
        <el-col :span="10"> {{ err.playletName }}</el-col>
        <el-col :span="10" :offset="err.playletName ? 0 : 10">{{
          err.reason
        }}</el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { listRank, addRank } from "@/api/agtdata/rank";
import { parseTime } from "@/utils/ruoyi";
import FileUpload from "@/components/FileUpload";

export default {
  name: "Rank",
  components: {
    FileUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短剧热力榜日榜表格数据
      rankList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      errorOpen: false,
      errorList: [],
      // 查询参数
      queryParams: {
        type: "1",
        pageNum: 1,
        pageSize: 10,
        dateStr: parseTime(Date.now() - 24 * 60 * 60 * 1000, "{y}-{m}-{d}"),
        playletName: null,
        tag: null,
        relatedParty: null,
        contractor: null,
        materialCount: null,
        consumeNum: null,
        promotionCount: null,
        gmtCreate: null,
        gmtModified: null,
      },
      valueFormatType: {
        1: "date",
        2: "week",
        3: "month",
      },
      format: {
        1: "yyyy-MM-dd",
        2: "yyyy 第 WW 周",
        3: "yyyy-MM",
      },
      // 表单参数
      form: {
        dateStr: "",
        file: "",
      },
      fileList: [],
      // 表单校验
      rules: {
        dateStr: [{ required: true, message: "日期不能为空", trigger: "blur" }],
        file: [{ required: true, message: "请导入短剧文件", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleExceed() {
      this.$modal.msgError(`上传文件数量最多1个，请删除后重试!`);
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件大小
      const isLt = file.size / 1024 / 1024 < 10;
      if (!isLt) {
        this.$modal.msgError(`上传文件大小不能超过 10 MB!`);
        return false;
      }
      return true;
    },
    uploadChange(file, fileList) {
      this.fileList = fileList.slice(-1);
      this.form.dateStr = file.name.match(/\d{4}-\d{1,2}-\d{1,2}/)?.[0] || "";
      this.$modal.loading("正在上传文件，请稍候...");
      this.form.file = file.raw;
      this.$modal.closeLoading();
    },
    /** 榜单变化 */
    handleBdRadioChange(value) {
      if (value !== "4") {
        const dateType = value === "1" ? "day" : this.valueFormatType[value];
        this.queryParams.dateStr = this.dayjs()
          .subtract(1, dateType)
          .startOf(dateType)
          .format("YYYY-MM-DD");
      } else {
        this.queryParams.dateStr = "";
      }
      this.getList();
    },
    //导入热力榜
    handleAdd() {
      this.open = true;
    },
    /** 查询短剧热力榜日榜列表 */
    getList() {
      this.loading = true;
      if (this.queryParams.type === "2") {
        this.queryParams.dateStr = this.dayjs(this.queryParams.dateStr)
          .startOf("week")
          .format("YYYY-MM-DD");
      }
      listRank(this.queryParams).then((response) => {
        this.rankList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const fd = new FormData();
          fd.append("file", this.form.file);
          fd.append("dateStr", this.form.dateStr);
          addRank(fd).then((response) => {
            if (response.data && response.data.length) {
              this.errorOpen = true;
              this.errorList = response.data;
              return;
            }
            this.$modal.msgSuccess("导入成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "agtdata/rank/export",
        {
          ...this.queryParams,
        },
        `短剧热力榜导出_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.error-dialog {
  ::v-deep .el-dialog {
    .el-dialog__body {
      padding: 0 20px 20px;
    }
  }
}
.error-title {
  font-size: 15px;
  font-weight: 600;
}
</style>
