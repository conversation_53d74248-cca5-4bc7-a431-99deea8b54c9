<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="78px"
    >
      <el-form-item label="广告账户" prop="filterAdvertiserId">
        <remote-select
          v-model="queryParams.filterAdvertiserId"
          :remote-method="handleFetchAdvertiser"
          multiple
          clearable
          @change="handleQuery"
        ></remote-select>
      </el-form-item>
      <el-form-item label="分销平台" prop="filterSupplierName">
        <el-select
          v-model="queryParams.filterSupplierName"
          filterable
          multiple
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in supplierOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="投手" prop="filterUserNickname">
        <el-select
          v-model="queryParams.filterUserNickname"
          filterable
          multiple
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in userOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="短剧名称" prop="playletName">
        <el-input
          v-model="queryParams.playletName"
          placeholder="请输入短剧名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="时间范围" prop="dateRange">
        <DatePickerRange
          clearable
          v-model="queryParams.dateRange"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </DatePickerRange>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['agtdata:roiall:export']"
          >导出</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" class="mt4">
        <DropdownCheckboxGroup
          v-model="queryParams.dimension"
          :options="dimensionOptions"
          :default-value="dimensionDefaultValue"
          @change="handleDimensionChange"
        >
          <el-link type="primary" :underline="false"
            >维度选择<i class="el-icon-arrow-down el-icon--right"></i
          ></el-link>
        </DropdownCheckboxGroup>
      </el-col>
      <el-col :span="1.5">
        <el-alert
          class="size-small"
          title="巨量有消耗账户3分钟更新一次，无消耗账户1小时更新一次"
          type="warning"
          show-icon
          :closable="false"
        >
        </el-alert
      ></el-col>
      <right-toolbar
        :name="columnsStorageName"
        :showSearch.sync="showSearch"
        :columns.sync="columns"
        :default-selected-column-keys="[
          'cost',
          'costIncome',
          'roiNhAddAd',
          'costProfit',
          'convertUv',
          'convertCost',
        ]"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      :key="tableKey"
      v-loading="loading"
      :data="roiallList"
      @sort-change="handleSortChange"
    >
      <el-table-column
        label="日期"
        align="center"
        prop="curDate"
        width="100"
        fixed
        v-if="queryParams.dimension.includes('curDate')"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.curDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="短剧名称"
        align="center"
        prop="playletName"
        width="150"
        fixed
        v-if="queryParams.dimension.includes('playletName')"
      />
      <el-table-column
        label="投手"
        align="center"
        prop="userNickname"
        width="100"
        fixed
        v-if="queryParams.dimension.includes('userNickname')"
      >
        <template slot-scope="scope">
          {{ scope.row.userNickname }}({{ scope.row.userRealname }})
        </template>
      </el-table-column>
      <el-table-column
        label="主体"
        align="center"
        prop="accountId"
        width="150"
        v-if="queryParams.dimension.includes('accountId')"
      >
      </el-table-column>
      <el-table-column
        label="广告账号"
        width="280"
        prop="advertiserName"
        v-if="queryParams.dimension.includes('advertiserId')"
      >
        <template slot-scope="scope">
          <a
            class="hover-light"
            :href="`https://ad.oceanengine.com/promotion/promote-manage/project?aadvid=${scope.row.advertiserId}`"
            target="_blank"
          >
            <TextEllipsis
              :row="2"
              :text="`${scope.row.advertiserId || '-'}/${
                scope.row.advertiserName || '-'
              }`"
            ></TextEllipsis>
          </a>
        </template>
      </el-table-column>
      <el-table-column
        label="分销平台"
        align="center"
        width="80"
        prop="supplierNameShort"
        v-if="queryParams.dimension.includes('supplierNameShort')"
      />
      <el-table-column
        label="平台"
        align="center"
        width="60"
        prop="platformDyWx"
        v-if="queryParams.dimension.includes('platformDyWx')"
      >
        <template slot-scope="scope">
          {{ constants.platformDyWx[scope.row.platformDyWx] || "未知" }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="cost"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="90"
        v-if="this.columns.cost.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">消耗</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="巨量账面消耗（未去赔付）：巨量所有主体下的所有广告账户消耗数据之和"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="现金消耗"
        align="center"
        prop="costCash"
        width="120"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.costCash.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">现金消耗</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="巨量账面消耗（未去赔付）/1.05"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="costIncome"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="105"
        v-if="this.columns.costIncome.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">总充值</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="所有平台充值之和"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="costIncomeShare"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="165"
        v-if="this.columns.costIncomeShare.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">真实充值(分成后)</span>
          <el-tooltip class="item" effect="dark" placement="top">
            <i class="el-icon-question"></i>
            <template slot="content"
              >所有平台（安卓充值*安卓分成）+（IOS充值*IOS分成）之和</template
            >
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="payCount"
        width="110"
        v-if="this.columns.payCount.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">充值人数UV</span>
          <el-tooltip class="item" effect="dark" placement="top">
            <i class="el-icon-question"></i>
            <template slot="content">各个平台充值成功的uv数量</template>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="充值笔数PV"
        prop="payOrderCount"
        width="100"
        v-if="this.columns.payOrderCount.visible"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="payArpu"
        width="120"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        v-if="this.columns.payArpu.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">短剧arpu</span>
          <el-tooltip class="item" effect="dark" placement="top">
            <i class="el-icon-question"></i>
            <template slot="content">充值金额/充值人数</template>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="激活数"
        prop="activeCount"
        width="80"
        v-if="this.columns.activeCount.visible"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="激活成本"
        prop="activeCost"
        width="80"
        v-if="this.columns.activeCost.visible"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="costIncomeAdv"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="110"
        v-if="this.columns.costIncomeAdv.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">广告金</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="所有平台安卓充值*广告金分成之和"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="110"
        prop="roiNhAddAd"
        v-if="this.columns.roiNhAddAd.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">诺禾ROI含</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="诺禾ROI（含广告金）：「真实充值（分成后）+广告金收入*0.85」/现金消耗"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="120"
        prop="roiNh"
        v-if="this.columns.roiNh.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">诺禾ROI不含</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="诺禾ROI（不含广告金）：真实充值（分成后）/现金消耗"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="roi"
        width="100"
        v-if="this.columns.roi.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">账面ROI</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="总充值/账面消耗"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="costProfit"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="90"
        v-if="this.columns.costProfit.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">利润</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="真实充值（分成后）+广告金收入*0.85-现金消耗"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="转化数"
        align="center"
        prop="convertUv"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="85"
        v-if="this.columns.convertUv.visible"
      />
      <el-table-column
        align="center"
        prop="convertRate"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="110"
        v-if="this.columns.convertRate.visible"
      >
        <template slot="header">
          <span style="margin-right: 5px">转化率</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="转化数/点击数*100%"
            placement="top"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="平均转化成本"
        align="center"
        width="100"
        prop="convertCost"
        v-if="this.columns.convertCost.visible"
      />
      <el-table-column
        label="展示数"
        align="center"
        prop="showPv"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="85"
        v-if="this.columns.showPv.visible"
      />
      <el-table-column
        label="点击数"
        align="center"
        prop="click"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="85"
        v-if="this.columns.click.visible"
      />
      <el-table-column
        label="素材点击率"
        align="center"
        width="120"
        prop="ctr"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        :formatter="handleCtrFormatter"
        v-if="this.columns.ctr.visible"
      />
      <el-table-column
        label="点击arpu"
        align="center"
        prop="avgClickCost"
        sortable="custom"
        :sort-orders="['descending', 'ascending', null]"
        width="100"
        v-if="this.columns.avgClickCost.visible"
      />
      <!-- 填充空白 -->
      <el-table-column />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { dataDetail } from "@/api/agtdata/roiall";
import { listJuliangad } from "@/api/agtaccount/juliangad";
import { listSupplier } from "@/api/agtaccount/supplier";
import { listUser } from "@/api/agtaccount/user";
import storage from "@/utils/storage";
import { parseTime } from "@/utils/ruoyi";
import { platformDyWx } from "@/constants/agt";
import { createUniqueString, fake } from "@/utils";

// 维度选择默认值
const dimensionDefaultValue = [
  "curDate",
  "advertiserId",
  "playletName",
  "userNickname",
  "supplierNameShort",
];

// 维度选择本地存储名称
const DIMENSION_STORAGE_NAME = "playset-data-list-dimension";

// 显隐列本地存储名称，最后数字为版本号，为避免修改对应数据后线上不生效，需要更新版本号
const COLUMNS_STORAGE_NAME = "playset-data-list-columns_2";

// 表单搜索记录本地存储名称
const QUERY_PARAMS_STORAGE_NAME = "query-params-storage-name";

export default {
  name: "Roiall",
  data() {
    return {
      // 表格key
      tableKey: createUniqueString(),
      // 常量
      constants: {
        platformDyWx,
      },
      // 遮罩层
      loading: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分销平台数据roi表格数据
      roiallList: [],
      // 分销平台列表
      supplierOptions: [],
      // 投手列表
      userOptions: [],
      // 维度选择
      dimensionOptions: [
        { label: "日期", value: "curDate" },
        { label: "主体ID/名称", value: "accountId" },
        { label: "广告账户ID/名称", value: "advertiserId" },
        { label: "短剧名称", value: "playletName" },
        { label: "投手", value: "userNickname" },
        { label: "分销平台", value: "supplierNameShort" },
        { label: "微小/抖小", value: "platformDyWx" },
      ],
      // 维度默认数据
      dimensionDefaultValue,
      // 查询参数
      queryParams: Object.assign(
        {
          playletName: null,
          filterAdvertiserId: [],
          filterSupplierName: [],
          filterUserNickname: [],
        },
        storage.local.getByUser(QUERY_PARAMS_STORAGE_NAME),
        {
          pageNum: 1,
          pageSize: 10,
          dateRange: [
            parseTime(Date.now(), "{y}-{m}-{d}"),
            parseTime(Date.now(), "{y}-{m}-{d}"),
          ],
          dimension: storage.local.getByUser(DIMENSION_STORAGE_NAME) || [
            ...dimensionDefaultValue,
          ],
        }
      ),
      // 表单参数
      form: {},
      // 显隐列本地存储
      columnsStorageName: COLUMNS_STORAGE_NAME,
      // 表格列显隐控制
      columns: Object.assign(
        {
          cost: {
            label: "消耗",
            key: "cost",
            visible: true,
          },
          costCash: {
            label: "现金消耗",
            key: "costCash",
            visible: false,
          },
          costIncome: {
            label: "总充值",
            key: "costIncome",
            visible: true,
          },
          costIncomeShare: {
            label: "真实充值(分成后)",
            key: "costIncomeShare",
            visible: false,
          },
          payCount: {
            label: "充值人数UV",
            key: "payCount",
            visible: false,
          },
          payOrderCount: {
            label: "充值笔数PV",
            key: "payOrderCount",
            visible: false,
          },
          payArpu: {
            label: "短剧arpu",
            key: "payArpu",
            visible: false,
          },
          activeCount: {
            label: "激活数",
            key: "activeCount",
            visible: false,
          },
          activeCost: {
            label: "激活成本",
            key: "activeCost",
            visible: false,
          },
          costIncomeAdv: {
            label: "广告金",
            key: "costIncomeAdv",
            visible: false,
          },
          roiNhAddAd: {
            label: "诺禾ROI含",
            key: "roiNhAddAd",
            visible: true,
          },
          roiNh: {
            label: "诺禾ROI不含",
            key: "roiNh",
            visible: false,
          },
          roi: { label: "账面ROI", key: "roi", visible: false },
          costProfit: {
            label: "利润",
            key: "costProfit",
            visible: true,
          },
          convertUv: {
            label: "转化数",
            key: "convertUv",
            visible: true,
          },
          convertRate: {
            label: "转化率",
            key: "convertRate",
            visible: false,
          },
          convertCost: {
            label: "平均转化成本",
            key: "convertCost",
            visible: true,
          },
          showPv: {
            label: "展示数",
            key: "showPv",
            visible: false,
          },
          click: {
            label: "点击数",
            key: "click",
            visible: false,
          },
          ctr: {
            label: "素材点击率",
            key: "ctr",
            visible: false,
          },
          avgClickCost: {
            label: "点击arpu",
            key: "avgClickCost",
            visible: false,
          },
        },
        storage.local.getByUser(COLUMNS_STORAGE_NAME)
      ),
    };
  },
  watch: {
    columns: {
      handler() {
        this.tableKey = createUniqueString();
      },
      deep: true,
    },
    queryParams: {
      handler() {
        // 清除列表请求的本地存储标记，防止筛选条件变更后，getList不执行
        storage.session.remove("roiall-get-list");
      },
      deep: true,
    },
  },
  created() {
    this.getList();
    this.handleFetchSupplier();
    this.handleFetchUser();
  },
  methods: {
    /** 维度变更 */
    handleDimensionChange(value) {
      storage.local.setByUser(DIMENSION_STORAGE_NAME, value);
      this.handleQuery();
      this.tableKey = createUniqueString();
    },
    /** 素材点击率格式化 */
    handleCtrFormatter(_, __, value) {
      if (typeof value !== "number") return value;
      return `${value}%`;
    },
    /** 查询分销平台数据roi列表 */
    getList() {
      // 5秒发一次真正的请求
      fake(
        "roiall-get-list",
        () => {
          if (this.loading) return;
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
          }, 500);
        },
        () => {
          const { dateRange, ...params } = this.queryParams;
          this.loading = true;
          dataDetail({
            ...params,
            orderBy: this.orderBy || [],
            filterStartDate: dateRange?.[0],
            filterEndDate: dateRange?.[1],
          }).then((response) => {
            // 以下搜索条件做本地存储
            storage.local.setByUser(QUERY_PARAMS_STORAGE_NAME, {
              playletName: params.playletName,
              filterAdvertiserId: params.filterAdvertiserId,
              filterSupplierName: params.filterSupplierName,
              filterUserNickname: params.filterUserNickname,
            });
            this.roiallList = response.list;
            this.total = response.total;
            this.loading = false;
          });
        },
        5000
      );
    },
    /** 排序变更 */
    handleSortChange({ prop, order }) {
      if (order) {
        this.orderBy = [
          {
            field: prop,
            type: order.replace("ending", ""),
          },
        ];
      } else {
        this.orderBy = [];
      }
      storage.session.remove("roiall-get-list");
      this.getList();
    },
    /** 获取广告账户列表 */
    async handleFetchAdvertiser(advertiserName) {
      const { list } = await listJuliangad({ advertiserName, pageSize: 5 });
      return list.map((item) => ({
        label: item.advertiserName,
        value: item.advertiserId,
      }));
    },
    /** 获取分销平台列表 */
    async handleFetchSupplier(supplierNameShort) {
      const { list } = await listSupplier({
        supplierNameShort,
        pageSize: 10e6,
      });
      this.supplierOptions = list.map((item) => ({
        label: item.supplierNameShort,
        value: item.supplierNameShort,
      }));
    },
    /** 获取投手列表 */
    async handleFetchUser(userNickname) {
      const { list } = await listUser({
        userNickname,
        pageSize: 10e6,
        post: 2,
      });
      this.userOptions = list.map((item) => ({
        label: `${item.userNickname}(${item.userRealname})`,
        value: item.userNickname,
      }));
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.$nextTick(() => {
        this.getList();
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      const { dateRange, ...params } = this.queryParams;
      this.download(
        "agtdata/roiall/dataDetail/export",
        {
          ...params,
          filterStartDate: dateRange?.[0],
          filterEndDate: dateRange?.[1],
        },
        `短剧分销数据明细_${parseTime(new Date(), "{y}-{m}-{d}")}.xlsx`
      );
    },
  },
};
</script>
