<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker v-model="queryParams.dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
          @change="handleQuery"></el-date-picker>
      </el-form-item>
      <el-form-item label="广告账户ID" prop="filterAdvertiserId">
        <el-input v-model="queryParams.filterAdvertiserId" placeholder="请输入广告账户ID" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="广告账户名" prop="filterAdvertiserName">
        <el-input v-model="queryParams.filterAdvertiserName" placeholder="请输入广告账户名" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="投手" prop="filterUserNickname">
        <el-select v-model="queryParams.filterUserNickname" filterable multiple clearable @change="handleQuery">
          <el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="小程序" prop="filterAppId">
        <el-select v-model="queryParams.filterAppId" filterable multiple clearable @change="handleQuery">
          <el-option v-for="item in miniappList" :key="item.miniappId" :label="item.miniappName" :value="item.miniappId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="短剧名" prop="filterTvName">
        <el-input v-model="queryParams.filterTvName" placeholder="请输入短剧名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['data:oceanEngineProjectDateData:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" class="mt4">
        <DropdownCheckboxGroup v-model="queryParams.dimension" :options="dimensionOptions"
          :default-value="dimensionDefaultValue" @change="handleDimensionChange">
          <el-link type="primary" :underline="false">维度选择<i class="el-icon-arrow-down el-icon--right"></i></el-link>
        </DropdownCheckboxGroup>
      </el-col>
      <right-toolbar :name="columnsStorageName" :showSearch.sync="showSearch" @queryTable="getList"
        :columns.sync="columns" :default-selected-column-keys="Object.keys(columns)"></right-toolbar>
    </el-row>

    <el-table :key="tableKey" v-loading="loading" :data="roiallList">
      <el-table-column label="日期" align="center" prop="curDate" width="100" fixed
        v-if="queryParams.dimension.includes('cur_date')">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.curDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="组织ID" align="center" prop="accountId" width="150" fixed
        v-if="queryParams.dimension.includes('account_id')" /> -->
      <el-table-column label="组织名称" align="center" prop="accountNameCo" width="150" fixed
        v-if="queryParams.dimension.includes('account_name_co')" />
      <el-table-column label="广告账号ID" width="150" prop="advertiserId"
        v-if="queryParams.dimension.includes('advertiser_id')">
        <!-- <template slot-scope="scope">
          <a class="hover-light"
            :href="`https://ad.oceanengine.com/promotion/promote-manage/project?aadvid=${scope.row.advertiserId}`"
            target="_blank">
            <TextEllipsis :row="2" :text="`${scope.row.advertiserId || '-'}/${scope.row.advertiserName || '-'
              }`"></TextEllipsis>
          </a>
        </template> -->
      </el-table-column>
      <el-table-column label="账户名" align="center" prop="advertiserName" width="150" fixed
        v-if="queryParams.dimension.includes('advertiser_name')" />
      <el-table-column label="项目名称" align="center" prop="projectName" width="150" fixed
        v-if="queryParams.dimension.includes('project_name')" />
      <el-table-column label="投手" align="center" prop="userNickname" width="80" fixed
        v-if="queryParams.dimension.includes('user_nickname')">
        <template slot-scope="scope">
          {{ scope.row.userRealname }}
        </template>
      </el-table-column>
      <el-table-column label="小程序" align="center" prop="appName" width="150" fixed
        v-if="queryParams.dimension.includes('app_id')" />
      <el-table-column label="短剧名" align="center" prop="tvName" width="150" fixed
        v-if="queryParams.dimension.includes('tv_id')" />
      <el-table-column label="代理商" align="center" prop="accountAgent" width="150"
        v-if="queryParams.dimension.includes('account_agent')" />
      <el-table-column label="项目ID" align="center" prop="projectId" width="150"
        v-if="queryParams.dimension.includes('project_id')" />
      <!-- <el-table-column label="短剧名称" align="center" prop="playletName" width="150" fixed
        v-if="queryParams.dimension.includes('playletName')" /> -->
      <el-table-column label="账面消耗" align="center" prop="statCost" width="90" v-if="this.columns.statCost.visible">
      </el-table-column>
      <el-table-column label="现金消耗" align="center" prop="cashCost" width="120" v-if="this.columns.cashCost.visible">
        <template slot="header">
          <span style="margin-right: 5px">现金消耗</span>
          <el-tooltip class="item" effect="dark" content="账面消耗/（1+返点）" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" label="展示数" prop="showCnt" width="100" v-if="this.columns.showCnt.visible">
      </el-table-column>
      <el-table-column align="center" label="cpm千次展现费用" prop="cpmPlatform" width="100"
        v-if="this.columns.cpmPlatform.visible">
      </el-table-column>
      <el-table-column align="center" label="点击数" prop="clickCnt" width="100" v-if="this.columns.clickCnt.visible">
      </el-table-column>
      <el-table-column align="center" label="点击率" prop="ctr" width="100" v-if="this.columns.ctr.visible">
        <template slot-scope="scope">
          {{ scope.row.ctr }}%
        </template>
      </el-table-column>
      <el-table-column align="center" label="平均点击单价(元)" prop="cpcPlatform" width="100"
        v-if="this.columns.cpcPlatform.visible">
      </el-table-column>
      <el-table-column align="center" label="激活数" prop="active" width="100" v-if="this.columns.active.visible">
      </el-table-column>
      <el-table-column align="center" label="激活成本" prop="activeCost" width="100" v-if="this.columns.activeCost.visible">
      </el-table-column>
      <el-table-column align="center" label="激活率" prop="activeRate" width="100" v-if="this.columns.activeRate.visible">
        <template slot-scope="scope">
          {{ scope.row.activeRate }}%
        </template>
      </el-table-column>
      <el-table-column align="center" label="关键行为数" prop="gameAddiction" width="100"
        v-if="this.columns.gameAddiction.visible">
      </el-table-column>
      <el-table-column align="center" label="关键行为成本" prop="gameAddictionCost" width="100"
        v-if="this.columns.gameAddictionCost.visible">
      </el-table-column>
      <el-table-column align="center" label="关键行为率" prop="gameAddictionRate" width="100"
        v-if="this.columns.gameAddictionRate.visible">
        <template slot-scope="scope">
          {{ scope.row.gameAddictionRate }}%
        </template>
      </el-table-column>
      <el-table-column align="center" label="转化数" prop="convertCnt" width="100" v-if="this.columns.convertCnt.visible">
      </el-table-column>
      <el-table-column align="center" label="平均转化成本" prop="conversionCost" width="100"
        v-if="this.columns.conversionCost.visible">
      </el-table-column>
      <el-table-column align="center" label="转化率" prop="conversionRate" width="100"
        v-if="this.columns.conversionRate.visible">
        <template slot-scope="scope">
          {{ scope.row.conversionRate }}%
        </template>
      </el-table-column>
      <el-table-column align="center" label="当日广告变现ROI" prop="attributionMicroGame0dRoi" width="100"
        v-if="this.columns.attributionMicroGame0dRoi.visible">
      </el-table-column>
      <el-table-column align="center" label="当日变现金额（激活时间）" prop="attributionMicroGameIaapLtv1day" width="100"
        v-if="this.columns.attributionMicroGameIaapLtv1day.visible">
      </el-table-column>
      <el-table-column align="center" label="24小时变现金额（激活时间）" prop="statAttributionMicroGame24hAmount" width="100"
        v-if="this.columns.statAttributionMicroGame24hAmount.visible">
      </el-table-column>
      <el-table-column align="center" label="7日综合ROI（激活时间）" prop="attributionMicroGameIaapLtv7dRoi" width="100"
        v-if="this.columns.attributionMicroGameIaapLtv7dRoi.visible">
      </el-table-column>
      <el-table-column align="center" label="7日变现金额（激活时间）" prop="statAttributionMicroGame7dAmount" width="100"
        v-if="this.columns.statAttributionMicroGame7dAmount.visible">
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { listOceanEngineProjectDateData } from "@/api/data/oceanEngineProjectDateData";
import { listJuliang } from "@/api/agtaccount/juliang";
import { getDYMiniappList } from "@/api/agtdata/miniapp"
import { listUser } from "@/api/agtaccount/user";
import storage from "@/utils/storage";
import { parseTime } from "@/utils/ruoyi";
import { platformDyWx } from "@/constants/agt";
import { createUniqueString } from "@/utils";

// 维度选择默认值
const dimensionDefaultValue = [
  "cur_date", "account_name_co", "account_agent", "advertiser_id", "advertiser_name", "project_name", "project_id", "user_nickname", "app_id", "tv_id"
];

// 维度选择本地存储名称
const DIMENSION_STORAGE_NAME = "playset-data-list-dimension_1";

// 显隐列本地存储名称，最后数字为版本号，为避免修改对应数据后线上不生效，需要更新版本号
const COLUMNS_STORAGE_NAME = "playset-data-list-columns_3";

export default {
  name: "Roiall",
  data() {
    return {
      // 表格key
      tableKey: createUniqueString(),
      // 常量
      constants: {
        platformDyWx,
      },
      // 遮罩层
      loading: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分销平台数据roi表格数据
      roiallList: [],
      // 小程序列表
      miniappList: [],
      agentDatas: [],
      // 投手列表
      userOptions: [],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        }
      },
      // 维度选择
      dimensionOptions: [
        { label: "日期", value: "cur_date" },

        // { label: "组织ID", value: "account_id" },

        { label: "组织名称", value: "account_name_co" },

        { label: "代理商", value: "account_agent" },

        { label: "广告账号ID", value: "advertiser_id" },

        { label: "账户名", value: "advertiser_name" },
        { label: "项目名称", value: "project_name" },
        { label: "项目ID", value: "project_id" },
        { label: "投手", value: "user_nickname" },
        { label: "小程序", value: "app_id" },
        { label: "短剧名称", value: "tv_id" }
      ],
      // 维度默认数据
      dimensionDefaultValue,
      // 查询参数
      queryParams: Object.assign(
        {
          filterAppId: [],
          filterAdvertiserName: "",
          filterAdvertiserId: "",
          filterTvName: "",
          filterUserNickname: [],
        },
        {
          pageNum: 1,
          pageSize: 10,
          dateRange: [
            parseTime(Date.now(), "{y}-{m}-{d}"),
            parseTime(Date.now(), "{y}-{m}-{d}"),
          ],
          dimension: storage.local.getByUser(DIMENSION_STORAGE_NAME) || [
            ...dimensionDefaultValue,
          ],
        }
      ),
      // 表单参数
      form: {},
      // 显隐列本地存储
      columnsStorageName: COLUMNS_STORAGE_NAME,
      // 表格列显隐控制
      columns: Object.assign(
        {
          statCost: {
            key: 'statCost',
            label: '账面消耗',
            visible: true
          },
          cashCost: {
            key: 'cashCost',
            label: '投流现金消耗（元）',
            visible: true
          },
          showCnt: {
            key: 'showCnt',
            label: '展示数',
            visible: true
          },
          cpmPlatform: {
            key: 'cpmPlatform',
            label: 'cpm千次展现费用',
            visible: true
          },
          clickCnt: {
            key: 'clickCnt',
            label: '点击数',
            visible: true
          },
          ctr: {
            key: 'ctr',
            label: '点击率',
            visible: true
          },
          cpcPlatform: {
            key: 'cpcPlatform',
            label: '平均点击单价(元)',
            visible: true
          },
          active: {
            key: 'active',
            label: '激活数',
            visible: true
          },
          activeCost: {
            key: 'activeCost',
            label: '激活成本',
            visible: true
          },
          activeRate: {
            key: 'activeRate',
            label: '激活率',
            visible: true
          },
          gameAddiction: {
            key: 'gameAddiction',
            label: '关键行为数',
            visible: true
          },
          gameAddictionCost: {
            key: 'gameAddictionCost',
            label: '关键行为成本',
            visible: true
          },
          gameAddictionRate: {
            key: 'gameAddictionRate',
            label: '关键行为率',
            visible: true
          },
          convertCnt: {
            key: 'convertCnt',
            label: '转化数',
            visible: true
          },
          conversionCost: {
            key: 'conversionCost',
            label: '平均转化成本',
            visible: true
          },
          conversionRate: {
            key: 'conversionRate',
            label: '转化率',
            visible: true
          },
          attributionMicroGame0dRoi: {
            key: 'attributionMicroGame0dRoi',
            label: '当日广告变现ROI',
            visible: true
          },
          attributionMicroGameIaapLtv1day: {
            key: 'attributionMicroGameIaapLtv1day',
            label: '当日变现金额（激活时间）',
            visible: true
          },
          statAttributionMicroGame24hAmount: {
            key: 'statAttributionMicroGame24hAmount',
            label: '24小时变现金额（激活时间）',
            visible: true
          },
          attributionMicroGameIaapLtv7dRoi: {
            key: 'attributionMicroGameIaapLtv7dRoi',
            label: '7日综合ROI（激活时间）',
            visible: true
          },
          statAttributionMicroGame7dAmount: {
            key: 'statAttributionMicroGame7dAmount',
            label: '7日变现金额（激活时间）',
            visible: true
          }
        },
        storage.local.getByUser(COLUMNS_STORAGE_NAME)
      ),
    };
  },
  watch: {
    columns: {
      handler() {
        this.tableKey = createUniqueString();
      },
      deep: true,
    },
    queryParams: {
      handler() {
        // 清除列表请求的本地存储标记，防止筛选条件变更后，getList不执行
        storage.session.remove("roiall-get-list");
      },
      deep: true,
    },
  },
  created() {
    this.getList();
    this.getMiniapp();
    this.getListJuliang()
    this.handleFetchUser();
  },
  methods: {
    /** 维度变更 */
    handleDimensionChange(value) {
      storage.local.setByUser(DIMENSION_STORAGE_NAME, value);
      this.handleQuery();
      this.tableKey = createUniqueString();
    },
    /** 素材点击率格式化 */
    handleCtrFormatter(_, __, value) {
      if (typeof value !== "number") return value;
      return `${value}%`;
    },
    /** 查询分销平台数据roi列表 */
    getList() {
      const { dateRange, ...params } = this.queryParams;
      this.loading = true;
      listOceanEngineProjectDateData({
        ...params,
        filterStartDate: dateRange?.[0],
        filterEndDate: dateRange?.[1],
      }).then((response) => {
        this.roiallList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取组织 */
    async getListJuliang() {
      const { list } = await listJuliang({
        pageSize: 10e6,
        pageNum: 1,
      });
      this.agentDatas = list
    },
    /** 获取小程序列表 */
    async getMiniapp() {
      const { list } = await getDYMiniappList({
        pageSize: 10e6,
        pageNum: 1,
      });
      this.miniappList = list;
    },
    /** 获取投手列表 */
    async handleFetchUser(userNickname) {
      const { list } = await listUser({
        userNickname,
        pageSize: 10e6,
        post: 2,
      });
      this.userOptions = list.map((item) => ({
        label: `${item.userNickname}(${item.userRealname})`,
        value: item.userNickname,
      }));
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.$nextTick(() => {
        this.getList();
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      const { dateRange, ...params } = this.queryParams;
      this.download(
        "data/oceanEngineProjectDateData/export",
        {
          ...params,
          filterStartDate: dateRange?.[0],
          filterEndDate: dateRange?.[1],
        },
        `数据明细_${parseTime(new Date(), "{y}-{m}-{d}")}.xlsx`
      );
    },
  },
};
</script>
