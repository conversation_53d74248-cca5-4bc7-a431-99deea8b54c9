<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="日期" prop="dateRange">
        <DatePickerRange clearable v-model="queryParams.dateRange" type="daterange" value-format="yyyy-MM-dd"
                         range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleQuery">
        </DatePickerRange>
      </el-form-item>
      <el-form-item label="广告位id" prop="slotId">
        <el-input
          v-model="queryParams.slotId"
          placeholder="请输入广告位id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['slotdata:slotData:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['slotdata:slotData:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['slotdata:slotData:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['slotdata:slotData:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="slotDataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="日期" align="center" prop="curDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.curDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="广告位id" align="center" prop="slotId" />
      <el-table-column label="广告位名称" align="center" prop="slotName" />
      <el-table-column label="配置id" align="center" prop="configId" />
      <el-table-column label="配置名称" align="center" prop="configName" />
      <el-table-column label="内容id" align="center" prop="configItemId" />
      <el-table-column label="内容名称" align="center" prop="contentName" />
      <el-table-column label="访问pv" align="center" prop="pv" />
      <el-table-column label="访问uv" align="center" prop="uv" />
      <el-table-column label="创建时间" align="center" prop="gmtCreate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtCreate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['slotdata:slotData:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['slotdata:slotData:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改广告位地域分流日数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="日期" prop="curDate">
          <el-date-picker clearable
            v-model="form.curDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="广告位id" prop="slotId">
          <el-input v-model="form.slotId" placeholder="请输入广告位id" />
        </el-form-item>
        <el-form-item label="配置id" prop="configId">
          <el-input v-model="form.configId" placeholder="请输入配置id" />
        </el-form-item>
        <el-form-item label="内容id" prop="configItemId">
          <el-input v-model="form.configItemId" placeholder="请输入内容id" />
        </el-form-item>
        <el-form-item label="访问pv" prop="pv">
          <el-input v-model="form.pv" placeholder="请输入访问pv" />
        </el-form-item>
        <el-form-item label="访问uv" prop="uv">
          <el-input v-model="form.uv" placeholder="请输入访问uv" />
        </el-form-item>
        <el-form-item label="创建时间" prop="gmtCreate">
          <el-date-picker clearable
            v-model="form.gmtCreate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSlotData, getSlotData, delSlotData, addSlotData, updateSlotData } from "@/api/slotdata/slotData";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "SlotData",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 广告位地域分流日数据表格数据
      slotDataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        slotId: null,
        dateRange: [
          parseTime(Date.now() , "{y}-{m}-{d}"),
          parseTime(Date.now() , "{y}-{m}-{d}"),
        ],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        curDate: [
          { required: true, message: "日期不能为空", trigger: "blur" }
        ],
        slotId: [
          { required: true, message: "广告位id不能为空", trigger: "blur" }
        ],
        configId: [
          { required: true, message: "配置id不能为空", trigger: "blur" }
        ],
        configItemId: [
          { required: true, message: "内容id不能为空", trigger: "blur" }
        ],
        gmtCreate: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询广告位地域分流日数据列表 */
    getList() {
      this.loading = true;
      const { dateRange, ...params } = this.queryParams;
      listSlotData({
        ...params,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
      }).then(response => {
        this.slotDataList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        curDate: null,
        slotId: null,
        configId: null,
        configItemId: null,
        pv: null,
        uv: null,
        gmtCreate: null,
        gmtModified: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加广告位地域分流日数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getSlotData(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改广告位地域分流日数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSlotData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSlotData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除广告位地域分流日数据编号为"' + ids + '"的数据项？').then(function() {
        return delSlotData(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('slotdata/slotData/export', {
        ...this.queryParams
      }, `slotData_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
