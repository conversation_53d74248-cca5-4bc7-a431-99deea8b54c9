<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分销简称唯一，如美光" prop="supplierNameShort">
        <el-input
          v-model="queryParams.supplierNameShort"
          placeholder="请输入分销简称唯一，如美光"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分销公司全称" prop="supplierNameReal">
        <el-input
          v-model="queryParams.supplierNameReal"
          placeholder="请输入分销公司全称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="平台:1.抖小,2.微小" prop="platformDyWx">
        <el-input
          v-model="queryParams.platformDyWx"
          placeholder="请输入平台:1.抖小,2.微小"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Android分成比例%，分成 * 0.9" prop="wxAndroidRatio">
        <el-input
          v-model="queryParams.wxAndroidRatio"
          placeholder="请输入Android分成比例%，分成 * 0.9"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IOS分成比例，%" prop="wxIosRatio">
        <el-input
          v-model="queryParams.wxIosRatio"
          placeholder="请输入IOS分成比例，%"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告金分成比例%，安卓乘以" prop="wxAdRatio">
        <el-input
          v-model="queryParams.wxAdRatio"
          placeholder="请输入广告金分成比例%，安卓乘以"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Android分成比例%）" prop="dyAndroidRatio">
        <el-input
          v-model="queryParams.dyAndroidRatio"
          placeholder="请输入Android分成比例%）"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IOS分成比例，%" prop="dyIosRatio">
        <el-input
          v-model="queryParams.dyIosRatio"
          placeholder="请输入IOS分成比例，%"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告金分成比例%，安卓乘以" prop="dyAdRatio">
        <el-input
          v-model="queryParams.dyAdRatio"
          placeholder="请输入广告金分成比例%，安卓乘以"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="gmtCreate">
        <el-date-picker clearable
          v-model="queryParams.gmtCreate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="修改时间" prop="gmtModified">
        <el-date-picker clearable
          v-model="queryParams.gmtModified"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择修改时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['agtaccount:supplier:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['agtaccount:supplier:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['agtaccount:supplier:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['agtaccount:supplier:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="supplierList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键id" align="center" prop="id" />
      <el-table-column label="分销简称唯一，如美光" align="center" prop="supplierNameShort" />
      <el-table-column label="分销公司全称" align="center" prop="supplierNameReal" />
      <el-table-column label="平台:1.抖小,2.微小" align="center" prop="platformDyWx" />
      <el-table-column label="Android分成比例%，分成 * 0.9" align="center" prop="wxAndroidRatio" />
      <el-table-column label="IOS分成比例，%" align="center" prop="wxIosRatio" />
      <el-table-column label="广告金分成比例%，安卓乘以" align="center" prop="wxAdRatio" />
      <el-table-column label="Android分成比例%）" align="center" prop="dyAndroidRatio" />
      <el-table-column label="IOS分成比例，%" align="center" prop="dyIosRatio" />
      <el-table-column label="广告金分成比例%，安卓乘以" align="center" prop="dyAdRatio" />
      <el-table-column label="创建时间" align="center" prop="gmtCreate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtCreate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center" prop="gmtModified" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.gmtModified, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['agtaccount:supplier:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['agtaccount:supplier:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改分销三方供剧商对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="分销简称唯一，如美光" prop="supplierNameShort">
          <el-input v-model="form.supplierNameShort" placeholder="请输入分销简称唯一，如美光" />
        </el-form-item>
        <el-form-item label="分销公司全称" prop="supplierNameReal">
          <el-input v-model="form.supplierNameReal" placeholder="请输入分销公司全称" />
        </el-form-item>
        <el-form-item label="平台:1.抖小,2.微小" prop="platformDyWx">
          <el-input v-model="form.platformDyWx" placeholder="请输入平台:1.抖小,2.微小" />
        </el-form-item>
        <el-form-item label="Android分成比例%，分成 * 0.9" prop="wxAndroidRatio">
          <el-input v-model="form.wxAndroidRatio" placeholder="请输入Android分成比例%，分成 * 0.9" />
        </el-form-item>
        <el-form-item label="IOS分成比例，%" prop="wxIosRatio">
          <el-input v-model="form.wxIosRatio" placeholder="请输入IOS分成比例，%" />
        </el-form-item>
        <el-form-item label="广告金分成比例%，安卓乘以" prop="wxAdRatio">
          <el-input v-model="form.wxAdRatio" placeholder="请输入广告金分成比例%，安卓乘以" />
        </el-form-item>
        <el-form-item label="Android分成比例%）" prop="dyAndroidRatio">
          <el-input v-model="form.dyAndroidRatio" placeholder="请输入Android分成比例%）" />
        </el-form-item>
        <el-form-item label="IOS分成比例，%" prop="dyIosRatio">
          <el-input v-model="form.dyIosRatio" placeholder="请输入IOS分成比例，%" />
        </el-form-item>
        <el-form-item label="广告金分成比例%，安卓乘以" prop="dyAdRatio">
          <el-input v-model="form.dyAdRatio" placeholder="请输入广告金分成比例%，安卓乘以" />
        </el-form-item>
        <el-form-item label="创建时间" prop="gmtCreate">
          <el-date-picker clearable
            v-model="form.gmtCreate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="修改时间" prop="gmtModified">
          <el-date-picker clearable
            v-model="form.gmtModified"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择修改时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSupplier, getSupplier, delSupplier, addSupplier, updateSupplier } from "@/api/agtaccount/supplier";

export default {
  name: "Supplier",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分销三方供剧商表格数据
      supplierList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierNameShort: null,
        supplierNameReal: null,
        platformDyWx: null,
        wxAndroidRatio: null,
        wxIosRatio: null,
        wxAdRatio: null,
        dyAndroidRatio: null,
        dyIosRatio: null,
        dyAdRatio: null,
        gmtCreate: null,
        gmtModified: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        platformDyWx: [
          { required: true, message: "平台:1.抖小,2.微小不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询分销三方供剧商列表 */
    getList() {
      this.loading = true;
      listSupplier(this.queryParams).then(response => {
        this.supplierList = response.list;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        supplierNameShort: null,
        supplierNameReal: null,
        platformDyWx: null,
        wxAndroidRatio: null,
        wxIosRatio: null,
        wxAdRatio: null,
        dyAndroidRatio: null,
        dyIosRatio: null,
        dyAdRatio: null,
        gmtCreate: null,
        gmtModified: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加分销三方供剧商";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getSupplier(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改分销三方供剧商";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSupplier(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSupplier(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除分销三方供剧商编号为"' + ids + '"的数据项？').then(function() {
        return delSupplier(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('agtaccount/supplier/export', {
        ...this.queryParams
      }, `supplier_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
