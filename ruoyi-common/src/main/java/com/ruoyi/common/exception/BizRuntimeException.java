package com.ruoyi.common.exception;

import com.ruoyi.common.constant.ErrorCode;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/15 16:58
 */
@Data
public class BizRuntimeException extends RuntimeException {

    private Integer code;

    private ErrorCode errorCode;

    public BizRuntimeException(String message) {
        super(message);
    }

    public BizRuntimeException(Integer code, String message){
        super(message);
        this.code = code;
    }

    public BizRuntimeException(ErrorCode errorCode){
        super(errorCode.getMsg());
        this.errorCode = errorCode;
        this.code = errorCode.getCode();
    }

}