package com.ruoyi.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;

/**
 * 短剧权益券核销状态
 * <AUTHOR>
 * @date 2024/4/23 10:10
 */
@Getter
@AllArgsConstructor
public enum PlayletCouponUseStatusEnum {
    // 0未使用
    UNUSED(0, "UNUSED","未使用"),
    //主动使用
    ACTIVE_USE(1, "USED","主动使用"),
    //被动使用
    PASSIVE_USE(2, "USED","被动使用"),
    //过期
    EXPIRED(3, "EXPIRED","过期"),
    ;

    private Integer status;
    private String statusStr;
    private String desc;

    public static String getStatusStrByStatus(int status) {
        for (PlayletCouponUseStatusEnum statusEnum : PlayletCouponUseStatusEnum.values()) {
            if (statusEnum.getStatus() == status) {
                return statusEnum.getStatusStr();
            }
        }
        return null;
    }
    public static String getStatusDescByStatus(int status) {
        for (PlayletCouponUseStatusEnum statusEnum : PlayletCouponUseStatusEnum.values()) {
            if (statusEnum.getStatus() == status) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }

    public static boolean isUsed(int status){
        return Lists.newArrayList(ACTIVE_USE.status,PASSIVE_USE.status).contains(status);
    }
    public static int getUseStatus(int status, Date expiredTime){
        if(Lists.newArrayList(ACTIVE_USE.status,PASSIVE_USE.status).contains(status)){
            //被动使用和主动使用都返回使用
            return ACTIVE_USE.status;
        }
        if(status == UNUSED.status && expiredTime.before(new Date())){
            //未使用且过期
            return EXPIRED.status;
        }
        return status;
    }
}