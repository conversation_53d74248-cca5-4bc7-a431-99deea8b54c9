package com.ruoyi.common.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 短剧小程序媒体权益类型枚举
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Getter
@AllArgsConstructor
public enum AgentCouponTypeEnum {

    DAILY(1, "日度会员"),
    WEEKLY(2, "周度会员"),
    MONTHLY(3, "月度会员"),
    QUARTER(4, "季度会员"),
    ANNUAL(5, "年度会员"),
    UNLOCK_ALL(6, "解锁全集"),
    ;

    private final int type;
    private final String desc;

    public static String getDescByType(int type) {
        for (AgentCouponTypeEnum typeEnum : AgentCouponTypeEnum.values()) {
            if (Objects.equals(typeEnum.getType(), type)) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
