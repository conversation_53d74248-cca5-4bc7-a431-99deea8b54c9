package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 支付通道类型
 *
 * <AUTHOR>
 * @Date 2023/10/12 14:52
 */
@Getter
@AllArgsConstructor
public enum PayChannelTypeEnum {
    ALIPAY_APP_PAY(1, "支付宝app支付"),
    ALIPAY_APP_SUB_CYCLE(2, "支付宝续费订阅(周期扣款)"),
    ALIPAY_APP_SUB_BUSINESS(3, "支付宝续费订阅(商家扣款)");

    private final Integer type;
    private final String desc;

    public static PayChannelTypeEnum getByType(Integer type) {
        if (null == type) {
            return null;
        }
        for (PayChannelTypeEnum e : values()) {
            if (Objects.equals(e.getType(), type)) {
                return e;
            }
        }
        return null;
    }

}
