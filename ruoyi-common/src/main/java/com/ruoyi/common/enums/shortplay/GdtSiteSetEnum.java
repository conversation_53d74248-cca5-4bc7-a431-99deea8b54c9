package com.ruoyi.common.enums.shortplay;

/**
 * 广点通平台版位枚举类
 */
public enum GdtSiteSetEnum {
    /**
     * 微信朋友圈
     */
    SITE_SET_MOMENTS("SITE_SET_MOMENTS", "微信朋友圈"),
    
    /**
     * 微信公众号与小程序
     */
    SITE_SET_WECHAT("SITE_SET_WECHAT", "微信公众号与小程序"),
    
    /**
     * 腾讯看点
     */
    SITE_SET_KANDIAN("SITE_SET_KANDIAN", "腾讯看点"),
    
    /**
     * QQ、腾讯音乐及游戏
     */
    SITE_SET_QQ_MUSIC_GAME("SITE_SET_QQ_MUSIC_GAME", "QQ、腾讯音乐及游戏"),
    
    /**
     * QQ、腾讯看点、腾讯音乐（待废弃）
     */
    SITE_SET_MOBILE_INNER("SITE_SET_MOBILE_INNER", "QQ、腾讯看点、腾讯音乐（待废弃）"),
    
    /**
     * 腾讯新闻
     */
    SITE_SET_TENCENT_NEWS("SITE_SET_TENCENT_NEWS", "腾讯新闻"),
    
    /**
     * 腾讯视频
     */
    SITE_SET_TENCENT_VIDEO("SITE_SET_TENCENT_VIDEO", "腾讯视频"),
    
    /**
     * 优量汇
     */
    SITE_SET_MOBILE_UNION("SITE_SET_MOBILE_UNION", "优量汇"),
    
    /**
     * 应用宝
     */
    SITE_SET_MOBILE_YYB("SITE_SET_MOBILE_YYB", "应用宝"),
    
    /**
     * PC QQ、QQ 空间、腾讯音乐
     */
    SITE_SET_PCQQ("SITE_SET_PCQQ", "PC QQ、QQ 空间、腾讯音乐"),
    
    /**
     * 微信小游戏
     */
    SITE_SET_MINI_GAME_WECHAT("SITE_SET_MINI_GAME_WECHAT", "微信小游戏"),
    
    /**
     * QQ 小游戏
     */
    SITE_SET_MINI_GAME_QQ("SITE_SET_MINI_GAME_QQ", "QQ 小游戏"),
    
    /**
     * App 游戏
     */
    SITE_SET_MOBILE_GAME("SITE_SET_MOBILE_GAME", "App 游戏"),
    
    /**
     * QQ浏览器、应用宝版位
     */
    SITE_SET_QBSEARCH("SITE_SET_QBSEARCH", "QQ浏览器、应用宝版位"),
    
    /**
     * 微信视频号
     */
    SITE_SET_CHANNELS("SITE_SET_CHANNELS", "微信视频号"),
    
    /**
     * 微信搜一搜
     */
    SITE_SET_WECHAT_SEARCH("SITE_SET_WECHAT_SEARCH", "微信搜一搜"),

    /**
     * 未知
     */
    SITE_SET_UNKNOWN("SITE_SET_UNKNOWN", "未知");

    private final String code;
    private final String desc;

    GdtSiteSetEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据版位代码获取枚举实例
     *
     * @param code 版位代码
     * @return 对应的枚举实例，若不存在则返回null
     */
    public static GdtSiteSetEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        
        for (GdtSiteSetEnum siteSet : GdtSiteSetEnum.values()) {
            if (siteSet.getCode().equals(code)) {
                return siteSet;
            }
        }
        
        return null;
    }
    
    /**
     * 判断给定的代码是否为有效的版位代码
     * 
     * @param code 版位代码
     * @return 若代码存在于枚举中，返回true；否则返回false
     */
    public static boolean isValidSiteSet(String code) {
        return getByCode(code) != null;
    }
} 