package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 金币流水类型
 * <AUTHOR>
 * @date 2023/7/4 11:36
 */
@Getter
@AllArgsConstructor
public enum FlowTypeEnum {
    CONSUME(1,"消费"),
    CHARGE(2,"充值"),
    REFUND(3,"退款"),
    IAA_CHARGE(4,"激励广告奖励充值"),
    IAA_CONSUME(5,"激励广告解锁消费"),
    ;
    private Integer type;

    private String desc;

    public static String getByType(Integer type) {
        for (FlowTypeEnum value : FlowTypeEnum.values()) {
            if (Objects.equals(value.getType(),type)) {
                return value.desc;
            }
        }
        return null;
    }
}