package com.ruoyi.common.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 短剧小程序媒体权益状态类型枚举
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Getter
@AllArgsConstructor
public enum AgentCouponStatusEnum {

    UNCLAIMED(0,"未领取"),
    UNCLAIMED_EXPIRED(1,"超时领取"),
    CLAIMED_UNUSED(2,"已领取"),
    USED(3,"已使用"),
    EXPIRED(4,"已过期"),
    ;

    private final int type;
    private final String desc;

    public static String getDescByType(int type){
        for(AgentCouponStatusEnum typeEnum : AgentCouponStatusEnum.values()){
            if(Objects.equals(typeEnum.getType(),type)){
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
