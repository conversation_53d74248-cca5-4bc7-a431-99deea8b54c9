package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * app类型枚举
 */
@Getter
@AllArgsConstructor
public enum AppTypeEnum {
    SHORT_PLAY(1, "短剧"),
    NOVEL(2, "小说");

    private final int code;
    private final String msg;
    
    /**
     * 检查给定的code是否是有效的枚举值
     *
     * @param code 要检查的code值
     * @return 如果是有效的枚举值返回true，否则返回false
     */
    public static boolean isValid(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        
        for (AppTypeEnum type : AppTypeEnum.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return true;
            }
        }
        return false;
    }
}
