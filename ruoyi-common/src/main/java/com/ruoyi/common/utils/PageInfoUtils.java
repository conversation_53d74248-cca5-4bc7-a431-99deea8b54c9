package com.ruoyi.common.utils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页工具类
 *
 * <AUTHOR>
 * @date 2021/9/6
 */
public class PageInfoUtils {

    public static <VO, DTO> PageInfo<VO> dto2Vo(List<DTO> list, Function<DTO, VO> convert) {
        if (null == list) {
            return buildReturnList(Collections.emptyList());
        }

        PageInfo<DTO> source = new PageInfo<>(list);
        Page<VO> dest = new Page<>(source.getPageNum(), source.getPageSize());
        dest.setTotal(source.getTotal());
        PageInfo<VO> result = new PageInfo<>(dest);
        if (CollectionUtils.isNotEmpty(source.getList())) {
            result.setList(source.getList().stream().map(convert).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * 构建返回列表分页对象 ,total为resultList的大小
     * @param resultList 结果列表
     * @param <VO> 对象类型
     * @return 结果
     */
    public static <VO> PageInfo<VO> buildReturnList(List<VO> resultList) {
        return new PageInfo<>(resultList);
    }
}
