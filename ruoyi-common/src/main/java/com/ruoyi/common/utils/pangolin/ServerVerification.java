package com.ruoyi.common.utils.pangolin;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.security.MessageDigest;

@Slf4j
public class ServerVerification {

    private String getMsgSignature(String tpToken, String timestamp, String nonce, String encrypt) {
        String[] values = new String[] {tpToken, timestamp, nonce, encrypt};
        Arrays.sort(values);

        StringBuilder sb = new StringBuilder();
        for (String value : values) {
            sb.append(value);
        }

        String str = sb.toString();

        try {
            //指定sha1算法
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
            messageDigest.update(str.getBytes());

            //获取字节数组
            byte[] messageDigestByte = messageDigest.digest();

            StringBuilder hexStr = new StringBuilder();
            // 字节数组转换为十六进制数
            for (byte b : messageDigestByte) {
                String shaHex = Integer.toHexString(b & 0xFF);
                if (shaHex.length() < 2) {
                    hexStr.append(0);
                }
                hexStr.append(shaHex);
            }

            return hexStr.toString();
        } catch (Exception e) {
            log.error("ServerVerification.getMsgSignature error, 不能生成签名", e);
        }
        return "";
    }

    private boolean verify(String msgSignature, String newMsgSignature) {
        return msgSignature.equals(newMsgSignature);
    }

    public static boolean verify(String tpToken, String timestamp, String nonce, String encrypt, String newMsgSignature) {
        ServerVerification test = new ServerVerification();
        return test.getMsgSignature(tpToken, timestamp, nonce, encrypt).equals(newMsgSignature);
    }

    public static void main(String[] args) {
        ServerVerification test = new ServerVerification();
        String msgSignature = "XXX";
        String newMsgSignature = test.getMsgSignature("XXX", "XXX", "XXX", "XXX");
        boolean result = test.verify(msgSignature, newMsgSignature);
        System.out.println(result);
    }
}
