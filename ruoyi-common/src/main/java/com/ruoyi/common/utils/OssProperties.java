package com.ruoyi.common.utils;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * oss 配置类
 * <AUTHOR>
 * @date 2022/6/10 3:58 下午
 */
@Configuration
@ConfigurationProperties(prefix = "oss")
public class OssProperties {
    /**
     * oss url
     */
    private static String url = "https://common.acthao.com/";
    /**
     * endpoint
     */
    private static String endpoint = "http://oss-cn-hangzhou-internal.aliyuncs.com";
    /**
     * access key
     */
    private String accessId = "LTAI5tLGKudCmLbqTnSgQCKz";
    /**
     * access secret
     */
    private String accessSecret = "******************************";
    /**
     * bucket name
     */
    private static String bucket = "nh-common";
    /**
     * path
     */
    private static String path = "";

    public static String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        OssProperties.url = url;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccessId() {
        return this.accessId;
    }

    public void setAccessId(String accessId) {
        this.accessId = accessId;
    }

    public String getAccessSecret() {
        return this.accessSecret;
    }

    public void setAccessSecret(String accessSecret) {
        this.accessSecret = accessSecret;
    }

    public static String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        OssProperties.bucket = bucket;
    }

    public static String getPath() {
        return path;
    }

    public void setPath(String path) {
        OssProperties.path = path;
    }
}
