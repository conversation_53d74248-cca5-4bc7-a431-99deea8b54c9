package com.ruoyi.common.utils;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.spring.SpringUtils;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 业务工具类
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
public class BizUtils {

    /**
     * 计算uv
     *
     * @param key Redis缓存Key
     * @param value 去重的value
     * @return 新增的uv
     */
    public static int countUv(String key, String value) {
        return countUv(key, value, 1, TimeUnit.DAYS);
    }

    /**
     * 计算uv
     *
     * @param key Redis缓存Key
     * @param value 去重的value
     * @param timeout 过期时间
     * @param unit 过期时间单位
     * @return 新增的uv
     */
    public static int countUv(String key, String value, final long timeout, final TimeUnit unit) {
        if (StringUtils.isBlank(key)) {
            return 0;
        }

        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Boolean isExist = redisCache.isSetMember(key, value);
        if (Objects.equals(isExist, true)) {
            return 0;
        }
        redisCache.sAdd(key, value, timeout, unit);
        return 1;
    }
}
