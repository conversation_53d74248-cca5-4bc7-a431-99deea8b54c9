package com.ruoyi.common.utils;

import lombok.extern.slf4j.Slf4j;
import ws.schild.jave.MultimediaInfo;
import ws.schild.jave.MultimediaObject;

import java.net.URL;

@Slf4j
public class GetUrlTime {

    public static Integer parseDuration(String fileUrl) {
        try {
            URL source = new URL(fileUrl);
            // 构造方法 接受URL对象
            MultimediaObject instance = new MultimediaObject(source);
            // 构造方法 接受File对象
//            MultimediaObject instance = new MultimediaObject(new File(fileUrl));
            MultimediaInfo result = instance.getInfo();
            Long ls = result.getDuration() / 1000;
            ls += result.getDuration() % 1000 >= 500 ? 1L:0L;
            return ls.intValue();

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return 0;
    }

}
