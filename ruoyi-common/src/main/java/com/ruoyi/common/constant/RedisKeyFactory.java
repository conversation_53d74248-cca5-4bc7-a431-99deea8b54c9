package com.ruoyi.common.constant;

/**
 * rediskey
 * <AUTHOR>
 * @date 2024/3/28 13:54
 */
public enum RedisKeyFactory {
    K001("校验微信上传任务状态分布式锁"),
    K002("校验微信剧目审核任务状态分布式锁"),
    K003("异步上传视频缓存"),
    <PERSON>004("Ip解析地域"),
    K005("抖音AccessToken缓存"),
    <PERSON>006("抖音图片视频资源缓存-废弃"),
    <PERSON>007("抖音图片视频资源缓存-废弃"),
    <PERSON>008("抖音图片视频资源缓存"),
    K009("抖音视频id和自有视频id映射"),
    K011("定时任务分布式锁(通用)"),
    K012("巨量accesstoken缓存"),
    K013("巨量refreshToken缓存"),
    K014("抖音优惠券发放分布式锁"),
    K015("发权益券分布式锁"),
    K016("核销短剧券分布式锁"),
    K017("接口缓存"),
    K018("微信小程序AccessToken缓存"),

    K020("短剧小程序微信session_key缓存"),
    K021("短剧小程序AccessToken缓存"),
    K022("短剧小程序UrlScheme缓存"),

    K024("短剧小程序dataId缓存"),
    K025("短剧小程序行为埋点UV"),

    K027("短剧小程序广告曝光UV"),
    K028("创量素材同步时间缓存"),
    K029("抖音关键词数据缓存"),
    K030("抖音关键词监控分布式锁"),
    K031("抖音关键词上次更新数据"),
    K032("抖音关键词素材自增序列"),

    K035("优选上报-用户每5分钟解锁MAP"),
    K036("优选上报-当日新用户注册数"),
    K037("用户广告每日IPU统计"),
    K038("广点通补偿上报"),
    K039("广点通广告数据上次同步时间"),
    K040("广点通素材拉取上次创建时间记录"),
    K041("钉钉通知命名规范"),
    K042("巨量账户拉取变频"),
    K043("触摸token缓存"),
    K044("定时同步ROI分布式锁"),
    K045("定时同步素材分布式锁"),
    K046("广点通accesstoken缓存"),
    K048("广点通定时任务分布式锁"),
    K049("同步小说分布式锁"),
    K050("微信小说用户缓存锁"),
    K051("小说授权分布式锁"),
    K052("爆量创计划分布式锁"),
    K053("手动同步广点通广告账号数据分布式锁"),

    K055("抖音第三方小程序component_ticket缓存"),
    K056("抖音第三方小程序component_access_token缓存"),
    K057("抖音第三方小程序authorization_code缓存"),
    K058("抖音第三方小程序authorizer_access_token缓存"),

    K061("爆款小程序UV统计"),

    K065("短剧小程序上报去重"),
    K066("短剧小程序扣量上报"),
    K067("短故事小程序上报去重"),

    K071("地域分流数据缓存"),
    K072("用户当日广告点击数缓存"),
    K073("用户上报缓存"),
    K074("广点通监测缓存"),
    K075("清表分布式锁"),
    K076("ip解析缓存"),
    K077("广点通ROI企业微信提醒"),
    K078("注册上报clickId锁"),
    K079("广点通版位数据用户统计"),
    K080("用户看完广告PV"),
    K081("用户小程序进入时间戳缓存"),
    K082("短剧小程序红包用户阶段缓存"),
    K083("停留时长上报分布式锁"),
    K200("地域分流uv数据缓存"),
    K201("地域分流小时数据id缓存"),
    K202("地域分流日数据更新分布式锁"),
    ;

    private static final String SPACE = "RyPlaylet";
    private static final String SEPARATOR = "_";

    private final String desc;

    RedisKeyFactory(String desc){
        this.desc = desc;
    }

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    @Override
    public String toString() {
        return SPACE + SEPARATOR + this.name();
    }
}
