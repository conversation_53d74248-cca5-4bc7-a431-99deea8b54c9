package com.ruoyi.common.constant;

/**
 * 广告引擎的Redis Key
 * <AUTHOR>
 * @date 2021/9/17 10:18 上午
 */
public enum EngineRedisKeyFactory {

    IA001("用户短剧激励视频UV"),
    IA002("巨量点击检测回传规则"),
    ;

    private static final String SPACE = "iaa";
    private static final String SEPARATOR = "_";

    private final String desc;

    EngineRedisKeyFactory(String desc){
        this.desc = desc;
    }

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    @Override
    public String toString() {
        return SPACE + SEPARATOR + this.name();
    }
}
