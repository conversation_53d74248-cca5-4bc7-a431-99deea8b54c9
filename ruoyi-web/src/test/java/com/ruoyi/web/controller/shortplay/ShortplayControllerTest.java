package com.ruoyi.web.controller.shortplay;

import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.entity.system.SysConfig;

import com.ruoyi.system.service.ruoyi.ISysConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ClickId限制功能集成测试
 * 直接调用gdtLoginReportClickIdLock和dyLoginReportClickIdLock方法
 */
@SpringBootTest
public class ShortplayControllerTest {

    @Autowired
    private ShortplayController shortplayController;

    @Autowired
    private RedisCache redisTemplate;

    @Autowired
    private ISysConfigService sysConfigService;

    private static final String TEST_APP_ID = "testApp001";
    private static final String TEST_USER_ID_1 = "user123";
    private static final String TEST_USER_ID_2 = "user456";
    private static final String TEST_CLICK_ID_1 = "click001";
    private static final String TEST_CLICK_ID_2 = "click002";
    private static final String TEST_CLICK_ID_3 = "click003";

    @BeforeEach
    void setUp() {
        // 清理测试相关的Redis数据
        cleanupRedisData();

        // 设置默认测试配置
        setupDefaultConfig();
    }

    private void cleanupRedisData() {
        // 清理可能存在的测试数据
        for (int i = 0; i < 50; i++) {
            String key =  RedisKeyFactory.K078.join("shard", i);
            redisTemplate.deleteObject(key);
        }
    }

    private void setupDefaultConfig() {
        // 设置默认配置：限制3个clickId
        updateConfigValue("shortplay.gdt.clickId.loginReport.appid", TEST_APP_ID + "-3");
        updateConfigValue("shortplay.dy.clickId.loginReport.appid", TEST_APP_ID + "-3");
    }

    /**
     * 更新配置值的辅助方法
     */
    private void updateConfigValue(String configKey, String configValue) {
        // 先查询是否存在该配置
        SysConfig queryConfig = new SysConfig();
        queryConfig.setConfigKey(configKey);
        queryConfig.setConfigValue(configValue);
        if ( "shortplay.gdt.clickId.loginReport.appid".equals(configKey) ) {
            queryConfig.setConfigId(115l);
        }
        if ( "shortplay.dy.clickId.loginReport.appid".equals(configKey) ) {
            queryConfig.setConfigId(117l);
        }

        sysConfigService.updateConfig(queryConfig);
    }

    /**
     * 测试1：广点通 - 首次上报成功
     * 前置条件：配置testApp001-3，用户首次上报clickId
     * 预期结果：返回true
     */
    @Test
    void testGdtFirstClickIdSuccess() {
        boolean result = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, TEST_USER_ID_1);

        assertTrue(result, "广点通首次上报clickId应该成功");
    }

    /**
     * 测试2：广点通 - 重复提交拒绝
     * 前置条件：同一用户重复上报相同clickId
     * 预期结果：第二次返回false
     */
    @Test
    void testGdtDuplicateClickIdRejected() {
        // 第一次上报
        boolean result1 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, TEST_USER_ID_1);
        assertTrue(result1, "第一次上报应该成功");

        // 第二次上报相同clickId
        boolean result2 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, TEST_USER_ID_1);
        assertFalse(result2, "重复提交应该被拒绝");
    }

    /**
     * 测试3：广点通 - 达到上限拒绝
     * 前置条件：配置限制为2，用户已上报2个不同clickId
     * 预期结果：第3个clickId被拒绝
     */
    @Test
    void testGdtLimitReachedRejected() {
        // 设置限制为2
        updateConfigValue("shortplay.gdt.clickId.loginReport.appid", TEST_APP_ID + "-2");

        // 上报第1个clickId
        boolean result1 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, TEST_USER_ID_1);
        assertTrue(result1, "第1个clickId应该成功");

        // 上报第2个clickId
        boolean result2 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_2, TEST_APP_ID, TEST_USER_ID_1);
        assertTrue(result2, "第2个clickId应该成功");

        // 上报第3个clickId（超过限制）
        boolean result3 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_3, TEST_APP_ID, TEST_USER_ID_1);
        assertFalse(result3, "第3个clickId应该被拒绝（超过限制）");
    }

    /**
     * 测试4：抖音 - 首次上报成功
     * 前置条件：配置testApp001-3，用户首次上报clickId
     * 预期结果：返回true
     */
    @Test
    void testDyFirstClickIdSuccess() {
        boolean result = shortplayController.dyLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, TEST_USER_ID_2);

        assertTrue(result, "抖音首次上报clickId应该成功");
    }

    /**
     * 测试5：抖音 - 重复提交拒绝
     * 前置条件：同一用户重复上报相同clickId
     * 预期结果：第二次返回false
     */
    @Test
    void testDyDuplicateClickIdRejected() {
        // 第一次上报
        boolean result1 = shortplayController.dyLoginReportClickIdLock(TEST_CLICK_ID_2, TEST_APP_ID, TEST_USER_ID_2);
        assertTrue(result1, "第一次上报应该成功");

        // 第二次上报相同clickId
        boolean result2 = shortplayController.dyLoginReportClickIdLock(TEST_CLICK_ID_2, TEST_APP_ID, TEST_USER_ID_2);
        assertFalse(result2, "重复提交应该被拒绝");
    }

    /**
     * 测试6：用户隔离测试
     * 前置条件：不同用户上报相同clickId
     * 预期结果：不同用户之间互不影响
     */
    @Test
    void testUserIsolation() {
        // 用户1上报clickId
        boolean result1 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, TEST_USER_ID_1);
        assertTrue(result1, "用户1上报应该成功");

        // 用户2上报相同clickId
        boolean result2 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, TEST_USER_ID_2);
        assertTrue(result2, "用户2上报相同clickId应该成功（用户隔离）");
    }

    /**
     * 测试7：参数为空，允许通过
     * 前置条件：传入空参数
     * 预期结果：返回true（容错处理）
     */
    @Test
    void testNullParametersAllowed() {
        // 测试clickId为空
        boolean result1 = shortplayController.gdtLoginReportClickIdLock(null, TEST_APP_ID, TEST_USER_ID_1);
        assertTrue(result1, "clickId为空应该允许通过");

        // 测试appId为空
        boolean result2 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, null, TEST_USER_ID_1);
        assertTrue(result2, "appId为空应该允许通过");

        // 测试userId为空
        boolean result3 = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, null);
        assertTrue(result3, "userId为空应该允许通过");
    }

    /**
     * 测试8：刚好达到限制允许
     * 前置条件：配置限制为3，用户上报第3个clickId
     * 预期结果：第3个clickId成功（刚好达到限制）
     */
    @Test
    void testExactLimitAllowed() {
        String userId = "exactLimitUser";

        // 上报第1个clickId
        boolean result1 = shortplayController.gdtLoginReportClickIdLock("exact001", TEST_APP_ID, userId);
        assertTrue(result1, "第1个clickId应该成功");

        // 上报第2个clickId
        boolean result2 = shortplayController.gdtLoginReportClickIdLock("exact002", TEST_APP_ID, userId);
        assertTrue(result2, "第2个clickId应该成功");

        // 上报第3个clickId（刚好达到限制）
        boolean result3 = shortplayController.gdtLoginReportClickIdLock("exact003", TEST_APP_ID, userId);
        assertTrue(result3, "第3个clickId应该成功（刚好达到限制）");

        // 上报第4个clickId（超过限制）
        boolean result4 = shortplayController.gdtLoginReportClickIdLock("exact004", TEST_APP_ID, userId);
        assertFalse(result4, "第4个clickId应该被拒绝（超过限制）");
    }

    /**
     * 测试9：性能测试 - 大量clickId上报
     * 前置条件：配置限制为100，快速上报多个clickId
     * 预期结果：性能在可接受范围内
     */
    @Test
    void testPerformanceWithManyClickIds() {
        // 设置限制为100
        updateConfigValue("shortplay.gdt.clickId.loginReport.appid", TEST_APP_ID + "-100");

        String userId = "performanceUser";
        long startTime = System.currentTimeMillis();

        // 上报50个不同的clickId
        for (int i = 0; i < 50; i++) {
            String clickId = "perf" + String.format("%03d", i);
            boolean result = shortplayController.gdtLoginReportClickIdLock(clickId, TEST_APP_ID, userId);
            assertTrue(result, "第" + i + "个clickId应该成功");
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println("上报50个clickId耗时: " + duration + "ms");
        assertTrue(duration < 10000, "性能应该在可接受范围内（10秒内）");
    }

    /**
     * 测试10：边界条件 - 限制为1
     * 前置条件：配置限制为1
     * 预期结果：第1个成功，第2个失败
     */
    @Test
    void testLimitOne() {
        // 设置抖音限制为1
        updateConfigValue("shortplay.dy.clickId.loginReport.appid", TEST_APP_ID + "-1");

        String userId = "limitOneUser";

        // 上报第1个clickId
        boolean result1 = shortplayController.dyLoginReportClickIdLock("limit001", TEST_APP_ID, userId);
        assertTrue(result1, "限制为1时，第1个应该成功");

        // 尝试上报第2个clickId
        boolean result2 = shortplayController.dyLoginReportClickIdLock("limit002", TEST_APP_ID, userId);
        assertFalse(result2, "限制为1时，第2个应该失败");
    }

    /**
     * 测试11：字符串匹配精确性
     * 前置条件：上报相似但不同的clickId
     * 预期结果：相似clickId应该被视为不同的clickId
     */
    @Test
    void testStringMatchingAccuracy() {
        String userId = "accuracyUser";

        // 上报click001
        boolean result1 = shortplayController.gdtLoginReportClickIdLock("click001", TEST_APP_ID, userId);
        assertTrue(result1, "click001应该成功");

        // 尝试上报click0011（部分匹配，应该成功）
        boolean result2 = shortplayController.gdtLoginReportClickIdLock("click0011", TEST_APP_ID, userId);
        assertTrue(result2, "click0011应该成功（不同的clickId）");

        // 尝试上报1click001（部分匹配，应该成功）
        boolean result3 = shortplayController.gdtLoginReportClickIdLock("1click001", TEST_APP_ID, userId);
        assertTrue(result3, "1click001应该成功（不同的clickId）");
    }

    /**
     * 测试12：混合平台测试
     * 前置条件：同时测试广点通和抖音平台
     * 预期结果：不同平台使用不同配置，互不影响
     */
    @Test
    void testMixedPlatforms() {
        String userId = "mixedUser";

        // 广点通平台上报
        boolean gdtResult1 = shortplayController.gdtLoginReportClickIdLock("mixed001", TEST_APP_ID, userId);
        assertTrue(gdtResult1, "广点通平台上报应该成功");

        // 抖音平台上报相同clickId
        boolean dyResult1 = shortplayController.dyLoginReportClickIdLock("mixed001", TEST_APP_ID, userId);
        assertTrue(dyResult1, "抖音平台上报相同clickId应该成功（不同平台配置）");

        // 广点通平台重复上报
        boolean gdtResult2 = shortplayController.gdtLoginReportClickIdLock("mixed001", TEST_APP_ID, userId);
        assertFalse(gdtResult2, "广点通平台重复上报应该失败");

        // 抖音平台重复上报
        boolean dyResult2 = shortplayController.dyLoginReportClickIdLock("mixed001", TEST_APP_ID, userId);
        assertFalse(dyResult2, "抖音平台重复上报应该失败");
    }

    /**
     * 测试13：配置格式测试 - 不限制数量
     * 前置条件：配置为只有appId，不设置数量限制
     * 预期结果：可以上报大量不同clickId
     */
    @Test
    void testUnlimitedConfig() {
        // 设置不限制数量的配置
        updateConfigValue("shortplay.gdt.clickId.loginReport.appid", TEST_APP_ID);

        String userId = "unlimitedUser";

        // 上报10个不同的clickId，都应该成功
        for (int i = 0; i < 10; i++) {
            String clickId = "unlimited" + String.format("%03d", i);
            boolean result = shortplayController.gdtLoginReportClickIdLock(clickId, TEST_APP_ID, userId);
            assertTrue(result, "第" + i + "个clickId应该成功（不限制数量）");
        }
    }

    /**
     * 测试14：配置格式测试 - 混合配置
     * 前置条件：配置包含限制和不限制的混合格式
     * 预期结果：正确解析目标appId的配置
     */
    @Test
    void testMixedConfig() {
        // 设置混合配置：app1-5,testApp001,app2-10
        updateConfigValue("shortplay.gdt.clickId.loginReport.appid",
                "app1-5," + TEST_APP_ID + ",app2-10");

        String userId = "mixedConfigUser";

        // 上报10个不同的clickId，都应该成功（testApp001不限制）
        for (int i = 0; i < 10; i++) {
            String clickId = "mixed" + String.format("%03d", i);
            boolean result = shortplayController.gdtLoginReportClickIdLock(clickId, TEST_APP_ID, userId);
            assertTrue(result, "第" + i + "个clickId应该成功（混合配置中不限制）");
        }
    }

    /**
     * 测试15：配置不存在测试
     * 前置条件：appId不在配置中
     * 预期结果：允许通过
     */
    @Test
    void testConfigNotExists() {
        // 设置不包含测试appId的配置
        updateConfigValue("shortplay.gdt.clickId.loginReport.appid", "otherApp-5,anotherApp-10");

        String userId = "notExistsUser";

        // 上报clickId应该成功（appId不在配置中）
        boolean result = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, userId);
        assertTrue(result, "appId不在配置中应该允许通过");
    }

    /**
     * 测试16：配置解析异常测试
     * 前置条件：配置包含无效的数字格式
     * 预期结果：异常处理，允许通过
     */
    @Test
    void testConfigParseException() {
        // 设置包含无效数字的配置
        updateConfigValue("shortplay.gdt.clickId.loginReport.appid", TEST_APP_ID + "-abc");

        String userId = "parseExceptionUser";

        // 上报clickId应该成功（配置解析异常时允许通过）
        boolean result = shortplayController.gdtLoginReportClickIdLock(TEST_CLICK_ID_1, TEST_APP_ID, userId);
        assertTrue(result, "配置解析异常应该允许通过");
    }
}

/*
 * 测试运行说明：
 *
 * 1. 环境要求：
 *    - Redis服务器运行在localhost:6379
 *    - 配置中心需要设置以下配置：
 *      shortplay.gdt.clickId.loginReport.appid = testApp001-3
 *      shortplay.dy.clickId.loginReport.appid = testApp001-3
 *
 * 2. 测试数据：
 *    - 测试会自动清理Redis中的测试数据
 *    - 使用testApp001作为测试appId
 *    - 使用user123、user456等作为测试userId
 *
 * 3. 测试覆盖：
 *    - 基本功能：首次上报、重复提交、达到上限
 *    - 边界条件：刚好达到限制、限制为1
 *    - 用户隔离：不同用户互不影响
 *    - 平台隔离：广点通和抖音使用不同配置
 *    - 性能测试：大量clickId上报的性能
 *    - 精确匹配：字符串匹配的准确性
 *
 * 4. 注意事项：
 *    - 某些测试需要特定的配置，请根据注释调整配置中心的设置
 *    - 性能测试的时间阈值可能需要根据实际环境调整
 *    - 如果Redis连接失败，相关测试会失败
 */
