package com.ruoyi.web.controller.playlet.nuohe;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.service.nuohe.impl.NuoheServiceImpl;
import com.ruoyi.web.controller.BaseControllerTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

class AgtDataSupplierSnapshotNuoheControllerTest extends BaseControllerTest {

    @Autowired
    private NuoheServiceImpl nuoheService;

    @Test
    void getOrder() {
        Date date = new Date();
        DateTime start = DateUtil.beginOfDay(date);
        DateTime end = DateUtil.endOfDay(date);
        nuoheService.getOrderInfoSaveToDB(start, end);
    }

}