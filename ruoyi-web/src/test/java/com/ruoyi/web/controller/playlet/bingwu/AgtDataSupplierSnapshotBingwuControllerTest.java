package com.ruoyi.web.controller.playlet.bingwu;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.service.bingwu.impl.BingwuServiceImpl;
import com.ruoyi.web.controller.BaseControllerTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

class AgtDataSupplierSnapshotBingwuControllerTest extends BaseControllerTest {


    @Autowired
    private BingwuServiceImpl bingwuServiceImpl;

    @Test
    void getOrderInfoSaveToDB() {

        for (int i = 0; i < 1; i++) {
            Date cur = DateUtil.offsetDay(new Date(), -i);
            DateTime start = DateUtil.beginOfDay(cur);
            DateTime end = DateUtil.endOfDay(cur);
            bingwuServiceImpl.getOrderInfoSaveToDB(start, end);
        }

    }


}