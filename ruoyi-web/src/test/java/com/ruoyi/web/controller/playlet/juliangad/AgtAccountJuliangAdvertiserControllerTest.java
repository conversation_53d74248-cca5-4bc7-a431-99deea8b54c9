package com.ruoyi.web.controller.playlet.juliangad;


import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.mapper.juliangad.AgtAccountJuliangAdvertiserSelfMapper;
import com.ruoyi.system.service.juliang.impl.AgtAccountJuliangServiceSelfImpl;
import com.ruoyi.system.service.juliangad.AgtAccountJuliangAdvertiserSelfService;
import com.ruoyi.web.controller.BaseControllerTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

class AgtAccountJuliangAdvertiserControllerTest extends BaseControllerTest {


    @Autowired
    private AgtAccountJuliangServiceSelfImpl agtAccountJuliangServiceSelf;
    @Autowired
    private AgtAccountJuliangAdvertiserSelfService agtAccountJuliangAdvertiserSelfService;
    @Autowired
    private AgtAccountJuliangAdvertiserSelfMapper agtAccountJuliangAdvertiserSelfMapper;

    @Test
    void testBatchInsert() {
        AgtAccountJuliangAdvertiserEntity entity = new AgtAccountJuliangAdvertiserEntity();
        entity.setAdvertiserId(123L);
        entity.setAccountId(12322L);
        entity.setPlayletName("test");
        entity.setPlatformDyWx(1L);

        ArrayList<AgtAccountJuliangAdvertiserEntity> list = new ArrayList<>();
        list.add(entity);

        agtAccountJuliangAdvertiserSelfMapper.batchInsertOrUpdate(list);
    }
}