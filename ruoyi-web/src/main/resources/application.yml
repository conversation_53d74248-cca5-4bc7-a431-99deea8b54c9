# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.7
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: ${user.home}/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8795
  servlet:
    # 应用的访问路径
    context-path: /api
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 8000
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 1000

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# MyBatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
#  configuration: # 此配置是为了在本地调试过程中，打印出MyBatis SQL语句，方便调试
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

juhe:
  key:
    ipAnalysis: c636e52f361cbe1d4f37d0bc1535b2d2

# 毫秒科技IP解析
haomiao:
  ip:
    secretId: AKIDMYWT8Xa3FHyaaoha7Wgx4pS9pivcvruag8z4
    secretKey: 8lajp3o309luef6g2xZsaFcvwcYk5zb30yET5o3i

nuohe:
  accessKey: f4b21a24486f4c5d8f1db87c541f4fdc

playlet:
  profile: ${user.home}/uploadPath

wx:
  miniapp:
    appid: wx523011ef7aed165e
    secret: 9ef853a0c3c3ea360846d89fb372d569
    msgDataFormat: JSON

# 定时任务开启状态
quartz:
  enabled: true

# oss配置
oss:
  url: https://common.acthao.com/
  endpoint: http://oss-cn-hangzhou-internal.aliyuncs.com
  accessId: LTAI5tLGKudCmLbqTnSgQCKz
  accessSecret: ******************************
  bucket: nh-common

stat:
  send: true

# 抖音第三方小程序配置
dy:
  component:
    appId: tt246c52d1bb4680c8
    secret: 102512431b366850925ec6a6c90b9168a7a8dcf4

# 接口耗时告警
time:
  profile:
    threshold: 1000

aes:
  secret: Ka1y0ma34R7N2qAb