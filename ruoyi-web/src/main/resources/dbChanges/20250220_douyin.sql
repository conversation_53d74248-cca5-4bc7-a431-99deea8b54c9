--组织表和账户表都复用之前的，需要把原来的```定时任务```开启




CREATE TABLE `tb_ocean_engine_project` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `advertiser_id` bigint unsigned NOT NULL COMMENT '广告账户id',
  `project_id` bigint unsigned NOT NULL COMMENT '项目 ID',
  `app_id` varchar(255) DEFAULT '' COMMENT '抖小程序 ID',
  `micro_promotion_type` varchar(50) DEFAULT NULL COMMENT '小程序类型，允许值： BYTE_APP 字节小程序、BYTE_GAME字节小游戏、WECHAT_APP 微信小程序、WECHAT_GAME 微信小游戏、AWEME抖音号',
  `project_name` varchar(255)  DEFAULT NULL COMMENT '项目名称',
  `project_create_time` datetime DEFAULT NULL COMMENT '项目创建时间，格式yyyy-MM-dd HH:mm:ss',
  `project_modify_time` datetime DEFAULT NULL COMMENT '项目更新时间，格式yyyy-MM-dd HH:mm:ss',
  `project_status` varchar(50)  DEFAULT NULL COMMENT '项目状态',
  `status_first` varchar(50)  DEFAULT NULL COMMENT '项目一级状态',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_project_id` (`project_id`) USING BTREE COMMENT '项目 id 唯一键'
) ENGINE=InnoDB DEFAULT COMMENT='巨量广告账户项目表';


CREATE TABLE `tb_ocean_engine_project_date_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `advertiser_id` varchar(255) NOT NULL COMMENT '广告主id',
  `project_id` bigint unsigned NOT NULL COMMENT '项目id',
  `cur_date` date NOT NULL COMMENT '日期',
  `stat_cost` decimal(10,2) DEFAULT NULL COMMENT '巨量消耗',
  `cash_cost` decimal(10,2) DEFAULT NULL COMMENT '投流现金消耗，现金消耗=账面消耗/（1+返点）',
  `show_cnt` int DEFAULT NULL COMMENT '展示数',
  `cpm_platform` decimal(10,2) DEFAULT NULL COMMENT 'cpm',
  `click_cnt` int DEFAULT NULL COMMENT '点击数',
  `ctr` decimal(10,2) DEFAULT NULL COMMENT '点击率',
  `cpc_platform` decimal(10,2) DEFAULT NULL COMMENT '平均点击单价(元)',
  `active` int DEFAULT NULL COMMENT '激活数',
  `active_cost` decimal(10,2) DEFAULT NULL COMMENT '激活成本',
  `active_rate` decimal(10,2) DEFAULT NULL COMMENT '激活率',
  `game_addiction` int DEFAULT NULL COMMENT '关键行为数',
  `game_addiction_cost` decimal(10,2) DEFAULT NULL COMMENT '关键行为成本',
  `game_addiction_rate` decimal(10,2) DEFAULT NULL COMMENT '关键行为率',
  `convert_cnt` int DEFAULT NULL COMMENT '转化数',
  `conversion_cost` decimal(10,2) DEFAULT NULL COMMENT '平均转化成本',
  `conversion_rate` decimal(10,2) DEFAULT NULL COMMENT '转化率',
  `attribution_micro_game_0d_ltv` decimal(10,2) DEFAULT NULL COMMENT '小程序/小游戏当日LTV',
  `attribution_micro_game_0d_roi` decimal(10,2) DEFAULT NULL COMMENT '当日广告变现ROI',
  `attribution_micro_game_iaap_ltv_1day` decimal(10,2) DEFAULT NULL COMMENT '当日变现金额(激活时间)',
  `stat_attribution_micro_game_24h_amount` decimal(10,2) DEFAULT NULL COMMENT '24小时变现金额（激活时间）',
  `attribution_micro_game_iaap_ltv_7d` decimal(10,2) DEFAULT NULL COMMENT '7日综合流水（激活时间）',
  `attribution_micro_game_iaap_ltv_7d_roi` decimal(10,2) DEFAULT NULL COMMENT '7日综合ROI（激活时间）',
  `stat_attribution_micro_game_7d_amount` decimal(10,2) DEFAULT NULL COMMENT '7日变现金额（激活时间）',
  `account_id` bigint DEFAULT NULL COMMENT '组织 id',
  `account_name_co` varchar(255)  DEFAULT NULL COMMENT '主体名称',
  `account_agent` varchar(255) DEFAULT NULL COMMENT '代理商',
  `advertiser_name` varchar(255)  DEFAULT NULL COMMENT '账户名',
  `project_name` varchar(255)  DEFAULT NULL COMMENT '项目名',
  `user_nickname` varchar(255) DEFAULT NULL COMMENT '投手',
  `app_id` varchar(64)  NOT NULL default '' COMMENT '小程序',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_data_project` (`project_id`,`cur_date`) USING BTREE,
  KEY `idx_advertiser_id` (`advertiser_id`) USING BTREE,
  KEY `idx_project_id` (`project_id`) USING BTREE,
  KEY `idx_app_date` (`app_id`, `cur_date`) USING BTREE,
  KEY `idx_cur_date` (`cur_date`) USING BTREE
) ENGINE=InnoDB DEFAULT COMMENT='巨量广告账号项目维度日数据表';


ALTER TABLE `nh_playlet`.`tb_agt_account_juliang`
ADD COLUMN `account_agent` varchar(255) NULL DEFAULT '侠客行' COMMENT '代理商' AFTER `account_name_co`,
ADD COLUMN `commission` decimal(10, 3) NULL DEFAULT '0.05' COMMENT '返点' AFTER `account_agent`,
ADD COLUMN `advertiser_count` int NULL COMMENT '广告账户数量' AFTER `commission`;














