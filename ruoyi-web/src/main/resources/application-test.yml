# 数据源配置
spring:
  # redis 配置
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: xyRedis123
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 50
        # 连接池的最大数据库连接数
        max-active: 50
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ***************************************************************************************************************************************************************************************************************
        username: nh_test_acount
        password: nH_tEst_2023
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 10
      # 最小连接池数量
      minIdle: 20
      # 最大连接池数量
      maxActive: 50
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: nh_test_acount
        login-password: nH_tEst_2023
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 3000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true


# 日志配置
logging:
  level:
    com.ruoyi: info
    org.springframework: warn

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 1440

# 对接积分时代参数
jfsd:
  url: http://test.openapi.jf180.cn/api/merchant/chargeorder/notify/nuoheuse
  signKey: AD^OGaY6B%hAbIu2rmYueza4KHkgDcoM

# 巨量开发者后台应用
oceanengine:
  developer:
    app_id: 1802462235521379
    secret: 580a2c0d312566404f46c4a7dc982352f1b4d92c
  spi:
    secret: 7134d04962e746c080392c10a7840ee0
    id: 1806064908444747


youhe:
  wechat_id: 196
  app_key: 4526791196
  secret: 43466c4a71bfe865906c65da9a9863d4
  username: nhbojc0531
  password: Nhbojc@0531
  wechat_id2: 122
  username2: bjjc0703
  password2: Bjjc@0703
  host: https://open-playlet.64xiaoshuo.cn

chumo:
  host: https://tsd.api.cmappgogo.cn
  secret_key: 05fcc712ccc63694c3499dda090e6395

lianfan:
  host: https://oper.lsttonline.com/prod-api
  apiId: WgfrXftSlcBZ
  apiSecret: upOt1Slg4PQV4l2DcgUlPyIsQ2feakXR8XM9

bingwu:
  key: akXSSjEnCWzmbks23n

fanqie:
  distributorIdOrigin: 1806172584541347
  secretKey: HANGJx78bZdRRnP0ODzdtg0MZL46zG64

dianzhong:
  clientId: 10007931
  token: 2bArpcjo5gPEsSQzJF


# 钉钉机器人
ding:
  webhook:
    agtRegAlert: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1da95122-06eb-45bf-8c68-8295da5a1689
    douyinAlert: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b59683bf-ac7e-410f-bfcd-13d621f7713d
    douyinDataAlert: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c3205725-61b2-4fed-b37a-e92af997f423
    qywxAlert: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=435c8905-1c9b-456d-8b6b-c203e890d350
    adsReqAlert: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f9b8c130-9c18-4a37-b3a1-ac1f448831ce
    dyVideoAlert: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3a628635-59d3-4cc0-855e-061e152a24da
    dyVideoAlertTech: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6e02c967-123f-4c4b-b3bc-68a389e53119
    roiAlert: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c2ebc6b4-ad31-4b40-9173-2088fa6c3549
# 支付宝小程序
alipaymp:
  configs:
    - appId: 2021002175686097
      privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCCH7F5zvD/U02hR+T9MQ/quyGHqRJL3j/zvpfs7D9vVtBvjuoI8owCY0jR+1+mI2LGfZoMX8ZMU+6jDAIyoegEwRy6sPbPQrz+PIf+KGFFAaI+u29Hlx2GJMRcS9iz+sxkGe1iEJOekknu9wKJ0xieNTMz/z5AdEnYWZ5aXnXJXvPrGwpmPWbdNZhJ9Pfcf6x27nU8rkY1gHdhGN6j7SdxGMgLnmBFwaCobiK3ekJnNEE3CjKLMH2K5dIncN6o7kUW/fWmOuucpryTeBqz+fnaoOK8ErDyoZq3HIGzxhHoG7dd5i0x+ULiiZ/EzLbxrYQm/V9Q0fXnX/d5HiQGQ7vFAgMBAAECggEAAVQLfr/TC/gwua2/M6p1jCLe48ChFC8vnfBbIqsCtsM/G3RtBSxcq4WR77rOLib8UiRkFy4/GvZcYEO2HggzEyFZhrpZuqHmxf2eMcT9wTOUEWneZ++t+HmVm4+WpbewsHDnUHH039CEerEA0KFcBflrDWPUodnOCyxXBnsiXqzanmy/vA9g1wZ1Y4BW/HptktTMNgBGJhib+TGB6F9lxZjdkjBedqov0Ocx15vhGjVBTDmpql0+so9KKGTfTWw9fRhhJeVEsCiTL2Io8fklpMr7tV6D/T6L9YDUl0gvDJ3O3OYFcEfXrTMlKA/RpYpkooA/LA9XUYXbHycTtyMr/QKBgQDqmn9mZVY5Hc4UdzDYlcLv9vmTxjRc4QPkxfTVp3BTJmor3+CCYv0oCJLYaW2Gpvc8Li4xPfjk92zs9UkmSEMcVAxYPwqtCAFr0mz+LPED+96DSl44cp9GH7Bme6s8t9yfwQyjiJKAdEmTD6a71DC2mFjBBsZ5uMx0siuZDknilwKBgQCN/dABECqQs89wtKPKxycYnzFTMKV6N385hYcbBGgcO+WmGWWa6EIuvI1TLmU9DowJcVdDfdlwFrrSLxUWBD1elpsKWyqp8eoQGbBLizV+4rqHGyNOHdfG/OhvXIs1UA/XcB9vhnjCzBH+eFCRXP51M/9vGsf99LpLq7PWRVEMAwKBgQCSAXM2c+z0gRMnc4KH2LTIiWmMSXd/Ml8s+vJEIf/rD+ZVxfBA1YB+cM+LNhn/ClymLIt9A/ep8D7vPjy3QZPaxogkR5/KTkgsJXrFuYyKES/Lf/YhpVxw1PoA3giVMkxxdED8advbbAP6LWx0SzvB+BCMmT3etV8xZU1y5QygowKBgHjWzpR5C6CGRj4mnVZPUohzwmiH9+whGtb7OlXV1DxjBTfsWToR+3EG6kcmLuZdYPQQTzGTAt+PmIKTqyBHj2rWRS/1EkP7KzV0h0JjQK2xiwi8IbmzUBV62t0bJyXB8xlnxHkHOfKd9ZpZDkgHmk5oaDRH2HfaNEl94NizuRu9AoGBAKP2HxA8sLrCAn5/+hQpk678XLM/YulFCdP7qXWztF2ar+WUM7QW9GolpVJ7Kl4AzGHdk7U2bjhq6ModA/fSNGqCoEdVOVZZ54dKFBN9wwkbXMVpLaqKSne6M6mzVzrQ1d+gTrz87k7vFrNGsONuS8p0KAJw4UenXoZrCSVS7GRp
      alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo35OzbXMkq5Q+juJfc6pUJKrLzNZIM0yzHLqn8VHQc8ojk/D7ljJn8ytD3Rvo1bXdK8btc288oXkrqlUpnO5KO2Tt7lRiB0sitCtZgPYkeDaFTy7AbilWj2TIe5Tb5wCGP053MavxxmwUUSPWzbbW6Fpc0uI5mQdTAlEZE+P9vH0TLmdZ15FaNGK609FlellM9d+Z1pna6eSIhD/uWcLQXN3AxN+ZBcJRbUWgPhAR9HtVrNYmr4FCoLOYz46buwpEyzIWHI36FPSSeAuvcr18myOuP5GprDJ9eeO5xvETd+2HG2sACx/5KCxC9lQ8+b0uik/uvCPCKHqXqZo3n25MwIDAQAB
    - appId: 2021004186680545
      privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCwjPiYgKHrv1OP8Fk9vygoBzLTjEp9kjdI2LJOm5RtoJChBSsUaYRy8RliMjSJ02EfqVm/CL8htgcb8k4jqsS2ZnAiH7VT2Kxepp0IZCCw3bLjF2Y5EiGiM1AH9z4jzsMTbX2UXYOabxx/ur/fNtVttcGdsD9pJONaXdszZYJO5FLQLGSfls/uYXr/8RF67xBGeLjfx8hmDy7jP5GNWxvhQ5dVJnJ0+SCvARPX5MeoVXjSaa/O7It1/my4lL7ll+SoYLvN1oJPO536fhuvyMEEJAlaT8X4LxovWORYQorW+MOPwH+ku95Kfx3GCuAsBBGyYNIvHXYgybazKQsVzmAfAgMBAAECggEBAKwz5CpDlSvvwx6rTTAn6pAYbV/f/6w5WMkp68k2gPDo4wjblKhlPzYpqhuYYpxoW9xY/************************************+avZ1OPODrET5/4ssACTU751lZ1vj5fdATHCaIgUhPzVPucg8w946u3ujYjQ7H++bnd4Ga331u0oCVSH8h7h5XUueTsM5wSXGlAqTP8v8W5kAGj5+H/YgeFvCHS0DzzcSqedYUxrKJGXVtCV8rbxP8JnVLQRkZpj19VzwqnC8ndAZr8vru4qqH1V0JqZJfIjlEk20I1GgXkuRk18MYwa9DMH8Ul1ds0nkc1jeiEbKzqEEkCgYEA+QqSw75LiEKlW+Bqw5NcicfOm1rOLnyTQEfr0p0IdStwt7c9437w4hRDf0xNcT7NFiveCW1LpEugRKvtOgC6yPFW52KZZD63fp0Ou0yBTipuu6gqnBFx+VYanKVfNWrv72B8olOIwxQNUr2Y9Gz3yaKDSeEiiOBuMivyUnhsEp0CgYEAtXvcv7b/zkXjIdDif7wu05bE5bw3s7ZvNPkKQiaWOLmg73G673k7Ipm8TW7DAnaGoG4nIz9vdgLICBgcItEX7floaURppds3Graqg4749NGd+PjaRrKNVoOfJQMo9gdH07hFwgQwIHJ+Gp3AebI5i7G6zHNPqrIt2auarTg/UusCgYEA7xbNjAr3bNu+8RPkq9qfrs8adV+aLlr+62mg51RHOGX6EwZi7jOCgym9G0tf1eba8fwvcJFzW0oXlZYRa+wjZqL8MawhY3Iqq+2QpRRv12E5agOq1KaS6zNU3SqO2zPNId1aCmV0GQT5PYQ0ggTTZssUjCsOAqakZJp4S+C8sKkCgYANgsYhY69RnPRW3SUvm0PlLrFD5SpEb82BpRS1bgf6+x7XFKF1GJJldgqFvTw5X7eQFANH5X4XzAc2Sw01LlnYtpumUpiG53YHwBebqbYxC7X2BhjB8KMDENN1PvH6tvoK8goHcqZelAd9VccxiCoukzGz7NW+gHtXXCYsxSY5twKBgAduAZp4csxGEE117G7WG1iOEHDX1Fxu3R3vQ7JbI9R0di7mYmO7vgteFdIn/wRwgiqiO5MVCmYlw/V6xlX29qeDbAhKULiPFCYUHKDO/SnRzbOplqIooH7nen/vE0VD0YvojLaxCmKLR3as1TfzVSKbZv+z6/zFbho2UD1FJe2W
      alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo35OzbXMkq5Q+juJfc6pUJKrLzNZIM0yzHLqn8VHQc8ojk/D7ljJn8ytD3Rvo1bXdK8btc288oXkrqlUpnO5KO2Tt7lRiB0sitCtZgPYkeDaFTy7AbilWj2TIe5Tb5wCGP053MavxxmwUUSPWzbbW6Fpc0uI5mQdTAlEZE+P9vH0TLmdZ15FaNGK609FlellM9d+Z1pna6eSIhD/uWcLQXN3AxN+ZBcJRbUWgPhAR9HtVrNYmr4FCoLOYz46buwpEyzIWHI36FPSSeAuvcr18myOuP5GprDJ9eeO5xvETd+2HG2sACx/5KCxC9lQ8+b0uik/uvCPCKHqXqZo3n25MwIDAQAB
    - appId: 2021004187653079
      privateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCDETc1VB8D2ToPZ/Jd9nO3Npm/j0rTOW+n4AY7c7omJJpHV+jzI4+8QrOZR+PlJ5fZlm1DkNZNIoD1Cy+ce/Ww2WqzN9ir0bNWpUQvmK8yuWvEklyWa7tY7JLmVFRiiFRagFeJ51/nrgnrBwC2CEknjFIwjsh0iUBL0ddCGih0UQ/S4lZiLO2hp/3GSavv8vf0VHANN1xaZD4lMr80wrH+GdC7hiVAvsQcd98T6DyJ7eQx6T08pCD9QqXtCVTT8vzayuJnALJCyB2cQrGqP67vREpmk5H76cmD72SBwYL7UKpIiN1WWJHY1xf+UzjHsQb3V9ObY4XmFvp2xM12c6hTAgMBAAECggEAN4DN2VaDP9T4wldpiqL4Q8jqePWMYUB8aSH0Ya7TUJ3Fe5rdukD/+GBiv/Fyr7MBmhIauTcXmViYNa0D/4d69uZLShPHPJO0F2grJODKqHdfDMJSVRzQHIsqdkGV7pf+6MhaybpiobDQ4Gd5xJxjpkzE7W05tCf/Q1xchNCg+whN4Z+gAjBGkZbtX5O+2zzaN0ZBBPkzKqQPpsTEShkbicmfXWOGGHuYZ10jpNJOa+OpWh3lLLpgBG+yxv2ltwqWoOg6FCM0gqtTanAQBpH9qtqSl6FsYbXG9qv3led92hE0OYdOXnqEkrjTfJ3eJwDCqKZt8pgVCILeV2bgVkQlcQKBgQDk55zhyzPA4FIpojhEW5Zy2SI6o8ar4djuPOOKNSpryz2TO/TXCE4RURJsO9Vd56PNws7DuczD/iwvqoARyfkW6XcnPBNiLDKFCiJ6LSNqhV1MV0URv3dtaRTqhA/T9s959NhvTcTP+9OtmDkyK6EA04ElsJv1BQNQt6OKvcU2mQKBgQCSlOG5+eHSQe9UU0kkgqT0ILxNRdnhkqpEFWTBGOSp1J1poH1p5KG56n7QrrZ1iAJBn7Z7OJcjJy3JvfYvLEAnKaISl3AuIo8VOFB5QGozV1AzKWuYEf7Plo0LxZnQSMNvOrk4sRh79b1kgB60YtO6c5ASVPM8o1lj72X3EB9lywKBgCqToiylfUTc9Ktlzw138639EU7S87s40P7POIuWhAN01Ek0l7//fXXfHIwhPPiwg1lcaNUjFCFIf7OGBThJ1xkZ593WYMyb5/GzBuKuzq2bjNUMTn6UnIQvTYKpecA1YjYPAssBLiB5eNALRmg8kT/0F0uJZBjZC9rRrYu67JnRAoGAemUM0qC/mjRqJgxhP7ZDDIIlGZTg6MTxnWYLul0R2mlpg2B6OCirjjUq7Q9l1V/bGnbFBcDsGnNFfXY8EYmui8Tsyv9esXcz6HEcaRKLWxFmK0JD0Df+f9HjNlgqJkwLvF3ZeWgO3LsGCNweR1taFg8W1ufOLuzq4FJu3sy8mt8CgYARST+XUExTTBuyFSKasB2IVusTFLJ1CuZHWJysoSfb+kDaar82IL3NbOEump35H8LeVw9hspAYjSQUAy6/7LszTD5dQIg5fdhmo2yLLoJkDogDtKMOrfMWoVdYebKj/m4n9QukQ/jXVhkJ8rGBFL1ei+BPHRaiqKC8slcVmwMH1w==
      alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnNw005eASXYeN+yhSKIqqnzQbNI7YaWAundK5d3nEso3qDGGs7mAyMX99m8wt2G1q+qf+1zS9XoopJVALu31M6xbkoObzdd6tdmaGMIGNtl89a5RTcpLWiFPu2BBTrafByuY+tYRoNVdHLR6zsmJnAlhRaW0ZWsj32xUeliqKR81P2ddb8kjgI3MTXY4o5/WAiexcORqzdJwyI7gK9FG0+nFRRqMtt/2z5JOpuPyvuamK4wC2uOpaiyQrTh6IE6Lkd4ly9ONBYzvaMuA/zY4s0GrycuFmUyieVYUfAC0c4pmG69hq/BEG5NuYVcDyDZn2zS2zrP6O/LquRu0vrwWyQIDAQAB


server:
  domain: https://tq-test.nuohe.com.cn/

# 广点通素材同步
gdt:
  material:
    sync: true