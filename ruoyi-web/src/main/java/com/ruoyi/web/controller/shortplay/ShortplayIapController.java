package com.ruoyi.web.controller.shortplay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.exception.BizRuntimeException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.bo.shortplay.VirtualPaymentSignDataBo;
import com.ruoyi.system.entity.shortplay.app.ShortplayWxAppEntity;
import com.ruoyi.system.entity.shortplay.iap.ShortplayWxPayOrderEntity;
import com.ruoyi.system.entity.shortplay.record.ShortplayTvUnlockRecordEntity;
import com.ruoyi.system.entity.shortplay.tv.ShortplayTvEntity;
import com.ruoyi.system.entity.shortplay.user.ShortplayUserEntity;
import com.ruoyi.system.req.shortplay.iap.PayOrderReq;
import com.ruoyi.system.req.shortplay.iap.VirtualPaymentReq;
import com.ruoyi.system.service.shortplay.app.ShortplayWxAppService;
import com.ruoyi.system.service.shortplay.iap.ShortplayWxPayOrderService;
import com.ruoyi.system.service.shortplay.record.ShortplayTvUnlockRecordService;
import com.ruoyi.system.service.shortplay.tf.ShortplayDyTfurlService;
import com.ruoyi.system.service.shortplay.tv.ShortplayTvService;
import com.ruoyi.system.service.shortplay.user.ShortplayUserService;
import com.ruoyi.system.service.wxpay.WeiXinPayService;
import com.ruoyi.system.vo.shortplay.iap.PayOrderVo;
import com.ruoyi.system.vo.shortplay.iap.VirtualPaymentVo;
import com.ruoyi.system.vo.wxpay.WxPayVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 短剧小程序IAP Controller
 *
 * <AUTHOR>
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/shortplay/iap")
public class ShortplayIapController {

    @Autowired
    private ShortplayWxPayOrderService shortplayWxPayOrderService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private ShortplayWxAppService shortplayWxAppService;

    @Autowired
    private ShortplayUserService shortplayUserService;

    @Autowired
    private WeiXinPayService weiXinPayService;

    @Autowired
    private ShortplayDyTfurlService shortplayDyTfurlService;

    @Autowired
    private ShortplayTvUnlockRecordService shortplayTvUnlockRecordService;

    @Autowired
    private ShortplayTvService shortplayTvService;
    @Value("${server.domain}")
    public String serverDomain;
    /**
     * 虚拟支付下单
     */
    @PostMapping("requestVirtualPayment")
    public Result<VirtualPaymentVo> requestVirtualPayment(@RequestBody VirtualPaymentReq req, HttpServletRequest request) {
        RedisLock lock = redisAtomicClient.getLock(RedisKeyFactory.K011.join(req.getUserId()), 3);
        if (lock == null) {
            return ResultBuilder.fail("下单失败，请重试");
        }

        ShortplayUserEntity user = shortplayUserService.selectById(req.getUserId());
        if (null == user) {
            return ResultBuilder.fail("用户不存在");
        }
        ShortplayWxAppEntity app = shortplayWxAppService.selectByAppId(user.getAppId());
        if (null == app) {
            return ResultBuilder.fail("未查询到appKey");
        }
        Integer payAmount = shortplayDyTfurlService.getPayAmount(user.getTfid());
        if (null == payAmount || payAmount <= 0) {
            return ResultBuilder.fail("未配置支付金额");
        }

        String outTradeNo = generateOutTradeNo(req.getUserId());

        VirtualPaymentSignDataBo signData = new VirtualPaymentSignDataBo();
        signData.setOfferId(app.getOfferId());
        signData.setBuyQuantity(payAmount);
        signData.setEnv(0);
        signData.setCurrencyType("CNY");
        signData.setOutTradeNo(outTradeNo);
        signData.setAttach("");
        String signDataStr = JSON.toJSONString(signData);

        String sessionKey = shortplayUserService.getSessionKey(user.getWxOpenid(), req.getCode(), user.getAppId());

        VirtualPaymentVo result = new VirtualPaymentVo();
        result.setSignData(signDataStr);
        result.setMode("short_series_coin");
        result.setPaySig(calcPaySig(app.getAppKey(), signDataStr));
        result.setSignature(calSignature(sessionKey, signDataStr));

        // 新增订单记录
        ShortplayWxPayOrderEntity order = new ShortplayWxPayOrderEntity();
        order.setOrderType(1);
        order.setOutTradeNo(outTradeNo);
        order.setUserId(req.getUserId());
        order.setOpenId(user.getWxOpenid());
        order.setAppId(app.getAppId());
        order.setMchId(app.getMchId());
        order.setDescription("短剧购买"); // TODO
        order.setTotalFee(payAmount);
        order.setIp(IpUtils.getIpAddr(request));
        order.setReferer(request.getHeader("referer"));
        order.setUserAgent(request.getHeader("User-Agent"));
        order.setUnlockTvId(req.getUnlockTvId());
        shortplayWxPayOrderService.insert(order);
        return ResultBuilder.success(result);
    }

    /**
     * 小程序支付
     */
    @CrossOrigin
    @PostMapping(value = "/requestPayment")
    public Result<WxPayVo> requestPayment(@RequestBody VirtualPaymentReq req, HttpServletRequest request) {
        RedisLock lock = redisAtomicClient.getLock(RedisKeyFactory.K011.join(req.getUserId()), 3);
        if (lock == null) {
            return ResultBuilder.fail("下单失败，请重试");
        }

        ShortplayUserEntity user = shortplayUserService.selectById(req.getUserId());
        if (null == user) {
            return ResultBuilder.fail("用户不存在");
        }
        ShortplayWxAppEntity app = shortplayWxAppService.selectByAppId(user.getAppId());
        if (null == app) {
            return ResultBuilder.fail("未查询到appKey");
        }
        Integer payAmount = shortplayDyTfurlService.getPayAmount(user.getTfid());
        if (null == payAmount || payAmount <= 0) {
            return ResultBuilder.fail("未配置支付金额");
        }

        String outTradeNo = generateOutTradeNo(req.getUserId());

        // 新增订单记录
        ShortplayWxPayOrderEntity order = new ShortplayWxPayOrderEntity();
        order.setOrderType(2);
        order.setOutTradeNo(outTradeNo);
        order.setUserId(req.getUserId());
        order.setOpenId(user.getWxOpenid());
        order.setAppId(app.getAppId());
        order.setMchId(app.getMchId());
        order.setDescription("短剧购买"); // TODO
        order.setTotalFee(payAmount);
        order.setIp(IpUtils.getIpAddr(request));
        order.setReferer(request.getHeader("referer"));
        order.setUserAgent(request.getHeader("User-Agent"));
        order.setUnlockTvId(req.getUnlockTvId());
        shortplayWxPayOrderService.insert(order);

        WxPayService wxPayService = weiXinPayService.createWxPayService(user.getAppId(), app);
        WxPayUnifiedOrderV3Request orderRequest = new WxPayUnifiedOrderV3Request();
        WxPayUnifiedOrderV3Request.Amount amountReq = new WxPayUnifiedOrderV3Request.Amount();
        amountReq.setTotal(payAmount);
        orderRequest.setAmount(amountReq);
        orderRequest.setOutTradeNo(outTradeNo);
        orderRequest.setDescription("短剧购买");//todo
        orderRequest.setNotifyUrl(serverDomain + "api/shortplay/iap/callback/" + user.getAppId());
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(user.getWxOpenid());
        orderRequest.setPayer(payer);
        try {
            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, orderRequest);
            return ResultBuilder.success(BeanUtil.copyProperties(jsapiResult, WxPayVo.class));
        } catch (WxPayException e) {
            log.error("微小程序支付异常,appId:{},mchId:{},outTradeNo:{},e:", user.getAppId(), app.getMchId(), outTradeNo, e);
            throw new BizRuntimeException(ErrorCode.WX_PAY_FAIL);
        }
    }

    /**
     * 虚拟支付回调验证
     */
    @GetMapping(value = "/virtualpay/callback")
    public String virtualPayCallbackVerify(HttpServletRequest request) {
        return StringUtils.defaultString(request.getParameter("echostr"));
    }

    /**
     * 虚拟支付回调
     * {
     * "ToUserName": "gh_47d0f50a257c",
     * "FromUserName": "o0hYl7fHI1LEQ7eOEL7HfWvy0Cgk",
     * "CreateTime": 1726212323,
     * "MsgType": "event",
     * "Event": "xpay_coin_pay_notify",
     * "OpenId": "o0hYl7fHI1LEQ7eOEL7HfWvy0Cgk",
     * "OutTradeNo": "20240913152511121154754",
     * "WeChatPayInfo": {
     * "MchOrderNo": "VPO240913152512054391859",
     * "TransactionId": "4200002347202409131995443011",
     * "PaidTime": 1726212322
     * },
     * "Env": 0,
     * "CoinInfo": {
     * "Quantity": 1,
     * "OrigPrice": 1,
     * "ActualPrice": 1,
     * "Attach": ""
     * },
     * "RetryTimes": 0
     * }
     */
    @PostMapping(value = "/virtualpay/callback")
    public String virtualPayCallback(@RequestBody JSONObject notifyData, HttpServletRequest request) {
        log.info("虚拟支付回调通知，notifyData={}", notifyData);

        if (!StringUtils.contains(notifyData.getString("Event"), "pay")) {
            return "";
        }

        // 解锁短剧
        ShortplayWxPayOrderEntity wxPayOrder = shortplayWxPayOrderService.selectByOutTradeNo(notifyData.getString("OutTradeNo"));
        if (null == wxPayOrder || null == wxPayOrder.getUnlockTvId()) {
            return "";
        }
        ShortplayTvEntity tv = shortplayTvService.selectById(wxPayOrder.getUnlockTvId());

        ShortplayTvUnlockRecordEntity record = new ShortplayTvUnlockRecordEntity();
        record.setUserId(wxPayOrder.getUserId());
        record.setTvId(wxPayOrder.getUnlockTvId());
        record.setTvTbId(tv.getTbId());
        shortplayTvUnlockRecordService.insert(record);

        // 更新订单状态
        ShortplayWxPayOrderEntity updateOrder = new ShortplayWxPayOrderEntity();
        updateOrder.setId(wxPayOrder.getId());
        updateOrder.setPayStatus(1);

        JSONObject payInfo = notifyData.getJSONObject("WeChatPayInfo");
        if (null != payInfo) {
            updateOrder.setTransactionId(payInfo.getString("TransactionId"));
            updateOrder.setSuccessTime(new Date(payInfo.getLong("PaidTime") * 1000));
        }
        shortplayWxPayOrderService.update(updateOrder);
        return "";
    }

    /**
     * 微信支付回调接口回调
     */
    @CrossOrigin
    @PostMapping("/callback/{appId}")
    public Result<String> callback(@PathVariable String appId, @RequestBody String notifyData, HttpServletRequest request) {
        log.info("微信小程序支付回调通知，appId={}, notifyData={}", appId, notifyData);
        WxPayService wxPayService = weiXinPayService.createWxPayService(appId);
        try {
            WxPayNotifyV3Result notifyV3Result = wxPayService.parseOrderNotifyV3Result(notifyData, null);
            WxPayNotifyV3Result.DecryptNotifyResult payResult = notifyV3Result.getResult();
            //内部订单号
            String outTradeNo = payResult.getOutTradeNo();
            //微信支付订单号
            String transactionId = payResult.getTransactionId();
            boolean paySuccess = Objects.equals(payResult.getTradeState(), "SUCCESS");
            if (!paySuccess) {
                log.error("微信小程序支付回调通知,支付失败,tradeState:{},outTradeNo:{}", payResult.getTradeState(), outTradeNo);
                return ResultBuilder.fail("支付失败");
            }
            // 解锁短剧
            ShortplayWxPayOrderEntity wxPayOrder = shortplayWxPayOrderService.selectByOutTradeNo(outTradeNo);
            if (null == wxPayOrder || null == wxPayOrder.getUnlockTvId()) {
                return ResultBuilder.success("操作成功");
            }
            ShortplayTvEntity tv = shortplayTvService.selectById(wxPayOrder.getUnlockTvId());

            ShortplayTvUnlockRecordEntity record = new ShortplayTvUnlockRecordEntity();
            record.setUserId(wxPayOrder.getUserId());
            record.setTvId(wxPayOrder.getUnlockTvId());
            record.setTvTbId(tv.getTbId());
            shortplayTvUnlockRecordService.insert(record);

            // 更新订单状态
            ShortplayWxPayOrderEntity updateOrder = new ShortplayWxPayOrderEntity();
            updateOrder.setId(wxPayOrder.getId());
            updateOrder.setPayStatus(1);
            updateOrder.setTradeType(payResult.getTradeType());
            updateOrder.setTradeState(payResult.getTradeState());
            updateOrder.setTransactionId(payResult.getTransactionId());
            updateOrder.setPayerTotal(payResult.getAmount().getPayerTotal());
            updateOrder.setCurrency(payResult.getAmount().getCurrency());
            updateOrder.setBankType(payResult.getBankType());
            updateOrder.setSuccessTime(DateUtils.parseDate(payResult.getSuccessTime()));
            shortplayWxPayOrderService.update(updateOrder);
        } catch (WxPayException e) {
            throw new RuntimeException(e);
        }
        return ResultBuilder.success("操作成功");
    }

    /**
     * 订单列表
     */
    @GetMapping("payOrder")
    public Result<List<PayOrderVo>> payOrder(PayOrderReq req) {
        if (null == req.getUserId()) {
            return ResultBuilder.success(Collections.emptyList());
        }
        ShortplayWxPayOrderEntity param = new ShortplayWxPayOrderEntity();
        param.setUserId(req.getUserId());
        param.setPayStatus(1);
        List<ShortplayWxPayOrderEntity> list = shortplayWxPayOrderService.selectList(param);
        list = list.stream().filter(s -> null != s.getUnlockTvId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return ResultBuilder.success(Collections.emptyList());
        }
        List<Long> tvIds = list.stream().map(ShortplayWxPayOrderEntity::getUnlockTvId).collect(Collectors.toList());
        Map<Long, ShortplayTvEntity> tvMap = shortplayTvService.selectMapByIds(tvIds);

        return ResultBuilder.success(list.stream().map(order -> {
            PayOrderVo vo = new PayOrderVo();
            vo.setId(order.getId());
            vo.setTitle(StrUtil.format("解锁《{}》", tvMap.getOrDefault(order.getUnlockTvId(), new ShortplayTvEntity()).getTvName()));
            vo.setPayTime(order.getSuccessTime());
            vo.setPayAmount(NumberUtils.fenToYuan(order.getTotalFee()));
            if (null == vo.getPayTime()) {
                vo.setPayTime(order.getGmtCreate());
            }
            return vo;
        }).sorted((o1, o2) -> o2.getPayTime().compareTo(o1.getPayTime())).collect(Collectors.toList()));
    }

    /**
     * 微信虚拟支付，支付签名
     */
    private String calcPaySig(String appKey, String signDataStr) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            Key sKey = new SecretKeySpec(appKey.getBytes(StandardCharsets.UTF_8), mac.getAlgorithm());
            mac.init(sKey);
            byte[] hash = mac.doFinal(("requestVirtualPayment&" + signDataStr).getBytes(StandardCharsets.UTF_8));
            return HexUtil.encodeHexStr(hash);
        } catch (Exception e) {
            log.error("虚拟支付,支付签名异常, appKey={}, signData={}", appKey, signDataStr, e);
        }
        return "";
    }

    /**
     * 微信虚拟支付，用户态签名
     */
    private String calSignature(String sessionKey, String signDataStr) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            Key sKey = new SecretKeySpec(sessionKey.getBytes(StandardCharsets.UTF_8), mac.getAlgorithm());
            mac.init(sKey);
            byte[] hash = mac.doFinal(signDataStr.getBytes(StandardCharsets.UTF_8));
            return HexUtil.encodeHexStr(hash);
        } catch (Exception e) {
            log.error("虚拟支付,用户态签名异常, sessionKey={}, signData={}", sessionKey, signDataStr, e);
        }
        return "";
    }

    /**
     * 生成支付订单号
     */
    private String generateOutTradeNo(Long userId) {
        return DateUtil.format(new Date(), "yyyyMMddHHmmss" + userId + RandomUtil.randomNumbers(8));
    }
}

