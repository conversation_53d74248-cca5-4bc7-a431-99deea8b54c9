package com.ruoyi.web.controller.novel.manage;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.req.novel.manage.NovelChannelDayStatisticsReq;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.novel.NovelChannelDayStatisticsEntity;
import com.ruoyi.system.service.novel.NovelChannelDayStatisticsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 小说渠道日统计数据Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/novel/novelChannelDayStatistics")
public class NovelChannelDayStatisticsController {
    @Autowired
    private NovelChannelDayStatisticsService novelChannelDayStatisticsService;

    /**
     * 查询小说渠道日统计数据列表
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChannelDayStatistics:list')")
    @GetMapping("/list")
    public PageResult<NovelChannelDayStatisticsEntity> list(NovelChannelDayStatisticsReq req) {
        TableSupport.startPage();
        List<NovelChannelDayStatisticsEntity> list = novelChannelDayStatisticsService.selectNovelChannelDayStatisticsList(req);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出小说渠道日统计数据列表
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChannelDayStatistics:export')")
    @Log(title = "小说渠道日统计数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NovelChannelDayStatisticsReq req) {
        List<NovelChannelDayStatisticsEntity> list = novelChannelDayStatisticsService.selectNovelChannelDayStatisticsList(req);
        ExcelUtil<NovelChannelDayStatisticsEntity> util = new ExcelUtil<NovelChannelDayStatisticsEntity>(NovelChannelDayStatisticsEntity.class);
        util.exportExcel(response, list, "小说渠道日统计数据数据");
    }

    /**
     * 获取小说渠道日统计数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChannelDayStatistics:query')")
    @GetMapping(value = "/{id}")
    public Result<NovelChannelDayStatisticsEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(novelChannelDayStatisticsService.selectNovelChannelDayStatisticsById(id));
    }

    /**
     * 新增小说渠道日统计数据
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChannelDayStatistics:add')")
    @Log(title = "小说渠道日统计数据", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody NovelChannelDayStatisticsEntity novelChannelDayStatistics) {
        return ResultBuilder.success(novelChannelDayStatisticsService.insertNovelChannelDayStatistics(novelChannelDayStatistics));
    }

    /**
     * 修改小说渠道日统计数据
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChannelDayStatistics:edit')")
    @Log(title = "小说渠道日统计数据", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody NovelChannelDayStatisticsEntity novelChannelDayStatistics) {
        return ResultBuilder.success(novelChannelDayStatisticsService.updateNovelChannelDayStatistics(novelChannelDayStatistics));
    }

    /**
     * 删除小说渠道日统计数据
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChannelDayStatistics:remove')")
    @Log(title = "小说渠道日统计数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(novelChannelDayStatisticsService.deleteNovelChannelDayStatisticsByIds(ids));
    }
}
