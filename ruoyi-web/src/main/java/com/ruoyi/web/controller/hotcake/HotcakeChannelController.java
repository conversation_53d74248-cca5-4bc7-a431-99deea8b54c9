package com.ruoyi.web.controller.hotcake;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StrListUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.hotcake.HotcakeChannelEntity;
import com.ruoyi.system.entity.hotcake.HotcakeVideoEntity;
import com.ruoyi.system.req.hotcake.HotcakeChannelReq;
import com.ruoyi.system.service.hotcake.HotcakeChannelService;
import com.ruoyi.system.service.hotcake.HotcakeVideoService;
import com.ruoyi.system.vo.hotcake.HotcakeChannelVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * [爆款小程序]渠道管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hotcake/hotcakeChannel")
public class HotcakeChannelController {

    @Autowired
    private HotcakeChannelService hotcakeChannelService;

    @Autowired
    private HotcakeVideoService hotcakeVideoService;

    /**
     * 查询爆款小程序渠道列表
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeChannel:list')")
    @GetMapping("/list")
    public PageResult<HotcakeChannelVO> list(HotcakeChannelEntity hotcakeChannel) {
        TableSupport.startPage();
        List<HotcakeChannelEntity> list = hotcakeChannelService.selectHotcakeChannelList(hotcakeChannel);
        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(list, this::convertTo));
    }

    /**
     * 导出爆款小程序渠道列表
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeChannel:export')")
    @Log(title = "爆款小程序渠道", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HotcakeChannelEntity hotcakeChannel) {
        List<HotcakeChannelEntity> list = hotcakeChannelService.selectHotcakeChannelList(hotcakeChannel);
        ExcelUtil<HotcakeChannelEntity> util = new ExcelUtil<HotcakeChannelEntity>(HotcakeChannelEntity.class);
        util.exportExcel(response, list, "爆款小程序渠道数据");
    }

    /**
     * 获取爆款小程序渠道详细信息
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeChannel:query')")
    @GetMapping(value = "/{id}")
    public Result<HotcakeChannelVO> getInfo(@PathVariable("id") Long id) {
        HotcakeChannelEntity channel = hotcakeChannelService.selectHotcakeChannelById(id);
        return ResultBuilder.success(convertTo(channel));
    }

    /**
     * 新增爆款小程序渠道
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeChannel:add')")
    @Log(title = "爆款小程序渠道", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody HotcakeChannelReq req) {
        String channelCode = hotcakeChannelService.generateChannelCode();
        String promotionUrl = hotcakeChannelService.generatePromotionUrl(req.getPromotionUrl(), channelCode);

        HotcakeChannelEntity channel = new HotcakeChannelEntity();
        channel.setPromotionName(req.getPromotionName());
        channel.setChannelCode(channelCode);
        channel.setPromotionUrl(promotionUrl);
        channel.setVideoId(req.getVideoId());
        channel.setFeedPageVideoIds(StrListUtils.listToStr(req.getFeedPageVideoIds()));
        channel.setPlayPageVideoIds(StrListUtils.listToStr(req.getPlayPageVideoIds()));
        channel.setFreeAd(req.getFreeAd());
        channel.setClickAdIpu(req.getClickAdIpu());
        channel.setShareIpu(req.getShareIpu());
        return ResultBuilder.success(hotcakeChannelService.insertHotcakeChannel(channel));
    }

    /**
     * 修改爆款小程序渠道
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeChannel:edit')")
    @Log(title = "爆款小程序渠道", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody HotcakeChannelReq req) {
        HotcakeChannelEntity channel = new HotcakeChannelEntity();
        channel.setId(req.getId());
        channel.setPromotionName(req.getPromotionName());
        channel.setVideoId(req.getVideoId());
        channel.setFeedPageVideoIds(StrListUtils.listToStr(req.getFeedPageVideoIds()));
        channel.setPlayPageVideoIds(StrListUtils.listToStr(req.getPlayPageVideoIds()));
        channel.setFreeAd(req.getFreeAd());
        channel.setClickAdIpu(req.getClickAdIpu());
        channel.setShareIpu(req.getShareIpu());
        channel.setPromotionUrl(req.getPromotionUrl());
        return ResultBuilder.success(hotcakeChannelService.updateHotcakeChannel(channel));
    }

    private HotcakeChannelVO convertTo(HotcakeChannelEntity entity) {
        HotcakeChannelVO vo = BeanUtil.copyProperties(entity, HotcakeChannelVO.class);
        vo.setFeedPageVideoIds(StrListUtils.strToList(entity.getFeedPageVideoIds()));
        vo.setPlayPageVideoIds(StrListUtils.strToList(entity.getPlayPageVideoIds()));

        HotcakeVideoEntity video = hotcakeVideoService.selectById(entity.getId());
        if (null != video) {
            vo.setVideoTitle(video.getTitle());
        }
        return vo;
    }
}
