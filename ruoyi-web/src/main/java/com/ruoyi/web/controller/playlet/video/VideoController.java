package com.ruoyi.web.controller.playlet.video;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.img.gif.AnimatedGifEncoder;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.PlayletPlatformEnum;
import com.ruoyi.common.enums.PublishStatusEnum;
import com.ruoyi.common.enums.WxAuditStatusEnum;
import com.ruoyi.common.enums.WxUploadStatusEnum;
import com.ruoyi.common.exception.BizRuntimeException;
import com.ruoyi.common.param.IdParam;
import com.ruoyi.common.utils.*;
import com.ruoyi.system.entity.relate.VideoGroupWxRelateEntity;
import com.ruoyi.system.entity.relate.VideoWxRelateEntity;
import com.ruoyi.system.entity.video.VideoDyRelateEntity;
import com.ruoyi.system.entity.video.VideoEntity;
import com.ruoyi.system.entity.video.VideoGroupDyRelateEntity;
import com.ruoyi.system.entity.video.VideoGroupEntity;
import com.ruoyi.system.manager.WxDramaService;
import com.ruoyi.system.req.playlet.dy.DyOnlineReq;
import com.ruoyi.system.req.playlet.video.*;
import com.ruoyi.system.service.relate.VideoGroupWxRelateService;
import com.ruoyi.system.service.relate.VideoWxRelateService;
import com.ruoyi.system.service.shortplay.third.TmeService;
import com.ruoyi.system.service.video.*;
import com.ruoyi.system.vo.playlet.video.*;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.system.common.VideoConstants.XIAO_HE_DUO_DUO_APP_ID;
import static com.ruoyi.system.common.VideoConstants.appIdList;

/**
 * 短剧管理
 *
 * <AUTHOR>
 * @date 2023/7/4 14:45
 */
@Slf4j
@RestController
@RequestMapping("video")
public class VideoController {

    @Autowired
    private VideoGroupService videoGroupService;

    @Autowired
    private VideoService videoService;

    @Autowired
    private WxDramaService wxDramaService;

    @Resource
    private ExecutorService executorService;
    @Autowired
    private DouyinService douyinService;

    @Autowired
    private VideoGroupDyRelateService videoGroupDyRelateService;

    @Autowired
    private VideoDyRelateService videoDyRelateService;
    @Autowired
    private VideoGroupWxRelateService videoGroupWxRelateService;
    @Autowired
    private VideoWxRelateService videoWxRelateService;
    @Autowired
    private TmeService tmeService;

    /**
     * gif帧率
     */
    private static final int TARGET_FRAME_RATE = 15;
    /**
     * 截取视频时长
     */
    private static final int TARGET_DURATION_SECONDS = 10;

    /**
     * 上传文件目录
     */
    @Value("${playlet.profile}")
    private String uploadPath;
    @Value("${oss.url}")
    private String ossUrl;

    /**
     * 短剧分组列表
     *
     * @return
     */
    @Anonymous
    @GetMapping("groupList")
    public PageResult<VideoGroupListVo> groupList(VideoGroupSearchReq req) {
        TableSupport.startPage();
        List<VideoGroupEntity> groupEntities = videoGroupService.selectListByReq(req);
        List<Long> videoGroupIds = groupEntities.stream().map(VideoGroupEntity::getId).collect(Collectors.toList());
        //查询已上传集数
        Map<Long, Integer> videoCount = videoService.selectVideoCount(videoGroupIds);

        List<VideoGroupDyRelateEntity> dyRelateEntities = videoGroupDyRelateService.selectListByGroupIds(videoGroupIds);
        Map<Long, List<VideoGroupDyRelateEntity>> groupMaps = dyRelateEntities.stream().collect(Collectors.groupingBy(VideoGroupDyRelateEntity::getVideoGroupId));

        // 查询 wx 相关的关系表
        List<VideoGroupWxRelateEntity> wxRelateEntities = videoGroupWxRelateService.selectListByGroupIds(videoGroupIds);
        Map<Long, List<VideoGroupWxRelateEntity>> wxGroupMaps = wxRelateEntities.stream().collect(Collectors.groupingBy(VideoGroupWxRelateEntity::getVideoGroupId));


        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(groupEntities, entity -> {
            VideoGroupListVo vo = BeanUtil.copyProperties(entity, VideoGroupListVo.class);

            ArrayList<DramaAppIdItem> dramaAppIdItems = new ArrayList<>();
            DramaAppIdItem dramaAppIdItem = new DramaAppIdItem();
            dramaAppIdItem.setDramaId(entity.getDramaId());
            dramaAppIdItem.setAppId(XIAO_HE_DUO_DUO_APP_ID);
            dramaAppIdItems.add(dramaAppIdItem);

            List<VideoGroupDyRelateEntity> relateList = groupMaps.get(entity.getId());
            if (CollectionUtils.isNotEmpty(relateList)) {
                List<DyAuditInfo> auditInfoList = relateList.stream().map(relate -> {
                    DyAuditInfo info = new DyAuditInfo();
                    info.setAppId(relate.getAppId());
                    info.setDyAuditStatus(relate.getDyAuditStatus());
                    info.setDyAlbumId(relate.getDyAlbumId() + "");
                    info.setDyVersion(relate.getDyVersion());
                    info.setDyAuditErrorMsg(relate.getDyAuditErrorMsg());
                    info.setDyOnlineStatus(relate.getDyOnlineStatus());
                    return info;
                }).collect(Collectors.toList());
                vo.setDyAuditInfoList(auditInfoList);
            }

            List<VideoGroupWxRelateEntity> wxRelateList = wxGroupMaps.get(entity.getId());
            if (CollectionUtils.isNotEmpty(wxRelateList)) {
                List<WxAuditInfo> auditInfoList = wxRelateList.stream().map(relate -> {
                    WxAuditInfo info = new WxAuditInfo();
                    info.setAppId(relate.getAppId());
                    info.setWxAuditStatus(relate.getWxAuditStatus());
                    info.setAuditErrorMsg(relate.getAuditErrorMsg());
                    return info;
                }).collect(Collectors.toList());
                vo.setWxAuditInfoList(auditInfoList);

                for (VideoGroupWxRelateEntity videoGroupWxRelateEntity : wxRelateList) {
                    DramaAppIdItem dramaAppIdRel = new DramaAppIdItem();
                    dramaAppIdRel.setDramaId(videoGroupWxRelateEntity.getDramaId());
                    dramaAppIdRel.setAppId(videoGroupWxRelateEntity.getAppId());
                    dramaAppIdItems.add(dramaAppIdRel);
                }
            }

            vo.setDramaAppIdList(dramaAppIdItems);

            // video group 小和多多 小程序审核状态
            if (CollectionUtils.isEmpty(vo.getWxAuditInfoList())) {
                List<WxAuditInfo> auditInfoList = new ArrayList<>();
                WxAuditInfo wxAuditInfo = new WxAuditInfo();
                wxAuditInfo.setAppId(XIAO_HE_DUO_DUO_APP_ID);
                wxAuditInfo.setAuditErrorMsg(entity.getAuditErrorMsg());
                wxAuditInfo.setWxAuditStatus(entity.getWxAuditStatus());
                auditInfoList.add(wxAuditInfo);
                vo.setWxAuditInfoList(auditInfoList);
            } else {
                List<WxAuditInfo> wxAuditInfoList = vo.getWxAuditInfoList();
                WxAuditInfo wxAuditInfo = new WxAuditInfo();
                wxAuditInfo.setAppId(XIAO_HE_DUO_DUO_APP_ID);
                wxAuditInfo.setAuditErrorMsg(entity.getAuditErrorMsg());
                wxAuditInfo.setWxAuditStatus(entity.getWxAuditStatus());
                wxAuditInfoList.add(wxAuditInfo);
            }

            vo.setPlatforms(IntListUtils.convertToList(entity.getPlatform()));
            vo.setUploadedEpisode(videoCount.get(entity.getId()));
            return vo;
        }));
    }

    /**
     * 短剧详情
     *
     * @return
     */
    @GetMapping("groupInfo")
    public Result<VideoGroupInfoVo> groupInfo(@Valid @NotNull(message = "id不能为空") Long id) {
        VideoGroupEntity entity = videoGroupService.selectById(id);
        if (Objects.isNull(entity)) {
            throw new BizRuntimeException(ErrorCode.VIDEO_GROUP_NOT_EXIST);
        }
        VideoGroupInfoVo vo = BeanUtil.copyProperties(entity, VideoGroupInfoVo.class);
        vo.setPlatforms(IntListUtils.convertToList(entity.getPlatform()));
        if (StringUtils.isBlank(entity.getActorList())) {
            vo.setActors(Collections.EMPTY_LIST);
        } else {
            vo.setActors(JSONArray.parseArray(entity.getActorList(), VideoGroupActorListVo.class));
        }
        if (StringUtils.isNotBlank(entity.getDirectors())) {
            vo.setDirectorList(JSONArray.parseArray(entity.getDirectors(), String.class));
        }
        if (StringUtils.isNotBlank(entity.getProducers())) {
            vo.setProducerList(JSONArray.parseArray(entity.getProducers(), String.class));
        }
        if (StringUtils.isNotBlank(entity.getTags())) {
            vo.setTags(JSONArray.parseArray(entity.getTags(), String.class));
        }
        return ResultBuilder.success(vo);
    }

    /**
     * 更新上架的短剧剧集集数
     */
    @Deprecated
    @PostMapping("updateEpisode")
    @Log(title = "更新上架的短剧剧集集数")
    public Result<Boolean> updateEpisode(@RequestBody @Valid EditVideoEpisodeReq param) {
        VideoGroupEntity entity = BeanUtil.copyProperties(param, VideoGroupEntity.class);
        return ResultBuilder.success(videoGroupService.updateById(entity));
    }

    /**
     * 更新短剧排序
     */
    @PostMapping("updateSort")
    @Log(title = "更新短剧排序")
    public Result<Boolean> updateSort(@RequestBody @Valid EditVideoSortReq param) {
        VideoGroupEntity entity = BeanUtil.copyProperties(param, VideoGroupEntity.class);
        return ResultBuilder.success(videoGroupService.updateById(entity));
    }

    /**
     * 更新短剧腾讯音乐分类
     */
    @PostMapping("updateVideoCategory")
    @Log(title = "更新短剧腾讯音乐分类")
    public Result<Boolean> updateVideoCategory(@RequestBody @Valid EditVideoTmeCategoryReq param) {
        VideoGroupEntity updateVideo = new VideoGroupEntity();
        updateVideo.setId(param.getId());
        updateVideo.setTmeCategory(param.getTmeCategory());
        return ResultBuilder.success(videoGroupService.updateById(updateVideo));
    }

    /**
     * 批量上下架
     */
    @PostMapping("batchPublish")
    public Result<Boolean> batchPublish(@RequestBody @Valid BatchPublishReq req) {
        return ResultBuilder.success(videoGroupService.batchPublish(req.getIds(), req.getStatus()));
    }

    /**
     * 批量同步到腾讯音乐
     */
    @PostMapping("batchPushTme")
    public Result<Boolean> batchPushTme(@RequestBody @Valid BatchPushTmeReq req) {
        List<VideoGroupEntity> videoGroupList = videoGroupService.selectListByIds(req.getIds());
        if (CollectionUtils.isEmpty(videoGroupList)) {
            return ResultBuilder.fail("参数错误");
        }
        tmeService.pushToTme(videoGroupList);
        return ResultBuilder.success(true);
    }

    /**
     * 上下架短剧
     */
    @PostMapping("publish")
    @Log(title = "上下架短剧")
    public Result<Boolean> publish(@RequestBody @Valid PublishVideoReq param) {
        VideoGroupEntity videoGroup = videoGroupService.selectById(param.getId());
        if (Objects.isNull(videoGroup)) {
            throw new BizRuntimeException(ErrorCode.VIDEO_GROUP_NOT_EXIST);
        }
        VideoGroupEntity entity = BeanUtil.copyProperties(param, VideoGroupEntity.class);
        return ResultBuilder.success(videoGroupService.updateById(entity));
    }

    /**
     * 删除短剧
     */
    @PostMapping("delete")
    @Log(title = "删除短剧")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(@RequestBody @Valid IdParam param) {
        VideoGroupEntity entity = videoGroupService.selectById(param.getId());
        if (Objects.isNull(entity)) {
            throw new BizRuntimeException(ErrorCode.VIDEO_GROUP_NOT_EXIST);
        }
        if (Objects.equals(entity.getStatus(), PublishStatusEnum.PUBLISH.getStatus())) {
            throw new BizRuntimeException(ErrorCode.CANNOT_DELETE);
        }
        Boolean isDeletedVideoGroup = videoGroupService.deleteById(param.getId());
        List<VideoEntity> videoEntities = videoService.selectListByVideoGroupId(param.getId());
        if (CollectionUtils.isEmpty(videoEntities)) {
            return ResultBuilder.success(isDeletedVideoGroup);
        }
        Boolean isDeletedVideo = videoService.deleteByVideoGroupId(param.getId());
        return ResultBuilder.success(isDeletedVideo && isDeletedVideoGroup);
    }

    /**
     * 新增编辑短剧
     *
     * @param req
     * @return
     */
    @PostMapping("editVideoGroup")
    @Log(title = "新增编辑短剧")
    public Result<Boolean> editVideoGroup(@RequestBody @Valid EditVideoGroupReq req) {
        if (StringUtils.isNotBlank(req.getStory())) {
            req.setStory(req.getStory().replaceAll("\\s+", "").replaceAll(":", "，").replaceAll("：", "，"));
        }
        // format actors profile field. remove spaces and colon
        req.getActors().forEach(actor -> {
            actor.setProfile(actor.getProfile().replaceAll("\\s+", "").replaceAll(":", "，").replaceAll("：", "，"));
        });
        SysUser user = SecurityUtils.getLoginUser().getUser();
        req.setSspAccountId(1L); //TODO 这个字段可以删除，先随便传一个值，实际无意义
        VideoGroupEntity updateEntity = BeanUtil.copyProperties(req, VideoGroupEntity.class);
        updateEntity.setOperator(user.getNickName());
        updateEntity.setPlatform(IntListUtils.convertToInteger(req.getPlatforms()));
        updateEntity.setActorList(CollectionUtils.isEmpty(req.getActors()) ? JSONArray.toJSONString(Collections.EMPTY_LIST) : JSONArray.toJSONString(req.getActors()));
        updateEntity.setDirectors(CollectionUtils.isEmpty(req.getDirectorList()) ? JSONArray.toJSONString(Collections.EMPTY_LIST) : JSONArray.toJSONString(req.getDirectorList()));
        updateEntity.setProducers(CollectionUtils.isEmpty(req.getProducerList()) ? JSONArray.toJSONString(Collections.EMPTY_LIST) : JSONArray.toJSONString(req.getProducerList()));
        updateEntity.setTags(CollectionUtils.isEmpty(req.getTags()) ? JSONArray.toJSONString(Collections.EMPTY_LIST) : JSONArray.toJSONString(req.getTags()));
        boolean success;
        if (NumberUtils.isNotNullOrLteZero(req.getId())) {
            //如果勾选了抖音平台，则判断这部剧下的所有剧集是否都已经上传到抖音，没有则执行上传逻辑
            success = videoGroupService.updateById(updateEntity);
            douyinService.checkAllVideoUploadDouyin(req.getId());
            wxDramaService.uploadVideoToWxByVideoGroupId(req.getId());
            // 另外两个小程序也做视频上传
            for (String appId : appIdList) {
                wxDramaService.uploadVideoToWxByVideoGroupIdAndAppId(req.getId(), appId);
            }
        } else {
            success = videoGroupService.insert(updateEntity);
            // 关系表插入
            for (String appId : appIdList) {
                VideoGroupWxRelateEntity insertRelateEntity = new VideoGroupWxRelateEntity();
                insertRelateEntity.setVideoGroupId(updateEntity.getId());
                insertRelateEntity.setAppId(appId);
                videoGroupWxRelateService.insertVideoGroupWxRelate(insertRelateEntity);
            }
        }
        if (StringUtils.isBlank(req.getGifImg())) {
            generateGifCoverImg(updateEntity.getId(), updateEntity.getImg());
        }
        //如果短剧选择了抖音平台，则在抖音平台创建短剧
        douyinService.syncVideoToDy(updateEntity.getId());

        return ResultBuilder.success(success);
    }

    /**
     * 为短剧自动生成gif封面
     *
     * @param videoGroupId
     */
    private void generateGifCoverImg(Long videoGroupId, String img) {
        List<VideoEntity> videoEntities = videoService.selectListByVideoGroupId(videoGroupId);
        if (CollectionUtils.isEmpty(videoEntities)) {
            log.warn("短剧无剧集,无法生成gif封面,videoGroupId:{}", videoGroupId);
            return;
        }
        String videoUrl = videoEntities.stream().filter(entity -> entity.getEpisode() == 1).map(VideoEntity::getVideoUrl).findFirst().get();
        if (StringUtils.isBlank(videoUrl)) {
            return;
        }
        Long userId = SecurityUtils.getUserId();
        executorService.submit(() -> {
            File folder = new File(uploadPath);
            // 检查文件夹是否存在
            if (!folder.exists()) {
                folder.mkdir();
            }
            String[] split = videoUrl.split("/");
            String fileName = split[split.length - 1];
            fileName = fileName.substring(0, fileName.lastIndexOf("."));
            downloadVideo(videoUrl, uploadPath + "/" + fileName + ".mp4");
            String uploadPath = video2Gif(userId, this.uploadPath + "/" + fileName + ".mp4", this.uploadPath + "/" + fileName + ".gif", img);
            if (StringUtils.isBlank(uploadPath)) {
                return;
            }
            VideoGroupEntity updateEntity = new VideoGroupEntity();
            updateEntity.setId(videoGroupId);
            updateEntity.setGifImg(ossUrl + uploadPath);
            videoGroupService.updateById(updateEntity);
        });
    }

    /**
     * 通过url下载
     *
     * @param fileUrl
     * @param filePath
     */
    public static void downloadVideo(String fileUrl, String filePath) {
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection urlCon = (HttpURLConnection) url.openConnection();
            urlCon.setConnectTimeout(6000);
            urlCon.setReadTimeout(6000);
            int code = urlCon.getResponseCode();
            if (code != HttpURLConnection.HTTP_OK) {
                log.error("文件下载失败,url:{}", fileUrl);
            }
            //读文件流
            DataInputStream in = new DataInputStream(urlCon.getInputStream());
            DataOutputStream out = new DataOutputStream(Files.newOutputStream(Paths.get(filePath)));
            byte[] buffer = new byte[2048];
            int count;
            while ((count = in.read(buffer)) > 0) {
                out.write(buffer, 0, count);
            }
            out.flush();
            out.close();
            in.close();
        } catch (IOException e) {
            log.error("文件下载失败,url:{}", fileUrl);
        }
    }

    /**
     * 视频转gif
     *
     * @param videoPath
     * @param gifPath
     * @param coverImg
     * @return
     */
    public String video2Gif(Long userId, String videoPath, String gifPath, String coverImg) {
        String upload = "";
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoPath)) {
            FileOutputStream targetFile = new FileOutputStream(gifPath);
            grabber.start();
            int totalFrames = grabber.getLengthInFrames();//总帧数
            double frameRate = (double) totalFrames / grabber.getLengthInTime() * 1000000;//帧率
            int frameRateInt = (int) Math.round(frameRate);
            AnimatedGifEncoder encoder = new AnimatedGifEncoder();
            encoder.setFrameRate(frameRateInt);
            encoder.start(targetFile);
            Java2DFrameConverter converter = new Java2DFrameConverter();
            int targetTotalFrames = frameRateInt * TARGET_DURATION_SECONDS;//目标gif帧数
            double timeInterval = 1000.0 / TARGET_FRAME_RATE; // 每一帧的时间间隔(毫秒)
            int n = 0;
            URL imageUrl = new URL(coverImg);
            BufferedImage image = ImageIO.read(imageUrl);
            encoder.setDelay(1000);
            //取视频第一帧获取视频长宽
            grabber.setTimestamp(0);
            BufferedImage firstFrameImage = converter.convert(grabber.grabImage());
            //尺寸缩小5倍
            int height = firstFrameImage.getHeight() / 5;
            int width = firstFrameImage.getWidth() / 5;
            BufferedImage compressCoverImage = Thumbnails.of(image).outputFormat("jpg").size(width, height).outputQuality(0.0).asBufferedImage();
            encoder.addFrame(compressCoverImage);
            for (int i = 0; i <= targetTotalFrames; i += 2) {
                double frameTime = n * timeInterval * 1000; // 帧时间（微秒）
                grabber.setTimestamp((long) frameTime);
                BufferedImage frameImage = converter.convert(grabber.grabImage());
                BufferedImage compressImage = Thumbnails.of(frameImage).outputFormat("jpg").size(width, height).outputQuality(0.0).asBufferedImage();
                encoder.setDelay((int) timeInterval);
                encoder.addFrame(compressImage);
                n++;
            }
            encoder.finish();
            File file = FileUtil.file(gifPath);
            upload = OssUtils.upload(file, OssUtils.datePath(), userId, "apocalypse");
        } catch (Exception e) {
            log.error("视频转换gif错误,error:{}", e.getMessage());
        } finally {
            deleteLocalFile(gifPath);
            deleteLocalFile(videoPath);
        }
        return upload;
    }

    /**
     * 删除本地文件
     *
     * @param filePath
     * @return
     */
    public static boolean deleteLocalFile(String filePath) {
        File file = FileUtil.file(filePath);
        if (file.isFile() && file.exists()) {
            boolean result = file.delete();
            if (!result) {
                log.warn("本地文件删除失败, fileName={}", filePath);
            }
            return result;
        }
        return true;
    }

    /**
     * 为所有短剧生成gif封面
     *
     * @return
     */
    @GetMapping("createGifCover")
    public String createGifCover(Long videoGroupId) {
        VideoGroupSearchReq req = new VideoGroupSearchReq();
        if (NumberUtils.isNotNullOrLteZero(videoGroupId)) {
            req.setId(videoGroupId);
        }
        List<VideoGroupEntity> videoGroupEntities = videoGroupService.selectListByReq(req);
        videoGroupEntities.stream().filter(videoGroupEntity -> StringUtils.isBlank(videoGroupEntity.getGifImg())).forEach(videoGroupEntity -> {
            generateGifCoverImg(videoGroupEntity.getId(), videoGroupEntity.getImg());
        });
        return "success";
    }

    /**
     * 上传剧集
     */
    @PostMapping("uploadEpisode")
    @Log(title = "上传剧集")
    public Result<Boolean> uploadEpisode(@RequestBody @Valid UploadVideoReq req) {
        VideoGroupEntity videoGroup = videoGroupService.selectById(req.getVideoGroupId());
        if (Objects.isNull(videoGroup)) {
            throw new BizRuntimeException(ErrorCode.VIDEO_GROUP_NOT_EXIST);
        }
        List<VideoEntity> videoEntities = videoService.selectListByVideoGroupId(req.getVideoGroupId());
        Map<Integer, Long> episodeMap = videoEntities.stream().collect(Collectors.toMap(VideoEntity::getEpisode, VideoEntity::getId, (v1, v2) -> v1));
        List<VideoEntity> insertList = req.getReqList().stream().map(video -> {
            VideoEntity entity = BeanUtil.copyProperties(video, VideoEntity.class);
            entity.setVideoGroupId(req.getVideoGroupId());
            if (episodeMap.containsKey(video.getEpisode())) {
                throw new BizRuntimeException(ErrorCode.EPISODE_REPETITION);
            }
            return entity;
        }).collect(Collectors.toList());
        Boolean success = videoService.batchInsert(insertList);

        // tb_video_wx_relate insert
        for (String appId : appIdList) {
            List<VideoWxRelateEntity> insertEntities = new ArrayList<>();
            insertList.forEach(videoEntity -> {
                VideoWxRelateEntity insertEntity = new VideoWxRelateEntity();
                insertEntity.setAppId(appId);
                insertEntity.setVideoId(videoEntity.getId());
                insertEntity.setVideoGroupId(req.getVideoGroupId());
                insertEntities.add(insertEntity);
            });
            videoWxRelateService.batchInsert(insertEntities);
        }


        if (BooleanUtils.isTrue(success)) {
            executorService.submit(() -> {
                //批量上传给微信后台 ,微信默认都上传
                insertList.forEach(videoEntity -> {
                    wxDramaService.uploadVideoToWx(videoEntity.getId());
                });
                // According to appId to upload
                for (String appId : appIdList) {
                    insertList.forEach(videoEntity -> {
                        wxDramaService.uploadVideoToWxByAppId(videoEntity.getId(), appId);
                    });
                }
            });
            douyinService.checkAllVideoUploadDouyin(req.getVideoGroupId());

        }

        updateEpisode(req.getVideoGroupId());
        return ResultBuilder.success(success);
    }

    /**
     * 某些剧集上传，只在数据库中保存了，但是还没有调用微信上传接口。我们修复这一部分的视频
     *
     * @param videoGroupId
     * @return
     */
    @Anonymous
    @GetMapping("ReUploadEpisode")
    public void ReUploadEpisode(@NotNull(message = "videoGroupId不能为空") Long videoGroupId) {
        log.info("ReUploadEpisode videoGroupId:{}", videoGroupId);
        VideoGroupEntity videoGroup = videoGroupService.selectById(videoGroupId);
        if (Objects.isNull(videoGroup)) {
            throw new BizRuntimeException(ErrorCode.VIDEO_GROUP_NOT_EXIST);
        }
        List<VideoEntity> videoEntities = videoService.selectListByVideoGroupId(videoGroupId);

        executorService.submit(() -> {
            //批量上传给微信后台 ,微信默认都上传
            videoEntities.forEach(videoEntity -> {
                wxDramaService.uploadVideoToWx(videoEntity.getId());
            });
            // According to appId to upload
            for (String appId : appIdList) {
                videoEntities.forEach(videoEntity -> {
                    wxDramaService.uploadVideoToWxByAppId(videoEntity.getId(), appId);
                });
            }
        });

    }


    /**
     * 更新剧集上架集数，上架级数为上传级数
     *
     * @param videoGroupId
     * @return
     */
    private boolean updateEpisode(Long videoGroupId) {
        List<VideoEntity> videoEntityList = videoService.selectListByVideoGroupId(videoGroupId);
        VideoGroupEntity videoGroup = new VideoGroupEntity();
        videoGroup.setId(videoGroupId);
        videoGroup.setUpdateEpisode(videoEntityList.size());
        return videoGroupService.updateById(videoGroup);
    }

    /**
     * 剧集列表
     */
    @GetMapping("videoList")
    public PageResult<VideoListVo> videoList(VideoSearchReq req) {
        TableSupport.startPage();

        List<VideoEntity> videoEntities = videoService.selectListByReq(req);
        List<Long> videoIds = videoEntities.stream().map(VideoEntity::getId).collect(Collectors.toList());
        List<Long> videoGroupIds = videoEntities.stream().map(VideoEntity::getVideoGroupId).collect(Collectors.toList());
        List<VideoGroupEntity> groupEntities = videoGroupService.selectListByIds(videoGroupIds);
        Map<Long, VideoGroupEntity> groupEntityMap = groupEntities.stream().collect(Collectors.toMap(VideoGroupEntity::getId, Function.identity(), (v1, v2) -> v1));
        List<VideoDyRelateEntity> videoDyRelateEntities = videoDyRelateService.selectListByVideoIds(videoIds);
        Map<Long, List<VideoDyRelateEntity>> videoMap = videoDyRelateEntities.stream().collect(Collectors.groupingBy(VideoDyRelateEntity::getVideoId));

        List<VideoWxRelateEntity> videoWxRelateEntities = videoWxRelateService.selectListByVideoIds(videoIds);
        Map<Long, List<VideoWxRelateEntity>> wxVideoMap = videoWxRelateEntities.stream().collect(Collectors.groupingBy(VideoWxRelateEntity::getVideoId));

        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(videoEntities, entity -> {
            VideoListVo vo = BeanUtil.copyProperties(entity, VideoListVo.class);
            List<VideoDyRelateEntity> relateEntities = videoMap.get(entity.getId());
            if (CollectionUtils.isNotEmpty(relateEntities)) {
                vo.setDyVideoInfoList(BeanUtil.copyToList(relateEntities, DyVideoInfo.class));
            }

            List<VideoWxRelateEntity> wxRelateEntities = wxVideoMap.get(entity.getId());
            if (CollectionUtils.isNotEmpty(wxRelateEntities)) {
                vo.setWxVideoInfoList(BeanUtil.copyToList(wxRelateEntities, WxVideoInfo.class));
            }

            if (CollectionUtils.isEmpty(vo.getWxVideoInfoList())) {
                List<WxVideoInfo> wxVideoInfos = new ArrayList<>();
                WxVideoInfo wxVideoInfo = new WxVideoInfo();
                wxVideoInfo.setAppId(XIAO_HE_DUO_DUO_APP_ID);
                wxVideoInfo.setWxUploadStatus(entity.getWxUploadStatus());
                wxVideoInfo.setWxAuditStatus(entity.getWxAuditStatus());
                wxVideoInfo.setUploadErrorMsg(entity.getUploadErrorMsg());
                wxVideoInfo.setAuditErrorMsg(entity.getAuditErrorMsg());
                wxVideoInfos.add(wxVideoInfo);
                vo.setWxVideoInfoList(wxVideoInfos);
            } else {
                List<WxVideoInfo> wxVideoInfoList = vo.getWxVideoInfoList();
                WxVideoInfo wxVideoInfo = new WxVideoInfo();
                wxVideoInfo.setAppId(XIAO_HE_DUO_DUO_APP_ID);
                wxVideoInfo.setWxUploadStatus(entity.getWxUploadStatus());
                wxVideoInfo.setWxAuditStatus(entity.getWxAuditStatus());
                wxVideoInfo.setUploadErrorMsg(entity.getUploadErrorMsg());
                wxVideoInfo.setAuditErrorMsg(entity.getAuditErrorMsg());
                wxVideoInfoList.add(wxVideoInfo);
            }

            VideoGroupEntity groupEntity = groupEntityMap.get(entity.getVideoGroupId());
            vo.setVideoGroupTitle(groupEntity.getTitle());
            vo.setVideoId(entity.getId());
            vo.setPrice(vo.getEpisode() < groupEntity.getStartChargeEpisode() ? 0 : NumberUtils.isNullOrLteZero(vo.getPrice()) ? groupEntity.getPrice() : vo.getPrice());
            return vo;
        }));
    }

    /**
     * 编辑视频
     *
     * @param req
     * @return
     */
    @PostMapping("editVideo")
    @Log(title = "编辑视频")
    public Result<Boolean> editVideo(@RequestBody @Valid EditVideoReq req) {
        VideoEntity videoEntity = videoService.selectById(req.getId());
        boolean isUpload = false;
        VideoEntity entity = BeanUtil.copyProperties(req, VideoEntity.class);
        if (!Objects.equals(req.getVideoUrl(), videoEntity.getVideoUrl())) {
            //如果视频更新了，则重置微信上传状态
            entity.setWxUploadStatus(WxUploadStatusEnum.WAITING.getStatus());
            isUpload = true;
        } else {
            entity.setWxUploadStatus(videoEntity.getWxUploadStatus());
        }
        Boolean success = videoService.updateById(entity);
        // 更新关系表
        for (String appId : appIdList) {
            videoWxRelateService.updateUploadStatusByVideoIdAndAppId(req.getId(), appId, entity.getWxUploadStatus());
        }

        if (isUpload) {
            VideoDyRelateEntity updateEntity = new VideoDyRelateEntity();
            updateEntity.setVideoId(req.getId());
            updateEntity.setDyEpisodeId(0L);
            videoDyRelateService.updateByVideoId(updateEntity);
        }
        if (isUpload) {
            executorService.submit(() -> {
                //上传微信后台
                wxDramaService.uploadVideoToWx(req.getId());
                douyinService.checkAndUploadVideoToDouyin(req.getId());
                // 另外两个 appId 上传
                for (String appId : appIdList) {
                    wxDramaService.uploadVideoToWxByAppId(req.getId(), appId);
                }
            });
        }
        return ResultBuilder.success(success);
    }

    /**
     * 删除剧集
     *
     * @param params
     * @return
     */
    @PostMapping("deleteVideo")
    @Log(title = "删除剧集")
    public Result<Boolean> deleteVideo(@RequestBody @Valid List<IdParam> params) {
        if ( CollectionUtils.isEmpty(params)) {
            return ResultBuilder.success(false);
        }
        for (IdParam param : params) {
            VideoEntity entity = videoService.selectById(param.getId());
            if (Objects.isNull(entity)) {
               continue;
            }
            videoDyRelateService.deleteByVideoId(param.getId());
            boolean success = videoService.deleteById(param.getId());
            // delete wx relate table
            videoWxRelateService.deleteByVideoId(param.getId());
            updateEpisode(entity.getVideoGroupId());
        }

        return ResultBuilder.success(true);
    }

    /**
     * 查询所有短剧和剧集
     */
    @GetMapping("simpleVideoList")
    public Result<List<VideoGroupSimpleVo>> simpleVideoList() {
        List<VideoGroupEntity> groupEntities = videoGroupService.selectListByReq(new VideoGroupSearchReq());
        List<Long> videoGroupIds = groupEntities.stream().map(VideoGroupEntity::getId).collect(Collectors.toList());
        Map<Long, List<VideoEntity>> videoGroupMap = videoService.selectListByVideoGroupIds(videoGroupIds).stream().collect(Collectors.groupingBy(VideoEntity::getVideoGroupId));
        return ResultBuilder.success(groupEntities.stream().map(groupEntity -> {
            VideoGroupSimpleVo vo = BeanUtil.copyProperties(groupEntity, VideoGroupSimpleVo.class);
            vo.setVideoGroupId(groupEntity.getId());
            if (!videoGroupMap.containsKey(groupEntity.getId())) {
                return vo;
            }
            //根据剧集列表字段排序
            vo.setVideoList(videoGroupMap.get(groupEntity.getId()).stream().sorted(Comparator.comparing(VideoEntity::getEpisode)).map(videoEntity -> {
                VideoSimpleVo videoSimpleVo = BeanUtil.copyProperties(videoEntity, VideoSimpleVo.class);
                videoSimpleVo.setVideoId(videoEntity.getId());
                return videoSimpleVo;
            }).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList()));
    }

    /**
     * 送审剧目
     */
    @PostMapping("auditVideoGroup")
    @Log(title = "送审剧目")
    public Result<Boolean> auditVideoGroup(@RequestBody @Valid AuditVideoReq req) {
//        if (!SpringEnvironmentUtils.isProd()) {
//            throw new BizRuntimeException("测试环境不能送审");
//        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        VideoGroupEntity updateGroup = new VideoGroupEntity();
        List<String> wxAppIds = req.getWxAppIds();
        if (req.getPlatforms().contains(PlayletPlatformEnum.WEI_XIN.getPlatform()) && CollectionUtils.isNotEmpty(wxAppIds)) {
            // 校验给的 WxAppIds ，是否是 小禾多多、全民来追剧、好剧导航 这三个
            if (!checkWxAppIds(wxAppIds)) {
                throw new BizRuntimeException("非法的微信appId, appId 只支持 小禾多多、全民来追剧、好剧导航");
            }
            //更新剧目id
            if (wxAppIds.contains(XIAO_HE_DUO_DUO_APP_ID)) {
                Long dramaId = wxDramaService.auditVideoGroup(req.getId());
                updateGroup.setDramaId(dramaId);
                updateGroup.setWxAuditStatus(WxAuditStatusEnum.AUDITING.getStatus());
            }

            wxAppIds.remove(XIAO_HE_DUO_DUO_APP_ID);
            // According to appId to audit the drama
            for (String appId : wxAppIds) {
                Long dramaIdByAppId = wxDramaService.auditVideoGroupByAppId(req.getId(), appId);
                VideoGroupWxRelateEntity updateRelate = new VideoGroupWxRelateEntity();
                updateRelate.setDramaId(dramaIdByAppId);
                updateRelate.setWxAuditStatus(WxAuditStatusEnum.AUDITING.getStatus());
                updateRelate.setVideoGroupId(req.getId());
                updateRelate.setAppId(appId);
                videoGroupWxRelateService.updateByVideoGroupIdAndAppId(updateRelate);
            }
        }

        updateGroup.setId(req.getId());
        updateGroup.setOperator(user.getUserName());
        Boolean success = videoGroupService.updateById(updateGroup);

        if (req.getPlatforms().contains(PlayletPlatformEnum.DOU_YIN.getPlatform()) && CollectionUtils.isNotEmpty(req.getDyAppIds())) {
            //异步送审
            douyinService.auditDyVideo(req.getId(), req.getDyAppIds());
        }

        return ResultBuilder.success(success);
    }

    private boolean checkWxAppIds(List<String> wxAppIds) {
        if (CollectionUtils.isEmpty(wxAppIds)) {
            return false;
        }
        Set<String> appIdSet = new HashSet<>();
        appIdSet.addAll(appIdList);
        // 小和多多
        appIdSet.add(XIAO_HE_DUO_DUO_APP_ID);

        for (String wxAppId : wxAppIds) {
            if (!appIdSet.contains(wxAppId)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据微信剧目id查询剧集信息
     *
     * @param dramaId
     * @return
     */
    @GetMapping("getWxDramaInfo")
    public Result<List<WxVideoMediaIdVo>> getWxDramaInfo(@Valid @NotNull(message = "短剧id不能为空") Long dramaId, @Valid @NotNull(message = "短剧id不能为空") String appId) {

        if (appId.equals(XIAO_HE_DUO_DUO_APP_ID)) {
            VideoGroupEntity videoGroup = videoGroup = videoGroupService.selectByDramaId(dramaId);
            if (Objects.isNull(videoGroup)) {
                throw new BizRuntimeException(ErrorCode.VIDEO_NOT_EXIST);
            }
            List<VideoEntity> videoEntities = videoService.selectListByVideoGroupId(videoGroup.getId());
            List<WxVideoMediaIdVo> wxVideoMediaIdVos = BeanUtil.copyToList(videoEntities, WxVideoMediaIdVo.class);
            return ResultBuilder.success(wxVideoMediaIdVos);
        } else {
            VideoGroupWxRelateEntity videoGroupWxRelateEntity = videoGroupWxRelateService.selectByDramaId(dramaId);
            if (Objects.isNull(videoGroupWxRelateEntity)) {
                throw new BizRuntimeException(ErrorCode.VIDEO_NOT_EXIST);
            }
            List<VideoWxRelateEntity> videoWxRelateEntities = videoWxRelateService.selectListByVideoGroupIdAndAppId(videoGroupWxRelateEntity.getVideoGroupId(), appId);

            List<Long> videoIds = videoWxRelateEntities.stream().map(VideoWxRelateEntity::getVideoId).collect(Collectors.toList());
            List<VideoEntity> videoEntities = videoService.selectListByIds(videoIds);
            if (CollectionUtils.isEmpty(videoEntities)) {
                throw new BizRuntimeException(ErrorCode.VIDEO_NOT_EXIST);
            }
            Map<Long, Integer> videoIdAndEpisode = videoEntities.stream().collect(Collectors.toMap(VideoEntity::getId, VideoEntity::getEpisode));
            List<WxVideoMediaIdVo> wxVideoMediaIdVos = new ArrayList<WxVideoMediaIdVo>();

            for (VideoWxRelateEntity videoWxRelateEntity : videoWxRelateEntities) {
                WxVideoMediaIdVo vo = new WxVideoMediaIdVo();
                vo.setMediaId(videoWxRelateEntity.getMediaId());
                vo.setEpisode(videoIdAndEpisode.get(videoWxRelateEntity.getVideoId()));
                wxVideoMediaIdVos.add(vo);
            }

            return ResultBuilder.success(wxVideoMediaIdVos);
        }
    }

    /**
     * 抖音短剧手动上线
     */
    @PostMapping("online")
    public Result<Boolean> online(@RequestBody DyOnlineReq req) {
        VideoGroupDyRelateEntity dyRelateEntities = videoGroupDyRelateService.selectByDyAlbumId(Long.valueOf(req.getAlbumId()));
        if (Objects.isNull(dyRelateEntities)) {
            return ResultBuilder.fail("无效短剧id");
        }
        VideoGroupEntity groupEntity = videoGroupService.selectById(dyRelateEntities.getVideoGroupId());
        if (Objects.isNull(groupEntity)) {
            return ResultBuilder.fail("短剧不存在");
        }
        boolean online = douyinService.online(dyRelateEntities, groupEntity, req.getOnline());
        if (online) {
            //更新状态
            dyRelateEntities.setDyOnlineStatus(req.getOnline());
            videoGroupDyRelateService.updateById(dyRelateEntities);
        }
        return ResultBuilder.success(online);
    }

    /**
     * 获取所有剧集视频地址
     *
     * @param wxVid 微信剧目id
     * @param dyVid 抖音剧目id
     */
    @GetMapping("getVideoUrls")
    public Result<List<VideoSyncInfoVo>> getVideoUrls(Long wxVid, Long dyVid) {
        Long videoGroupId = 0L;
        if (NumberUtils.isNotNullOrLteZero(wxVid)) {
            VideoGroupEntity videoGroupEntity = videoGroupService.selectByDramaId(wxVid);
            if (Objects.isNull(videoGroupEntity)) {
                return ResultBuilder.success(Collections.emptyList());
            }
            videoGroupId = videoGroupEntity.getId();
        } else if (NumberUtils.isNotNullOrLteZero(dyVid)) {
            VideoGroupDyRelateEntity groupDyRelateEntity = videoGroupDyRelateService.selectByDyAlbumId(dyVid);
            if (Objects.isNull(groupDyRelateEntity)) {
                return ResultBuilder.success(Collections.emptyList());
            }
            videoGroupId = groupDyRelateEntity.getVideoGroupId();
        } else {
            return ResultBuilder.success(Collections.emptyList());
        }

        List<VideoEntity> videoEntities = videoService.selectListByVideoGroupId(videoGroupId);
        return ResultBuilder.success(videoEntities.stream().sorted(Comparator.comparing(VideoEntity::getEpisode)).map(videoEntity -> {
            VideoSyncInfoVo vo = new VideoSyncInfoVo();
            vo.setVideoUrl(videoEntity.getVideoUrl());
            return vo;
        }).collect(Collectors.toList()));


    }

    /**
     * 临时使用 修改微信剧目成本图
     *
     * @return
     */
    @Anonymous
    @GetMapping("modifydramabasicinfo")
    public Result<Boolean> modifyDramaBasicInfo(Long videoId) throws Exception {
        VideoGroupEntity videoGroup = videoGroupService.selectById(videoId);
        Integer productionCost = videoGroup.getProductionCost();
        String costCommitmentLetterMaterialUri = videoGroup.getCostCommitmentLetterMaterialUri();
        if (NumberUtils.isNullOrLteZero(videoId) || NumberUtils.isNullOrLteZero(productionCost) || StringUtils.isBlank(costCommitmentLetterMaterialUri)) {
            return ResultBuilder.fail("必填参数不能为空");
        }
        wxDramaService.modifyDramaBasicInfo(videoId);
        return ResultBuilder.success();
    }

}
