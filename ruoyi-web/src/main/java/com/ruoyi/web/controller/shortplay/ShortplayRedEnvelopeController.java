package com.ruoyi.web.controller.shortplay;

import cn.hutool.core.convert.Convert;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.RequestUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.shortplay.RewardStageBo;
import com.ruoyi.system.vo.shortplay.RedEnvelopeStatusVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 短剧小程序红包接口
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/shortplay/redenvelope")
public class ShortplayRedEnvelopeController {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    // 红包阶梯规则2
    private static final List<RewardStageBo> REWARD_STAGES = new ArrayList<>();

    static {
        REWARD_STAGES.add(new RewardStageBo("25.39", 10));
        REWARD_STAGES.add(new RewardStageBo("30.55", 10));
        REWARD_STAGES.add(new RewardStageBo("32.55", 10));
        REWARD_STAGES.add(new RewardStageBo("34.55", 10));
        REWARD_STAGES.add(new RewardStageBo("36.55", 10));
        REWARD_STAGES.add(new RewardStageBo("38.55", 10));
        REWARD_STAGES.add(new RewardStageBo("40.55", 10));
        REWARD_STAGES.add(new RewardStageBo("42.55", 10));
        REWARD_STAGES.add(new RewardStageBo("44.55", 10));
        REWARD_STAGES.add(new RewardStageBo("46.55", 10));
        REWARD_STAGES.add(new RewardStageBo("48.55", 15));
        REWARD_STAGES.add(new RewardStageBo("49.55", 15));
        REWARD_STAGES.add(new RewardStageBo("50.55", 15));
        REWARD_STAGES.add(new RewardStageBo("51.55", 15));
        REWARD_STAGES.add(new RewardStageBo("52.55", 15));
        REWARD_STAGES.add(new RewardStageBo("53.55", 15));
        REWARD_STAGES.add(new RewardStageBo("54.55", 20));
        REWARD_STAGES.add(new RewardStageBo("55.55", 20));
        REWARD_STAGES.add(new RewardStageBo("56.55", 20));
        REWARD_STAGES.add(new RewardStageBo("57.55", 20));
        REWARD_STAGES.add(new RewardStageBo("58.55", 20));
        REWARD_STAGES.add(new RewardStageBo("59.55", 20));
        REWARD_STAGES.add(new RewardStageBo("60.55", 20));
        REWARD_STAGES.add(new RewardStageBo("61.55", 20));
        REWARD_STAGES.add(new RewardStageBo("62.55", 20));
        REWARD_STAGES.add(new RewardStageBo("63.55", 20));
        REWARD_STAGES.add(new RewardStageBo("64.55", 20));
        REWARD_STAGES.add(new RewardStageBo("65.55", 20));
        REWARD_STAGES.add(new RewardStageBo("66.55", 20));
        REWARD_STAGES.add(new RewardStageBo("67.55", 20));
        REWARD_STAGES.add(new RewardStageBo("68.55", 20));
        REWARD_STAGES.add(new RewardStageBo("69.55", 20));
        REWARD_STAGES.add(new RewardStageBo("70.55", 20));
        REWARD_STAGES.add(new RewardStageBo("71.55", 20));
        REWARD_STAGES.add(new RewardStageBo("72.55", 20));
        REWARD_STAGES.add(new RewardStageBo("73.55", 20));
        REWARD_STAGES.add(new RewardStageBo("74.55", 20));
        REWARD_STAGES.add(new RewardStageBo("75.55", 20));
        REWARD_STAGES.add(new RewardStageBo("76.55", 20));
        REWARD_STAGES.add(new RewardStageBo("77.55", 20));
        REWARD_STAGES.add(new RewardStageBo("78.55", 20));
        REWARD_STAGES.add(new RewardStageBo("79.55", 20));
        REWARD_STAGES.add(new RewardStageBo("80.55", 20));
        REWARD_STAGES.add(new RewardStageBo("81.55", 20));
        REWARD_STAGES.add(new RewardStageBo("82.55", 20));
        REWARD_STAGES.add(new RewardStageBo("83.55", 20));
        REWARD_STAGES.add(new RewardStageBo("84.55", 20));
        REWARD_STAGES.add(new RewardStageBo("85.55", 20));
        REWARD_STAGES.add(new RewardStageBo("86.55", 20));
        REWARD_STAGES.add(new RewardStageBo("87.55", 20));
        REWARD_STAGES.add(new RewardStageBo("88.55", 20));
        REWARD_STAGES.add(new RewardStageBo("89.55", 20));
        REWARD_STAGES.add(new RewardStageBo("90.55", 20));
        REWARD_STAGES.add(new RewardStageBo("91.55", 20));
        REWARD_STAGES.add(new RewardStageBo("92.55", 20));
        REWARD_STAGES.add(new RewardStageBo("93.55", 20));
        REWARD_STAGES.add(new RewardStageBo("94.55", 20));
        REWARD_STAGES.add(new RewardStageBo("95.55", 20));
        REWARD_STAGES.add(new RewardStageBo("96.55", 20));
        REWARD_STAGES.add(new RewardStageBo("97.55", 20));
        REWARD_STAGES.add(new RewardStageBo("98.55", 20));
        REWARD_STAGES.add(new RewardStageBo("99.55", 0));
    }

    /**
     * 获取用户红包状态
     * @param userId 用户ID
     * @return 红包状态
     */
    @GetMapping("/status")
    public Result<RedEnvelopeStatusVo> getStatus(String userId, HttpServletRequest request) {
        if ( StringUtils.isBlank(userId) ) {
            String userAgent = RequestUtils.getUserAgent(request);
            log.warn("getStatus接口userId为空，请求UA: {}", userAgent);
            return ResultBuilder.fail("参数错误");
        }
        String redisKey = RedisKeyFactory.K082.join(userId);
        Long stageLong = redisAtomicClient.getLong(redisKey);
        int stage = stageLong == null ? 0 : stageLong.intValue();

        if (stage >= REWARD_STAGES.size()) {
            stage = REWARD_STAGES.size() - 1;
        }

        RewardStageBo currentStageData = REWARD_STAGES.get(stage);
        RedEnvelopeStatusVo vo = new RedEnvelopeStatusVo(currentStageData.getCumulativeAmount(), currentStageData.getNextIntervalSeconds(), stage);
        return ResultBuilder.success(vo);
    }

    /**
     * 增加用户红包金额
     * @param userId 用户ID
     * @return 更新后的红包状态
     */
    @PostMapping("/increase")
    public Result<RedEnvelopeStatusVo> increase(String userId, HttpServletRequest request) {
        if ( StringUtils.isBlank(userId) ) {
            String userAgent = RequestUtils.getUserAgent(request);
            log.warn("increase接口userId为空，请求UA: {}", userAgent);
            return ResultBuilder.fail("参数错误");
        }
        String redisKey = RedisKeyFactory.K082.join(userId);

        Date now = DateUtils.getNowDate();
        Date startOfTomorrow = DateUtils.addDays(DateUtils.getStartOfDay(now), 1);
        long secondsUntilEndOfDay = DateUtils.differentSecondsByMillisecond(now, startOfTomorrow);

        long newStageLong = redisAtomicClient.incrBy(redisKey, 1, secondsUntilEndOfDay, TimeUnit.SECONDS);
        int newStage = Convert.toInt(newStageLong);

        if (newStage >= REWARD_STAGES.size()) {
            newStage = REWARD_STAGES.size() - 1;
        }

        RewardStageBo newStageData = REWARD_STAGES.get(newStage);
        RedEnvelopeStatusVo vo = new RedEnvelopeStatusVo(newStageData.getCumulativeAmount(), newStageData.getNextIntervalSeconds(), newStage);
        return ResultBuilder.success(vo);
    }
}
