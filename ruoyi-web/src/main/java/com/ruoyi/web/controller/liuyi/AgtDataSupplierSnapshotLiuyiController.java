package com.ruoyi.web.controller.liuyi;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.liuyi.AgtDataSupplierSnapshotLiuyiEntity;
import com.ruoyi.system.service.liuyi.AgtDataSupplierSnapshotLiuyiService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 六翼快照Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/liuyi/agtDataSupplierSnapshotLiuyi")
public class AgtDataSupplierSnapshotLiuyiController {
    @Autowired
    private AgtDataSupplierSnapshotLiuyiService agtDataSupplierSnapshotLiuyiService;

    /**
     * 查询六翼快照列表
     */
    @PreAuthorize("@ss.hasPermi('liuyi:agtDataSupplierSnapshotLiuyi:list')")
    @GetMapping("/list")
    public PageResult<AgtDataSupplierSnapshotLiuyiEntity> list(AgtDataSupplierSnapshotLiuyiEntity agtDataSupplierSnapshotLiuyi) {
        TableSupport.startPage();
        List<AgtDataSupplierSnapshotLiuyiEntity> list = agtDataSupplierSnapshotLiuyiService.selectAgtDataSupplierSnapshotLiuyiList(agtDataSupplierSnapshotLiuyi);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出六翼快照列表
     */
    @PreAuthorize("@ss.hasPermi('liuyi:agtDataSupplierSnapshotLiuyi:export')")
    @Log(title = "六翼快照", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtDataSupplierSnapshotLiuyiEntity agtDataSupplierSnapshotLiuyi) {
        List<AgtDataSupplierSnapshotLiuyiEntity> list = agtDataSupplierSnapshotLiuyiService.selectAgtDataSupplierSnapshotLiuyiList(agtDataSupplierSnapshotLiuyi);
        ExcelUtil<AgtDataSupplierSnapshotLiuyiEntity> util = new ExcelUtil<AgtDataSupplierSnapshotLiuyiEntity>(AgtDataSupplierSnapshotLiuyiEntity.class);
        util.exportExcel(response, list, "六翼快照数据");
    }

    /**
     * 获取六翼快照详细信息
     */
    @PreAuthorize("@ss.hasPermi('liuyi:agtDataSupplierSnapshotLiuyi:query')")
    @GetMapping(value = "/{id}")
    public Result<AgtDataSupplierSnapshotLiuyiEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(agtDataSupplierSnapshotLiuyiService.selectAgtDataSupplierSnapshotLiuyiById(id));
    }

    /**
     * 新增六翼快照
     */
    @PreAuthorize("@ss.hasPermi('liuyi:agtDataSupplierSnapshotLiuyi:add')")
    @Log(title = "六翼快照", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody AgtDataSupplierSnapshotLiuyiEntity agtDataSupplierSnapshotLiuyi) {
        return ResultBuilder.success(agtDataSupplierSnapshotLiuyiService.insertAgtDataSupplierSnapshotLiuyi(agtDataSupplierSnapshotLiuyi));
    }

    /**
     * 修改六翼快照
     */
    @PreAuthorize("@ss.hasPermi('liuyi:agtDataSupplierSnapshotLiuyi:edit')")
    @Log(title = "六翼快照", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody AgtDataSupplierSnapshotLiuyiEntity agtDataSupplierSnapshotLiuyi) {
        return ResultBuilder.success(agtDataSupplierSnapshotLiuyiService.updateAgtDataSupplierSnapshotLiuyi(agtDataSupplierSnapshotLiuyi));
    }

    /**
     * 删除六翼快照
     */
    @PreAuthorize("@ss.hasPermi('liuyi:agtDataSupplierSnapshotLiuyi:remove')")
    @Log(title = "六翼快照", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(agtDataSupplierSnapshotLiuyiService.deleteAgtDataSupplierSnapshotLiuyiByIds(ids));
    }
}
