package com.ruoyi.web.controller.novel.manage;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.novel.NovelChapterEntity;
import com.ruoyi.system.service.novel.NovelChapterService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 小说章节Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/novel/novelChapter")
public class NovelChapterController {
    @Autowired
    private NovelChapterService novelChapterService;

    /**
     * 查询小说章节列表
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChapter:list')")
    @GetMapping("/list")
    public PageResult<NovelChapterEntity> list(NovelChapterEntity novelChapter) {
        TableSupport.startPage();
        List<NovelChapterEntity> list = novelChapterService.selectNovelChapterList(novelChapter);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出小说章节列表
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChapter:export')")
    @Log(title = "小说章节", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NovelChapterEntity novelChapter) {
        List<NovelChapterEntity> list = novelChapterService.selectNovelChapterList(novelChapter);
        ExcelUtil<NovelChapterEntity> util = new ExcelUtil<NovelChapterEntity>(NovelChapterEntity.class);
        util.exportExcel(response, list, "小说章节数据");
    }

    /**
     * 获取小说章节详细信息
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChapter:query')")
    @GetMapping(value = "/{id}")
    public Result<NovelChapterEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(novelChapterService.selectNovelChapterById(id));
    }

    /**
     * 新增小说章节
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChapter:add')")
    @Log(title = "小说章节", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody NovelChapterEntity novelChapter) {
        return ResultBuilder.success(novelChapterService.insertNovelChapter(novelChapter));
    }

    /**
     * 修改小说章节
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChapter:edit')")
    @Log(title = "小说章节", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody NovelChapterEntity novelChapter) {
        return ResultBuilder.success(novelChapterService.updateNovelChapter(novelChapter));
    }

    /**
     * 删除小说章节
     */
    @PreAuthorize("@ss.hasPermi('novel:novelChapter:remove')")
    @Log(title = "小说章节", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(novelChapterService.deleteNovelChapterByIds(ids));
    }
}
