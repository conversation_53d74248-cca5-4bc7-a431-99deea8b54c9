package com.ruoyi.web.controller.playlet.fanqie;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.fanqie.AgtDataSupplierSnapshotFanqieEntity;
import com.ruoyi.system.service.fanqie.AgtDataSupplierSnapshotFanqieService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 番茄快照Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agtdata/fanqie")
public class AgtDataSupplierSnapshotFanqieController {
    @Autowired
    private AgtDataSupplierSnapshotFanqieService agtDataSupplierSnapshotFanqieService;

    /**
     * 查询番茄快照列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fanqie:list')")
    @GetMapping("/list")
    public PageResult<AgtDataSupplierSnapshotFanqieEntity> list(AgtDataSupplierSnapshotFanqieEntity agtDataSupplierSnapshotFanqie) {
        TableSupport.startPage();
        List<AgtDataSupplierSnapshotFanqieEntity> list = agtDataSupplierSnapshotFanqieService.selectAgtDataSupplierSnapshotFanqieList(agtDataSupplierSnapshotFanqie);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出番茄快照列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fanqie:export')")
    @Log(title = "番茄快照", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtDataSupplierSnapshotFanqieEntity agtDataSupplierSnapshotFanqie) {
        List<AgtDataSupplierSnapshotFanqieEntity> list = agtDataSupplierSnapshotFanqieService.selectAgtDataSupplierSnapshotFanqieList(agtDataSupplierSnapshotFanqie);
        ExcelUtil<AgtDataSupplierSnapshotFanqieEntity> util = new ExcelUtil<AgtDataSupplierSnapshotFanqieEntity>(AgtDataSupplierSnapshotFanqieEntity.class);
        util.exportExcel(response, list, "番茄快照数据");
    }

    /**
     * 获取番茄快照详细信息
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fanqie:query')")
    @GetMapping(value = "/{id}")
    public Result<AgtDataSupplierSnapshotFanqieEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(agtDataSupplierSnapshotFanqieService.selectAgtDataSupplierSnapshotFanqieById(id));
    }

    /**
     * 新增番茄快照
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fanqie:add')")
    @Log(title = "番茄快照", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody AgtDataSupplierSnapshotFanqieEntity agtDataSupplierSnapshotFanqie) {
        return ResultBuilder.success(agtDataSupplierSnapshotFanqieService.insertAgtDataSupplierSnapshotFanqie(agtDataSupplierSnapshotFanqie));
    }

    /**
     * 修改番茄快照
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fanqie:edit')")
    @Log(title = "番茄快照", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody AgtDataSupplierSnapshotFanqieEntity agtDataSupplierSnapshotFanqie) {
        return ResultBuilder.success(agtDataSupplierSnapshotFanqieService.updateAgtDataSupplierSnapshotFanqie(agtDataSupplierSnapshotFanqie));
    }

    /**
     * 删除番茄快照
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fanqie:remove')")
    @Log(title = "番茄快照", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(agtDataSupplierSnapshotFanqieService.deleteAgtDataSupplierSnapshotFanqieByIds(ids));
    }
}
