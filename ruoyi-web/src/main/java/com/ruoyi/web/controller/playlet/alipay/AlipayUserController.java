package com.ruoyi.web.controller.playlet.alipay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.ChargeStatusEnum;
import com.ruoyi.common.enums.ChargeTypeEnum;
import com.ruoyi.common.enums.LoginPlatformEnum;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.charge.ChargeRecordEntity;
import com.ruoyi.system.entity.user.UserEntity;
import com.ruoyi.system.service.charge.ChargeRecordService;
import com.ruoyi.system.service.user.UserService;
import com.ruoyi.system.req.playlet.param.ChargeRecordParam;
import com.ruoyi.system.req.playlet.user.ChargeRecordReq;
import com.ruoyi.system.req.playlet.user.UserSearchReq;
import com.ruoyi.system.vo.playlet.alipay.AlipayChargeRecordSumVo;
import com.ruoyi.system.vo.playlet.alipay.AlipayChargeRecordVo;
import com.ruoyi.system.vo.playlet.alipay.AlipayUserVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户管理(域外H5)
 *
 * <AUTHOR>
 * @date 2023/9/5
 */
@RestController
@RequestMapping("/alipay/user")
public class AlipayUserController {

    @Autowired
    private UserService userService;

    @Autowired
    private ChargeRecordService chargeRecordService;

    /**
     * 用户列表
     */
    @GetMapping("list")
    public PageResult<AlipayUserVo> list(UserSearchReq req) {
        if (Objects.nonNull(req.getEndDate())) {
            req.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }

        req.setPlatformTypes(Collections.singletonList(LoginPlatformEnum.ALIPAY_H5.getType()));
        TableSupport.startPage();
        List<UserEntity> userEntities = userService.selectListByReq(req);
        List<Long> userIds = userEntities.stream().map(UserEntity::getId).collect(Collectors.toList());
        Map<Long, Integer> chargeSumMap = chargeRecordService.selectChargeSum(userIds);

        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(userEntities, entity -> {
            AlipayUserVo alipayUserVo = BeanUtil.copyProperties(entity, AlipayUserVo.class);
            alipayUserVo.setChargeSum(chargeSumMap.getOrDefault(entity.getId(),0));
            return alipayUserVo;
        }));
    }

    /**
     * 充值记录
     */
    @GetMapping("chargeRecord")
    public PageResult<AlipayChargeRecordVo> chargeRecord(ChargeRecordReq req) {
        ChargeRecordParam param = BeanUtil.copyProperties(req, ChargeRecordParam.class);
        if (StringUtils.isNotBlank(req.getNickname())) {
            List<Long> userIds = userService.selectUserIdsByNickname(req.getNickname());
            if (CollectionUtils.isEmpty(userIds)) {
                return ResultBuilder.successPage(PageInfo.emptyPageInfo());
            }
            param.setUserIds(userIds);
        }
        if (null != req.getChargeType()) {
            param.setChargeTypes(Collections.singletonList(req.getChargeType()));
        } else {
            param.setChargeTypes(Arrays.asList(ChargeTypeEnum.ALIPAY_H5_SUB.getType(), ChargeTypeEnum.ALIPAY_H5_VIP.getType()));
        }
        TableSupport.startPage();
        List<ChargeRecordEntity> chargeRecordEntityList = chargeRecordService.selectListByReq(param);
        List<Long> userIds = chargeRecordEntityList.stream().map(ChargeRecordEntity::getUserId).distinct().collect(Collectors.toList());
        Map<Long, String> userNameMap = userService.selectUserNameMap(userIds);

        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(chargeRecordEntityList, entity -> {
            AlipayChargeRecordVo vo = BeanUtil.copyProperties(entity, AlipayChargeRecordVo.class);
            vo.setNickname(userNameMap.getOrDefault(entity.getUserId(),""));

            return vo;
        }));
    }

    /**
     * 充值记录总额
     */
    @GetMapping("chargeRecordSum")
    public Result<AlipayChargeRecordSumVo> chargeRecordSum(ChargeRecordReq req) {
        ChargeRecordParam param = BeanUtil.copyProperties(req, ChargeRecordParam.class);
        param.setChargeStatus(ChargeStatusEnum.PAY_SUCCESS.getStatus());
        if (StringUtils.isNotBlank(req.getNickname())) {
            List<Long> userIds = userService.selectUserIdsByNickname(req.getNickname());
            if (CollectionUtils.isEmpty(userIds)) {
                return ResultBuilder.success(new AlipayChargeRecordSumVo());
            }
            param.setUserIds(userIds);
        }
        if (null != req.getChargeType()) {
            param.setChargeTypes(Collections.singletonList(req.getChargeType()));
        } else {
            param.setChargeTypes(Arrays.asList(ChargeTypeEnum.ALIPAY_H5_SUB.getType(), ChargeTypeEnum.ALIPAY_H5_VIP.getType()));
        }
        return ResultBuilder.success(BeanUtil.copyProperties(chargeRecordService.chargeRecordSum(param), AlipayChargeRecordSumVo.class));
    }
}
