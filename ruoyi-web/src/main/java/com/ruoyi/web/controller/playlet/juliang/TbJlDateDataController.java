package com.ruoyi.web.controller.playlet.juliang;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.douyin.TbJlAccountAdvertiser;
import com.ruoyi.system.entity.douyin.TbJlDateData;
import com.ruoyi.system.service.douyin.ITbJlAccountAdvertiserService;
import com.ruoyi.system.service.douyin.ITbJlDateDataService;
import com.ruoyi.system.vo.data.JlDateDataVo;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 巨量广告每日数据Controller
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
@RestController
@RequestMapping("/juliang/data")
public class TbJlDateDataController extends BaseController {
    @Autowired
    private ITbJlDateDataService tbJlDateDataService;
    @Autowired
    private ITbJlAccountAdvertiserService accountAdvertiserService;

    /**
     * 查询巨量广告每日数据列表
     */
    @PreAuthorize("@ss.hasPermi('juliang:data:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbJlDateData tbJlDateData) {
        PageInfo<JlDateDataVo> pageInfo = getTbJlAccountAdvertiserPageInfo(tbJlDateData,false);
        return getDataTable(pageInfo);
    }

    @NotNull
    private PageInfo<JlDateDataVo> getTbJlAccountAdvertiserPageInfo(TbJlDateData tbJlDateData,boolean export) {
        if(!export){
            startPage();
        }
        List<TbJlDateData> list = tbJlDateDataService.selectTbJlDateDataList(tbJlDateData);
        List<String> advertiserIds = list.stream().map(TbJlDateData::getAdvertiserId).collect(Collectors.toList());
        List<TbJlAccountAdvertiser> advertisers = accountAdvertiserService.selectListByAdvertiserIds(advertiserIds);
        Map<String, TbJlAccountAdvertiser> advertiserMap = advertisers.stream().collect(Collectors.toMap(TbJlAccountAdvertiser::getAdvertiserId, Function.identity(), (v1, v2) -> v1));
        PageInfo<JlDateDataVo> pageInfo = PageInfoUtils.dto2Vo(list, data -> {
            JlDateDataVo jlDateDataVo = BeanUtil.copyProperties(data, JlDateDataVo.class);
            jlDateDataVo.setStatCostStr(NumberUtils.fenToYuan(data.getStatCost()));
            jlDateDataVo.setConvertCostStr(NumberUtils.fenToYuan(data.getConvertCost()));
            jlDateDataVo.setAdIncomeStr(NumberUtils.fenToYuan(data.getTodayAdIncome()));
            jlDateDataVo.setFirstDayRoi(NumberUtils.calculateRate(data.getFirstDayAdIncome(),data.getStatCost(),"-")); // 首日ROI = 首日广告收入/首日广告消耗
            jlDateDataVo.setTodayRoi(NumberUtils.calculateRate(data.getTodayAdIncome(),data.getStatCost(),"-")); // 今日ROI = 广告收入/广告消耗
            jlDateDataVo.setCashRoi(NumberUtils.calculateRate((double)data.getTodayAdIncome(),(double)(data.getStatCost()*0.7),"-"));// 现金ROI = 广告收入/0.7/广告消耗
            jlDateDataVo.setProfit(NumberUtils.fenToYuan(Double.parseDouble(NumberUtils.calculateRate((double)data.getTodayAdIncome(),0.7,"0")) - data.getStatCost())); // 毛利 = 广告收入/0.7 - 广告消耗
            jlDateDataVo.setFirstDayIpu(NumberUtils.calculateRate(data.getAdWatchPv(),data.getAdWatchUv(),"-"));
            jlDateDataVo.setFirstDayArpu(NumberUtils.calculateRate(data.getFirstDayAdIncome(),data.getAdWatchUv(),"-"));
            jlDateDataVo.setFirstDayEcpm(NumberUtils.calculateRate(data.getStatCost() * 10,data.getShowCnt(),"-")); //ecpm = 广告消耗/展示数*1000/100(元)
            TbJlAccountAdvertiser advertiser = advertiserMap.get(data.getAdvertiserId());
            if (advertiser != null) {
                jlDateDataVo.setAdvertiserName(advertiser.getAdvertiserName());
            }
            return jlDateDataVo;
        });
        return pageInfo;
    }

    /**
     * 导出巨量广告每日数据列表
     */
    @PreAuthorize("@ss.hasPermi('juliang:data:export')")
    @Log(title = "巨量广告每日数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbJlDateData tbJlDateData) {
        List<TbJlDateData> list = tbJlDateDataService.selectTbJlDateDataList(tbJlDateData);
        ExcelUtil<TbJlDateData> util = new ExcelUtil<TbJlDateData>(TbJlDateData.class);
        util.exportExcel(response, list, "巨量广告每日数据数据");
    }

    /**
     * 获取巨量广告每日数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('juliang:data:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(tbJlDateDataService.selectTbJlDateDataById(id));
    }

    /**
     * 新增巨量广告每日数据
     */
    @PreAuthorize("@ss.hasPermi('juliang:data:add')")
    @Log(title = "巨量广告每日数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbJlDateData tbJlDateData) {
        return toAjax(tbJlDateDataService.insertTbJlDateData(tbJlDateData));
    }

    /**
     * 修改巨量广告每日数据
     */
    @PreAuthorize("@ss.hasPermi('juliang:data:edit')")
    @Log(title = "巨量广告每日数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbJlDateData tbJlDateData) {
        return toAjax(tbJlDateDataService.updateTbJlDateData(tbJlDateData));
    }

    /**
     * 删除巨量广告每日数据
     */
    @PreAuthorize("@ss.hasPermi('juliang:data:remove')")
    @Log(title = "巨量广告每日数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(tbJlDateDataService.deleteTbJlDateDataByIds(ids));
    }
}
