package com.ruoyi.web.controller.playlet.material;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.material.AgtDataMaterialEntity;
import com.ruoyi.system.service.material.AgtDataMaterialService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 巨量视频素材Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agtdata/material")
public class AgtDataMaterialController {
    @Autowired
    private AgtDataMaterialService agtDataMaterialService;

    /**
     * 查询巨量视频素材列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:material:list')")
    @GetMapping("/list")
    public PageResult<AgtDataMaterialEntity> list(AgtDataMaterialEntity agtDataMaterial) {
        TableSupport.startPage();
        List<AgtDataMaterialEntity> list = agtDataMaterialService.selectAgtDataMaterialList(agtDataMaterial);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出巨量视频素材列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:material:export')")
    @Log(title = "巨量视频素材", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtDataMaterialEntity agtDataMaterial) {
        List<AgtDataMaterialEntity> list = agtDataMaterialService.selectAgtDataMaterialList(agtDataMaterial);
        ExcelUtil<AgtDataMaterialEntity> util = new ExcelUtil<AgtDataMaterialEntity>(AgtDataMaterialEntity.class);
        util.exportExcel(response, list, "巨量视频素材数据");
    }

    /**
     * 获取巨量视频素材详细信息
     */
    @PreAuthorize("@ss.hasPermi('agtdata:material:query')")
    @GetMapping(value = "/{id}")
    public Result<AgtDataMaterialEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(agtDataMaterialService.selectAgtDataMaterialById(id));
    }

    /**
     * 新增巨量视频素材
     */
    @PreAuthorize("@ss.hasPermi('agtdata:material:add')")
    @Log(title = "巨量视频素材", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody AgtDataMaterialEntity agtDataMaterial) {
        return ResultBuilder.success(agtDataMaterialService.insertAgtDataMaterial(agtDataMaterial));
    }

    /**
     * 修改巨量视频素材
     */
    @PreAuthorize("@ss.hasPermi('agtdata:material:edit')")
    @Log(title = "巨量视频素材", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody AgtDataMaterialEntity agtDataMaterial) {
        return ResultBuilder.success(agtDataMaterialService.updateAgtDataMaterial(agtDataMaterial));
    }

    /**
     * 删除巨量视频素材
     */
    @PreAuthorize("@ss.hasPermi('agtdata:material:remove')")
    @Log(title = "巨量视频素材", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(agtDataMaterialService.deleteAgtDataMaterialByIds(ids));
    }
}
