package com.ruoyi.web.controller.playlet.fundStat;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity;
import com.ruoyi.system.entity.fundStat.FundQueryParam;
import com.ruoyi.system.entity.fundStat.FundVO;
import com.ruoyi.system.mapper.fundStat.AgtAdvertiserFundDailyStatSelfMapper;
import com.ruoyi.system.service.fundStat.AgtAdFundDailyStatSelfService;
import com.ruoyi.system.service.fundStat.AgtAdvertiserFundDailyStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: keboom
 * @date: 2024/7/25
 */
@RestController
@RequestMapping("/agtdata/fundStat")
public class AgtAdvertiserFundDailyStatSelfController {

    @Autowired
    private AgtAdFundDailyStatSelfService statSelfService;

    /**
     * 查询广告账户日流水列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fundStat:detail')")
    @PostMapping("/dataDetail")
    public PageResult<FundVO> list(@RequestBody FundQueryParam param) {
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        if (!DateUtils.isValidDate(startDate, DateUtils.YYYY_MM_DD) || !DateUtils.isValidDate(endDate, DateUtils.YYYY_MM_DD)) {
            throw new ServiceException("日期格式错误");
        }
        TableSupport.startPage();
        PageInfo<FundVO> list = statSelfService.selectFundDailyStatList(startDate, endDate);

        return ResultBuilder.successPage(list);
    }

    /**
     * 导出广告账户日流水列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fundStat:export')")
    @Log(title = "广告账户日流水", businessType = BusinessType.EXPORT)
    @PostMapping("/dataDetail/export")
    public void export(HttpServletResponse response, @RequestBody FundQueryParam param) {
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        if (!DateUtils.isValidDate(startDate, DateUtils.YYYY_MM_DD) || !DateUtils.isValidDate(endDate, DateUtils.YYYY_MM_DD)) {
            throw new ServiceException("日期格式错误");
        }

        PageInfo<FundVO> list = statSelfService.selectFundDailyStatList(startDate, endDate);
        List<FundVO> voList = list.getList();
        ExcelUtil<FundVO> util = new ExcelUtil<FundVO>(FundVO.class);
        util.exportExcel(response, voList, "广告账户日流水数据");
    }
}
