package com.ruoyi.web.controller.playlet.callback;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.dyiaa.JuLiangService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 巨量回调
 * <AUTHOR>
 * @date 2024/3/28 15:55
 */
@Slf4j
@RequestMapping("juliang/callback")
@RestController
public class JuliangCallbackController {
    @Autowired
    private JuLiangService juLiangService;
    /**
     * 授权回调
     * http://xxxx/api/juliang/callback/auth?appId=xxx
     * 注意：创建应用时，必须要选择的权限：广告主信息/数据报表/纵横组织管理
     * 如果权限通过，但是调用接口时还是提示没有权限，则需要重新点击授权链接授权
     */
    @GetMapping("auth")
    public void auth(String auth_code,String state,String appId){
        if(StringUtils.isBlank(auth_code) || StringUtils.isBlank(appId)){
            log.error("巨量授权回调参数错误");
            return;
        }
        log.info("巨量授权回调参数auth_code:{},state:{},appId:{}",auth_code,state,appId);
        //如果accessToken失效，则需要到巨量开发者后台找到对应应用重新授权，有多个应用的情况下，为了避免token设置冲突，所以state固定为appId,当申请应用时没有appId，所以state可以随便设置一个，审核通过后在修改为appId
        juLiangService.setAccessToken(auth_code,appId);
    }

    /**
     * 只有测试环境可以调用, 从生产环境复制过来到测试环境使用
     * @param accessToken
     */
    @GetMapping("setAccessToken")
    public void setAccessToken(String accessToken,String appId){
        juLiangService.setAccessTokenToRedis(accessToken,appId);
    }
}
