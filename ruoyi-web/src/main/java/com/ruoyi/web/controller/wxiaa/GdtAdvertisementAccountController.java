package com.ruoyi.web.controller.wxiaa;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.service.wxiaa.GdtAccountService;
import com.ruoyi.system.service.wxiaa.impl.GuangDianTongServiceImpl;
import com.ruoyi.system.vo.wxiaa.GdtAdvertisementAccountVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.wxiaa.GdtAdvertisementAccountEntity;
import com.ruoyi.system.service.wxiaa.GdtAdvertisementAccountService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 广点通广告账户Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wxiaa/gdtAdvertisementAccount")
public class GdtAdvertisementAccountController {
    @Autowired
    private GdtAdvertisementAccountService gdtAdvertisementAccountService;
    @Autowired
    private GdtAccountService gdtAccountService;

    /**
     * 查询广点通广告账户列表
     */
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtAdvertisementAccount:list')")
    @GetMapping("/list")
    public PageResult<GdtAdvertisementAccountVO> list(GdtAdvertisementAccountEntity gdtAdvertisementAccount) {
        TableSupport.startPage();
        List<GdtAdvertisementAccountEntity> list = gdtAdvertisementAccountService.selectGdtAdvertisementAccountList(gdtAdvertisementAccount);
        //查询组织名
        Map<Long, String> accountNameMap = gdtAccountService.selectAccountNameMap();

        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(list,entity->{
            GdtAdvertisementAccountVO vo = BeanUtil.copyProperties(entity, GdtAdvertisementAccountVO.class);
            vo.setAccountName(accountNameMap.getOrDefault(entity.getAccountId(),""));
            return vo;
        }));
    }

    /**
     * 导出广点通广告账户列表
     */
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtAdvertisementAccount:export')")
    @Log(title = "广点通广告账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GdtAdvertisementAccountEntity gdtAdvertisementAccount) {
        List<GdtAdvertisementAccountEntity> list = gdtAdvertisementAccountService.selectGdtAdvertisementAccountList(gdtAdvertisementAccount);
        ExcelUtil<GdtAdvertisementAccountEntity> util = new ExcelUtil<GdtAdvertisementAccountEntity>(GdtAdvertisementAccountEntity.class);
        util.exportExcel(response, list, "广点通广告账户数据");
    }

    /**
     * 获取广点通广告账户详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtAdvertisementAccount:query')")
    @GetMapping(value = "/{id}")
    public Result<GdtAdvertisementAccountEntity> getInfo(@PathVariable("id") String id) {
        return ResultBuilder.success(gdtAdvertisementAccountService.selectGdtAdvertisementAccountById(id));
    }

    /**
     * 新增广点通广告账户
     */
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtAdvertisementAccount:add')")
    @Log(title = "广点通广告账户", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody GdtAdvertisementAccountEntity gdtAdvertisementAccount) {
        return ResultBuilder.success(gdtAdvertisementAccountService.insertGdtAdvertisementAccount(gdtAdvertisementAccount));
    }

    /**
     * 修改广点通广告账户
     */
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtAdvertisementAccount:edit')")
    @Log(title = "广点通广告账户", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody GdtAdvertisementAccountEntity gdtAdvertisementAccount) {
        return ResultBuilder.success(gdtAdvertisementAccountService.updateGdtAdvertisementAccount(gdtAdvertisementAccount));
    }

    /**
     * 删除广点通广告账户
     */
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtAdvertisementAccount:remove')")
    @Log(title = "广点通广告账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable String[] ids) {
        return ResultBuilder.success(gdtAdvertisementAccountService.deleteGdtAdvertisementAccountByIds(ids));
    }

}
