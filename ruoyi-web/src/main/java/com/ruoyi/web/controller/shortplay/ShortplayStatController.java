package com.ruoyi.web.controller.shortplay;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.LuaScriptUtils;
import com.ruoyi.system.bo.shortplay.StayTimeReportRuleBo;
import com.ruoyi.system.entity.dyAdData.ShortplayDyAdDataEntity;
import com.ruoyi.system.entity.shortplay.config.ShortplayAgentSettingEntity;
import com.ruoyi.system.entity.shortplay.data.ShortplayActionDataCustomerEntity;
import com.ruoyi.system.entity.shortplay.data.ShortplayAdDataEntity;
import com.ruoyi.system.entity.shortplay.stat.GdtUserStayTimeEntity;
import com.ruoyi.system.entity.shortplay.stat.ShortplayGdtStatEntity;
import com.ruoyi.system.entity.shortplay.user.ShortplayUserEntity;
import com.ruoyi.system.req.stat.UserStayTimeReq;
import com.ruoyi.system.req.stat.StatAdDyReq;
import com.ruoyi.system.service.dyAdData.ShortplayDyAdDataService;
import com.ruoyi.system.service.ruoyi.ISysConfigService;
import com.ruoyi.system.service.shortplay.app.ShortplayAppService;
import com.ruoyi.system.service.shortplay.config.ShortplayAgentSettingService;
import com.ruoyi.system.service.shortplay.data.ShortplayActionDataCustomerService;
import com.ruoyi.system.service.shortplay.data.ShortplayAdDataService;
import com.ruoyi.system.service.shortplay.stat.ShortplayConvService;
import com.ruoyi.system.service.shortplay.stat.ShortplayGdtStatService;
import com.ruoyi.system.service.shortplay.tv.ShortplayTvService;
import com.ruoyi.system.service.shortplay.user.ShortplayUserService;
import com.ruoyi.system.service.wxiaa.GdtUserStayTimeService;
import com.ruoyi.web.controller.shortplay.ShortplayController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 短剧小程序埋点接口
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/shortplay/stat")
public class ShortplayStatController {

    public static ExecutorService statExecutor = new ThreadPoolExecutor(8, 8,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    private static final DefaultRedisScript<Long> deleteIfEqualsScript = LuaScriptUtils.getScript("delete_if_equals", Long.class);

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private ShortplayAdDataService shortplayAdDataService;

    @Autowired
    private ShortplayDyAdDataService shortplayDyAdDataService;

    @Autowired
    private ShortplayTvService shortplayTvService;

    @Autowired
    private ShortplayAppService shortplayAppService;

    @Autowired
    private ShortplayActionDataCustomerService shortplayActionDataCustomerService;

    @Autowired
    private GdtUserStayTimeService gdtUserStayTimeService;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ShortplayUserService shortplayUserService;

    @Autowired
    private ShortplayGdtStatService shortplayGdtStatService;

    @Autowired
    private ShortplayAgentSettingService shortplayAgentSettingService;

    @Autowired
    private ShortplayConvService shortplayConvService;

    @Autowired
    private ShortplayController shortplayController;

    @GetMapping("/threadMonitor")
    public JSONArray threadMonitor() {
        Map<String, ExecutorService> map = new HashMap<>();
        map.put("executorService", executorService);
        map.put("statExecutor", statExecutor);
        map.put("gdtExecutorService", GlobalThreadPool.gdtExecutorService);
        JSONArray arr = new JSONArray();
        map.forEach((k, v) -> {
            ThreadPoolExecutor te = (ThreadPoolExecutor) v;
            JSONObject jo = new JSONObject();
            jo.put("name", k);
            jo.put("PoolSize", te.getPoolSize());   // 初始线程数
            jo.put("CorePoolSize", te.getCorePoolSize());   // 核心线程数
            jo.put("Active", te.getActiveCount());  // 正在执行的任务数量
            jo.put("Queue", te.getQueue().size());  // 队列里缓存的任务数量
            jo.put("LargestPoolSize", te.getLargestPoolSize());     // 池中存在的最大线程数
            arr.add(jo);
        });
        return arr;
    }

    @CrossOrigin
    @RequestMapping("/action")
    public Result<Void> action(String appId, Long userId, Integer action) {
        if (null == userId || StringUtils.isBlank(appId) || null == action) {
            return ResultBuilder.fail("参数错误");
        }

        Date curDate = DateUtil.beginOfDay(new Date());
        String dateStr = DateUtil.formatDate(curDate);
        statExecutor.submit(() -> {
            Long dataId = getDataId(dateStr, appId);
            if (null == dataId) {
                dataId = shortplayActionDataCustomerService.selectIdBy(curDate, appId);
                cacheDataId(dateStr, appId, dataId);
            }
            if (null == dataId) {
                ShortplayActionDataCustomerEntity data = new ShortplayActionDataCustomerEntity();
                data.setCurDate(curDate);
                data.setMiniappId(appId);
                data.setMiniappName(shortplayAppService.getAppNameCache(appId));
                shortplayActionDataCustomerService.insert(data);
                dataId = shortplayActionDataCustomerService.selectIdBy(curDate, appId);
                cacheDataId(dateStr, appId, dataId);
            }
            if (null != dataId) {
                ShortplayActionDataCustomerEntity updateData = new ShortplayActionDataCustomerEntity();
                updateData.setId(dataId);
                switch (action) {
                    case 1:
                        updateData.setExposurePvAdd(1);
                        updateData.setExposureUvAdd(BizUtils.countUv(RedisKeyFactory.K025.join(dateStr, appId, action), String.valueOf(userId)));
                        break;
                    case 2:
                        updateData.setClickPvAdd(1);
                        updateData.setClickUvAdd(BizUtils.countUv(RedisKeyFactory.K025.join(dateStr, appId, action), String.valueOf(userId)));
                        break;
                    case 3:
                        updateData.setFocusPvAdd(1);
                        updateData.setFocusUvAdd(BizUtils.countUv(RedisKeyFactory.K025.join(dateStr, appId, action), String.valueOf(userId)));
                        break;
                    default:
                        return;
                }
                shortplayActionDataCustomerService.update(updateData);
            }
        });
        return ResultBuilder.success();
    }

    @CrossOrigin
    @RequestMapping("/ad")
    public Result<Void> adExposure(String appId, Long tvId, Integer series, Long userId, String dramaId) {
        if (null == userId || StringUtils.isBlank(appId) || null == series) {
            return ResultBuilder.fail("参数错误");
        }
        if (null == tvId && StringUtils.isNotBlank(dramaId)) {
            tvId = shortplayTvService.getTvIdByWxVideoId(dramaId);
        }

        Date curDate = DateUtil.beginOfDay(new Date());
        String dateStr = DateUtil.formatDate(curDate);
        Long fTvId = tvId;
        statExecutor.submit(() -> {
            Long dataId = getDataId(dateStr, appId, fTvId, series);
            if (null == dataId) {
                dataId = shortplayAdDataService.selectIdBy(curDate, appId, fTvId, series);
                cacheDataId(dateStr, appId, fTvId, series, dataId);
            }
            if (null == dataId) {
                ShortplayAdDataEntity record = new ShortplayAdDataEntity();
                record.setCurDate(curDate);
                record.setMiniappId(appId);
                record.setMiniappName(shortplayAppService.getAppNameCache(appId));
                record.setTvId(fTvId);
                record.setTvName(shortplayTvService.getTvNameCache(fTvId));
                record.setSeries(series);
                shortplayAdDataService.insert(record);
                dataId = shortplayAdDataService.selectIdBy(curDate, appId, fTvId, series);
                cacheDataId(dateStr, appId, fTvId, series, dataId);
            }
            if (null != dataId) {
                ShortplayAdDataEntity updateData = new ShortplayAdDataEntity();
                updateData.setId(dataId);
                updateData.setPvAdd(1);
                updateData.setUvAdd(BizUtils.countUv(RedisKeyFactory.K027.join(DateUtil.formatDate(curDate), appId, fTvId, series), String.valueOf(userId)));
                shortplayAdDataService.update(updateData);
            }
        });
        return ResultBuilder.success();
    }

    /**
     * 抖音小程序广告埋点
     * @return
     */
    @CrossOrigin
    @RequestMapping(value = "/adDy", method = RequestMethod.POST)
    public Result<Void> adExposure(@RequestBody @Validated StatAdDyReq req) {

        Date curDate = DateUtil.beginOfDay(new Date());
        String dateStr = DateUtil.formatDate(curDate);
        Long fTvId = req.getTvId();
        String appId = req.getAppId();
        Integer series = req.getSeq();
        Integer times = req.getTimes();

        statExecutor.submit(() -> {
            Long dataId = getDataIdDy(dateStr, appId, fTvId, series, times);
            if (null == dataId) {
                dataId = shortplayDyAdDataService.selectIdBy(curDate, appId, fTvId, series, times);
                cacheDataIdDy(dateStr, appId, fTvId, series, times, dataId);
            }
            if (null == dataId) {
                ShortplayDyAdDataEntity record = new ShortplayDyAdDataEntity();
                record.setCurDate(curDate);
                record.setAppId(appId);
                record.setAppName(req.getAppName());
                record.setTvId(fTvId);
                record.setTvName(req.getTvName());
                record.setSeq(series);
                record.setTimes(times);
                shortplayDyAdDataService.insertShortplayDyAdData(record);
                dataId = shortplayDyAdDataService.selectIdBy(curDate, appId, fTvId, series, times);
                cacheDataIdDy(dateStr, appId, fTvId, series, times, dataId);
            }
            if (null != dataId) {
                if (req.getType() == 1) {
                    shortplayDyAdDataService.addExposurePV(dataId);
                } else if (req.getType() == 2) {
                    shortplayDyAdDataService.addWatchPv(dataId);
                }
            }
        });
        return ResultBuilder.success();
    }

    /**
     * 广点通用户停留时长统计接口
     * @param req 请求参数
     * @return 统计结果
     */
    @CrossOrigin
    @PostMapping("/userStayTime")
    public Result<Void> userStayTime(@RequestBody @Validated UserStayTimeReq req) {
        if (req.getType() != 1 && req.getType() != 2) {
            return ResultBuilder.fail("type参数错误，只能是1或2");
        }

        String userId = req.getUserId();
        String appId = req.getAppId();
        Integer type = req.getType();

        Date curDate = DateUtils.getStartOfDay(new Date());
        String dateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, curDate);
        String redisKey = RedisKeyFactory.K081.join(dateStr, appId, userId);
        log.info("userStayTime req={}", JSONObject.toJSONString(req));

        if (type == 1) {
            // 进入小程序，保存进入时间戳
            long enterTime = System.currentTimeMillis();
            // 使用stringRedisTemplate确保与Lua脚本兼容
            redisCache.stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(enterTime), 12, TimeUnit.HOURS);
        } else if ( type == 2 ){
            // 退出小程序，计算停留时长
            String enterTimeStr = redisCache.stringRedisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotBlank(enterTimeStr)) {
                try {
                    long enterTime = Long.parseLong(enterTimeStr);
                    long exitTime = System.currentTimeMillis();
                    long stayTimeMillis = exitTime - enterTime;

                    // 转换为秒
                    long stayTimeSeconds = stayTimeMillis / 1000;

                    // 如果停留时长小于1秒，则不统计
                    if (stayTimeSeconds < 1) {
                        log.info("用户停留时长小于1秒，不进行统计, userid={}, appid={}, stayTime={}秒", userId, appId, stayTimeSeconds);
                        deleteIfEquals(redisKey, enterTimeStr);
                        return ResultBuilder.success();
                    }
                    

                    // 先计算停留时长，再尝试删除时间戳
                    final long finalStayTimeSeconds = stayTimeSeconds;

                    // 异步更新数据库
                    statExecutor.execute(() -> {
                        try {
                            Long userIdLong = Long.parseLong(userId);
                            gdtUserStayTimeService.insertOrUpdateStayTime(curDate, appId, userIdLong, finalStayTimeSeconds);
                            // 检查是否需要上报
                            checkAndReportStayTime(curDate, appId, userIdLong);
                        } catch (Exception e) {
                            log.error("更新用户停留时长失败, userid={}, appid={}, stayTime={}", userId, appId, finalStayTimeSeconds, e);
                        }
                    });

                    // 尝试删除时间戳，防止重复计算（但不影响本次统计）
                    deleteIfEquals(redisKey, enterTimeStr);

                } catch (Exception e) {
                    log.error("userStayTime fail, userid={}, appid={}, enterTimeStr={}", userId, appId, enterTimeStr, e);
                }
            }else {
                log.info("无用户进入小程序时间戳, userid={}, appid={}", userId, appId);
            }
        }

        return ResultBuilder.success();
    }

    /**
     * 解析停留时长上报配置
     *
     * @param configStr 配置字符串，格式为"appid1-seconds1-rate1,appid2-seconds2-rate2"
     * @return Map<String, StayTimeReportRuleBo> key为appid, value为规则对象
     * <AUTHOR>
     */
    private Map<String, StayTimeReportRuleBo> parseStayTimeReportConfig(String configStr) {
        Map<String, StayTimeReportRuleBo> reportConfig = new HashMap<>();
        if (StringUtils.isBlank(configStr)) {
            return reportConfig;
        }

        try {
            String[] appConfigs = configStr.split(",");
            for (String appConfig : appConfigs) {
                String[] parts = appConfig.split("-");
                if (parts.length == 3) {
                    StayTimeReportRuleBo rule = new StayTimeReportRuleBo();
                    rule.setAppId(parts[0].trim());
                    rule.setRequiredSeconds(Integer.parseInt(parts[1].trim()));
                    rule.setReportRate(Integer.parseInt(parts[2].trim()));
                    reportConfig.put(rule.getAppId(), rule);
                }
            }
        } catch (Exception e) {
            log.error("解析停留时长上报配置失败, configStr={}", configStr, e);
        }
        return reportConfig;
    }

    /**
     * 根据停留时长上报
     *
     * @param curDate 当前日期
     * @param appId   应用ID
     * @param userId  用户ID
     */
    private void checkAndReportStayTime(Date curDate, String appId, Long userId) {
        // 获取上报配置
        String configStr = sysConfigService.selectConfigByKey("shortplay.gdt.staytime.report");
        if (StringUtils.isBlank(configStr)) {
            return;
        }

        // 解析配置
        Map<String, StayTimeReportRuleBo> reportConfig = parseStayTimeReportConfig(configStr);

        // 检查当前appId是否需要上报
        if (!reportConfig.containsKey(appId)) {
            return;
        }

        // 查询用户当天的停留时长记录
        GdtUserStayTimeEntity stayTimeEntity = gdtUserStayTimeService.selectByDateAndAppIdAndUserId(curDate, appId, userId);
        if (stayTimeEntity == null) {
            return;
        }

        // 判断停留时长是否达到要求
        StayTimeReportRuleBo rule = reportConfig.get(appId);
        Integer requiredSeconds = rule.getRequiredSeconds();
        Integer reportRate = rule.getReportRate();

        if (stayTimeEntity.getStayTime() >= requiredSeconds) {
            String reportInfo = stayTimeEntity.getReportInfo() == null ? "" : stayTimeEntity.getReportInfo();
            String reportTag = String.valueOf(requiredSeconds);
            // 判断是否已经上报过。报暂时只上报广点通短剧
            if (!containsReportTag(reportInfo, reportTag)) {
                // 概率计算：针对UV（用户维度）
                if (reportRate < 0 || !(Math.abs(userId.hashCode()) % 100 < reportRate)) {
                    log.info("广点通停留时长被概率扣量上报或规则阻止，userId={}, appId={}, reportRate={}", userId, appId, reportRate);
                    return;
                }
                GlobalThreadPool.gdtExecutorService.submit(() -> {
                    // 使用Redis分布式锁防止重复上报
                    String dateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, curDate);
                    String lockKey = RedisKeyFactory.K083.join(dateStr, appId, userId, reportTag);
                    RedisLock lock = redisAtomicClient.getLock(lockKey, 300); // 锁定5分钟
                    if (lock == null) {
                        log.info("获取停留时长上报锁失败，可能已有其他线程在处理, userId={}, appId={}, requiredSeconds={}",
                                userId, appId, requiredSeconds);
                        return;
                    }

                    try {
                        // 双重检查：再次查询数据库最新状态
                        GdtUserStayTimeEntity latestEntity = gdtUserStayTimeService.selectByDateAndAppIdAndUserId(curDate, appId, userId);
                        if (latestEntity == null) {
                            log.info("用户停留时长记录不存在, userId={}, appId={}", userId, appId);
                            return;
                        }

                        String latestReportInfo = latestEntity.getReportInfo() == null ? "" : latestEntity.getReportInfo();
                        if (containsReportTag(latestReportInfo, reportTag)) {
                            log.info("停留时长已上报过（双重检查）, userId={}, appId={}, requiredSeconds={}", userId, appId, requiredSeconds);
                            return;
                        }

                        ShortplayUserEntity user = shortplayUserService.selectById(userId);
                        if (user == null || StringUtils.isBlank(user.getWxOpenid())) {
                            log.info("用户不存在或无wx_openid, userId={}", userId);
                            return;
                        }

                        String openid = user.getWxOpenid();
                        ShortplayGdtStatEntity stat = shortplayGdtStatService.selectByWechatOpenid(openid);
                        // 更新上报信息
                        gdtUserStayTimeService.updateReportInfo(latestEntity.getId(), reportTag);

                        shortplayController.adExposureReportToGdt(openid, appId, user.getTfid(), "REGISTER", stat);
                        log.info("根据停留时长上报广点通: userId={}, appId={}, stayTime={}, requiredSeconds={}",
                                userId, appId, latestEntity.getStayTime(), requiredSeconds);

                    } catch (Exception e) {
                        log.error("停留时长上报执行异常, userId={}, appId={}", userId, appId, e);
                    } finally {
                        // 释放锁
                        lock.unlock();
                    }
                });
            }
        }
    }

    /**
     * 精确检查reportInfo是否包含指定的标签
     * 避免模糊匹配问题，如"30"匹配"300"的情况
     *
     * @param reportInfo 上报信息字符串，格式如"30,60,90"
     * @param tag 要检查的标签
     * @return 是否包含指定标签
     */
    private boolean containsReportTag(String reportInfo, String tag) {
        if (StringUtils.isBlank(reportInfo) || StringUtils.isBlank(tag)) {
            return false;
        }

        // 使用逗号分割进行精确匹配
        String[] tags = reportInfo.split(",");
        for (String existingTag : tags) {
            if (tag.equals(existingTag.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 原子性地删除Redis中的key，仅当值匹配时才删除
     * 防止误删新的时间戳
     *
     * @param key Redis key
     * @param expectedValue 期望的值
     * @return 是否删除成功
     */
    private boolean deleteIfEquals(String key, String expectedValue) {
        try {
            Long result = redisCache.executeLuaScript(
                deleteIfEqualsScript,
                Collections.singletonList(key),
                expectedValue
            );

            return result != null && result > 0;
        } catch (Exception e) {
            log.error("原子性删除Redis key失败, key={}, expectedValue={}", key, expectedValue, e);
            return false;
        }
    }

    private Long getDataIdDy(String dateStr, String appId, Long fTvId, Integer series, Integer times) {
        String value = redisCache.getCacheObject(RedisKeyFactory.K025.join(dateStr, appId, fTvId, series, times));
        return StringUtils.isNumeric(value) ? Convert.toLong(value) : null;
    }


    private Long getDataId(String dateStr, String miniappId) {
        String value = redisCache.getCacheObject(RedisKeyFactory.K025.join(dateStr, miniappId));
        return StringUtils.isNumeric(value) ? Convert.toLong(value) : null;
    }

    private void cacheDataId(String dateStr, String miniappId, Long dataId) {
        if (null != dataId) {
            redisCache.setCacheObject(RedisKeyFactory.K025.join(dateStr, miniappId), String.valueOf(dataId), 1, TimeUnit.DAYS);
        }
    }

    private Long getDataId(String dateStr, String miniappId, Long tvId, Integer series) {
        String value = redisCache.getCacheObject(RedisKeyFactory.K025.join(dateStr, miniappId, tvId, series));
        return StringUtils.isNumeric(value) ? Convert.toLong(value) : null;
    }

    private void cacheDataId(String dateStr, String miniappId, Long tvId, Integer series, Long dataId) {
        if (null != dataId) {
            redisCache.setCacheObject(RedisKeyFactory.K025.join(dateStr, miniappId, tvId, series), String.valueOf(dataId), 1, TimeUnit.DAYS);
        }
    }

    private void cacheDataIdDy(String dateStr, String miniappId, Long tvId, Integer series, Integer times, Long dataId) {
        if (null != dataId) {
            redisCache.setCacheObject(RedisKeyFactory.K025.join(dateStr, miniappId, tvId, series, times), String.valueOf(dataId), 1, TimeUnit.DAYS);
        }
    }
}
