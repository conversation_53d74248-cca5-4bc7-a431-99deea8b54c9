package com.ruoyi.web.controller.playlet.lianfan;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.lianfan.AgtDataSupplierSnapshotLianfanEntity;
import com.ruoyi.system.service.lianfan.AgtDataSupplierSnapshotLianfanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 连番快照Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agtdata/lianfan")
public class AgtDataSupplierSnapshotLianfanController {
    @Autowired
    private AgtDataSupplierSnapshotLianfanService agtDataSupplierSnapshotLianfanService;

    /**
     * 查询连番快照列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:lianfan:list')")
    @GetMapping("/list")
    public PageResult<AgtDataSupplierSnapshotLianfanEntity> list(AgtDataSupplierSnapshotLianfanEntity agtDataSupplierSnapshotLianfan) {
        TableSupport.startPage();
        List<AgtDataSupplierSnapshotLianfanEntity> list = agtDataSupplierSnapshotLianfanService.selectAgtDataSupplierSnapshotLianfanList(agtDataSupplierSnapshotLianfan);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出连番快照列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:lianfan:export')")
    @Log(title = "连番快照", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtDataSupplierSnapshotLianfanEntity agtDataSupplierSnapshotLianfan) {
        List<AgtDataSupplierSnapshotLianfanEntity> list = agtDataSupplierSnapshotLianfanService.selectAgtDataSupplierSnapshotLianfanList(agtDataSupplierSnapshotLianfan);
        ExcelUtil<AgtDataSupplierSnapshotLianfanEntity> util = new ExcelUtil<AgtDataSupplierSnapshotLianfanEntity>(AgtDataSupplierSnapshotLianfanEntity.class);
        util.exportExcel(response, list, "连番快照数据");
    }

    /**
     * 获取连番快照详细信息
     */
    @PreAuthorize("@ss.hasPermi('agtdata:lianfan:query')")
    @GetMapping(value = "/{id}")
    public Result<AgtDataSupplierSnapshotLianfanEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(agtDataSupplierSnapshotLianfanService.selectAgtDataSupplierSnapshotLianfanById(id));
    }

    /**
     * 新增连番快照
     */
    @PreAuthorize("@ss.hasPermi('agtdata:lianfan:add')")
    @Log(title = "连番快照", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody AgtDataSupplierSnapshotLianfanEntity agtDataSupplierSnapshotLianfan) {
        return ResultBuilder.success(agtDataSupplierSnapshotLianfanService.insertAgtDataSupplierSnapshotLianfan(agtDataSupplierSnapshotLianfan));
    }

    /**
     * 修改连番快照
     */
    @PreAuthorize("@ss.hasPermi('agtdata:lianfan:edit')")
    @Log(title = "连番快照", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody AgtDataSupplierSnapshotLianfanEntity agtDataSupplierSnapshotLianfan) {
        return ResultBuilder.success(agtDataSupplierSnapshotLianfanService.updateAgtDataSupplierSnapshotLianfan(agtDataSupplierSnapshotLianfan));
    }

    /**
     * 删除连番快照
     */
    @PreAuthorize("@ss.hasPermi('agtdata:lianfan:remove')")
    @Log(title = "连番快照", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(agtDataSupplierSnapshotLianfanService.deleteAgtDataSupplierSnapshotLianfanByIds(ids));
    }
}
