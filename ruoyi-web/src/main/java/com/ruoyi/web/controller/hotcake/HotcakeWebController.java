package com.ruoyi.web.controller.hotcake;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.RedisKeyFactory;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.HotcakeUserSourceEnum;
import com.ruoyi.common.enums.SystemConfigEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StrListUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.bo.hotcake.MiniappBasicConfigBo;
import com.ruoyi.system.bo.hotcake.MiniappSlotConfigBo;
import com.ruoyi.system.bo.hotcake.ReportBO;
import com.ruoyi.system.entity.hotcake.HotcakeChannelEntity;
import com.ruoyi.system.entity.hotcake.HotcakeDayDataEntity;
import com.ruoyi.system.entity.hotcake.HotcakeMiniappEntity;
import com.ruoyi.system.entity.hotcake.HotcakeShareEntity;
import com.ruoyi.system.entity.hotcake.HotcakeUserEntity;
import com.ruoyi.system.entity.hotcake.HotcakeVideoEntity;
import com.ruoyi.system.req.hotcake.HotcakeDayDataUpdateReq;
import com.ruoyi.system.req.hotcake.HotcakeStatReq;
import com.ruoyi.system.req.hotcake.HotcakeWebLoginReq;
import com.ruoyi.system.req.hotcake.HotcakeWebVideoListReq;
import com.ruoyi.system.req.hotcake.HotcakeWebVideoPageReq;
import com.ruoyi.system.req.shortplay.GdtStatReq;
import com.ruoyi.system.service.hotcake.HotcakeChannelService;
import com.ruoyi.system.service.hotcake.HotcakeDayDataService;
import com.ruoyi.system.service.hotcake.HotcakeMiniappService;
import com.ruoyi.system.service.hotcake.HotcakeShareService;
import com.ruoyi.system.service.hotcake.HotcakeUserService;
import com.ruoyi.system.service.hotcake.HotcakeVideoService;
import com.ruoyi.system.service.queue.impl.HotcakeShareInsertConsumeService;
import com.ruoyi.system.service.ruoyi.ISysConfigService;
import com.ruoyi.system.service.stat.CommonReportService;
import com.ruoyi.system.service.systemconfig.SystemConfigService;
import com.ruoyi.system.vo.hotcake.HotcakeWebMiniappVO;
import com.ruoyi.system.vo.hotcake.HotcakeWebUserVO;
import com.ruoyi.system.vo.hotcake.HotcakeWebVideoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.GlobalThreadPool.statExecutorService;

/**
 * [爆款小程序]小程序端
 *
 * <AUTHOR>
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/hotcake/web")
public class HotcakeWebController {

    @Value("${stat.send}")
    public Boolean statSend;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private HotcakeVideoService hotcakeVideoService;

    @Autowired
    private HotcakeChannelService hotcakeChannelService;

    @Autowired
    private HotcakeMiniappService hotcakeMiniappService;

    @Autowired
    private HotcakeUserService hotcakeUserService;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private HotcakeDayDataService hotcakeDayDataService;
    @Autowired
    private HotcakeShareService hotcakeShareService;
    @Autowired
    private HotcakeShareInsertConsumeService hotcakeShareInsertConsumeService;
    @Autowired
    private CommonReportService commonReportService;
    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 小程序信息缓存
     */
    public LoadingCache<String, HotcakeWebMiniappVO> MINIAPP_INFO_CACHE = CacheBuilder.newBuilder().refreshAfterWrite(1, TimeUnit.MINUTES).build(new CacheLoader<String, HotcakeWebMiniappVO>() {
        @Override
        public HotcakeWebMiniappVO load(String appId) {
            HotcakeMiniappEntity entity = hotcakeMiniappService.selectByAppId(appId);
            return null == entity ? new HotcakeWebMiniappVO() : convertTo(entity);
        }

        @Override
        public ListenableFuture<HotcakeWebMiniappVO> reload(String key, HotcakeWebMiniappVO oldValue) {
            ListenableFutureTask<HotcakeWebMiniappVO> task = ListenableFutureTask.create(() -> load(key));
            executorService.submit(task);
            return task;
        }
    });

    /**
     * 小程序信息缓存
     */
    public LoadingCache<String, HotcakeWebVideoVO> VIDEO_CACHE = CacheBuilder.newBuilder().refreshAfterWrite(1, TimeUnit.MINUTES).build(new CacheLoader<String, HotcakeWebVideoVO>() {
        @Override
        public HotcakeWebVideoVO load(String channelCode) {
            HotcakeChannelEntity channel = getChannelCacheByCode(channelCode);
            if (null != channel && NumberUtils.isNotNullOrLteZero(channel.getVideoId())) {
                HotcakeVideoEntity video = hotcakeVideoService.selectById(channel.getVideoId());
                if (null != video) {
                    return BeanUtil.copyProperties(video, HotcakeWebVideoVO.class);
                }
            }
            return new HotcakeWebVideoVO();
        }

        @Override
        public ListenableFuture<HotcakeWebVideoVO> reload(String key, HotcakeWebVideoVO oldValue) {
            ListenableFutureTask<HotcakeWebVideoVO> task = ListenableFutureTask.create(() -> load(key));
            executorService.submit(task);
            return task;
        }
    });

    /**
     * 渠道信息缓存
     */
    public LoadingCache<String, HotcakeChannelEntity> CHANNEL_CACHE = CacheBuilder.newBuilder().refreshAfterWrite(1, TimeUnit.MINUTES).build(new CacheLoader<String, HotcakeChannelEntity>() {
        @Override
        public HotcakeChannelEntity load(String channelCode) {
            return hotcakeChannelService.selectByChanelCode(channelCode);
        }

        @Override
        public ListenableFuture<HotcakeChannelEntity> reload(String key, HotcakeChannelEntity oldValue) {
            ListenableFutureTask<HotcakeChannelEntity> task = ListenableFutureTask.create(() -> load(key));
            executorService.submit(task);
            return task;
        }
    });

    public HotcakeChannelEntity getChannelCacheByCode(String channelCode) {
        try {
            if(StringUtils.isBlank(channelCode)){
                return null;
            }
            return CHANNEL_CACHE.get(StringUtils.defaultString(channelCode));
        } catch (Exception e) {
            log.error("获取渠道信息异常,channelCode:{}", channelCode,e);
        }
        return null;
    }


    /**
     * 小程序登录
     */
    @PostMapping("/login")
    public Result<HotcakeWebUserVO> login(HttpServletRequest request, @RequestBody HotcakeWebLoginReq req) {
        Date now = new Date();

        // 获取推广视频
        HotcakeWebVideoVO video = getVideoByChannelCode(req.getChannelCode());
        Integer source = NumberUtils.isNotNullOrLteZero(req.getShareUserId()) ? HotcakeUserSourceEnum.SHARE.getSource() : StringUtils.isNotBlank(req.getChannelCode()) ? HotcakeUserSourceEnum.ENGINE.getSource() : HotcakeUserSourceEnum.NORMAL.getSource();
        HotcakeUserEntity user;
        if (null != req.getUserId()) {
            user = hotcakeUserService.selectById(Convert.toLong(req.getUserId()));
            if (null == user) {
                return ResultBuilder.success(new HotcakeWebUserVO().setVideo(video).setChannelCode(req.getChannelCode()));
            }
        } else {
            String openid = req.getOpenid();
            if (StringUtils.isBlank(openid)) {
                JSONObject session = hotcakeUserService.jscode2session(req.getCode(), req.getAppId());
                if (null != session) {
                    String sessionKey = session.getString("session_key");
                    openid = session.getString("openid");
                    cacheSessionKey(openid, sessionKey);
                }
            }
            if (StringUtils.isBlank(openid)) {
                return ResultBuilder.success(new HotcakeWebUserVO().setVideo(video).setChannelCode(req.getChannelCode()));
            }

            user = hotcakeUserService.selectByOpenId(openid);
            if (null == user) {
                user = new HotcakeUserEntity();
                user.setAppId(req.getAppId());
                user.setRegisterDate(now);
                user.setLastLoginTime(now);
                user.setLastLoginIp(IpUtils.getIpAddr(request));
                user.setChannelCode(req.getChannelCode());
                user.setOsType(req.getOsType());
                user.setOpenid(openid);
                user.setSource(source);
                user.setShareUserId(Convert.toLong(req.getShareUserId(), 0L));
                if (null != req.getPathParams()) {
                    user.setPathParamStr(JSON.toJSONString(req.getPathParams()));
                }
                hotcakeUserService.insertHotcakeUser(user);
                addShareRecord(req.getShareUserId(), user.getId(), req.getShareVideoId());
                loginStat(1, user);
                return ResultBuilder.success(convertTo(user).setVideo(video).setChannelCode(user.getChannelCode()).setShowAd(showAd(user.getSource(), user.getChannelCode())));
            }
        }

        HotcakeUserEntity updateUser = new HotcakeUserEntity();
        updateUser.setId(user.getId());
        updateUser.setAppId(req.getAppId());
        updateUser.setOpenid(user.getOpenid());
        updateUser.setSource(user.getSource());
        updateUser.setChannelCode(user.getChannelCode());
        //如果用户是24小时之前的,则重置用户来源等
        if (user.getLastLoginTime().before(DateUtils.addHours(now, -24))) {
            updateUser.setChannelCode(StrUtil.blankToDefault(req.getChannelCode(), user.getChannelCode()));
            updateUser.setSource(source);
            updateUser.setShareUserId(Convert.toLong(req.getShareUserId(), 0L));
            //如果是分享，则记录分享裂变
            addShareRecord(req.getShareUserId(), user.getId(), req.getShareVideoId());
        }
        updateUser.setLastLoginTime(now);
        updateUser.setLastLoginIp(IpUtils.getIpAddr(request));
        executorService.submit(() -> {
            hotcakeUserService.updateHotcakeUser(updateUser);
        });
        loginStat(2, user);

        return ResultBuilder.success(convertTo(user).setVideo(video).setChannelCode(updateUser.getChannelCode()).setShowAd(showAd(updateUser.getSource(), updateUser.getChannelCode())));
    }


    /**
     * 用户是否展示广告
     *
     * @param userSource
     * @return
     */
    private boolean showAd(Integer userSource, String channelCode) {
        //分享来的用户展示广告
        if (Objects.equals(userSource, HotcakeUserSourceEnum.SHARE.getSource())) {
            return true;
        }

        HotcakeChannelEntity channel = getChannelCacheByCode(channelCode);
        return Objects.nonNull(channel) && channel.getFreeAd() == 2;

    }

    /**
     * 增加分享记录
     *
     * @param userId       分享者用户id
     * @param sharedUserId 被分享者用户id
     * @param videoId      分享视频id
     */
    private void addShareRecord(Long userId, Long sharedUserId, Long videoId) {
        if (NumberUtils.isNullOrLteZero(userId) || NumberUtils.isNullOrLteZero(sharedUserId)) {
            return;
        }
        HotcakeShareEntity share = new HotcakeShareEntity();
        share.setUserId(userId);
        share.setSharedUserId(sharedUserId);
        share.setSourceVideoId(Convert.toLong(videoId, 0L));
        hotcakeShareInsertConsumeService.addQueue(share);
    }

    /**
     * 广告点击上报
     */
    @GetMapping("clickReport")
    public void clickReport(Long userId) {
        //查询用户渠道，如果没有渠道，则不处理
        Long time = redisAtomicClient.incrBy(RedisKeyFactory.K072.join(DateUtil.formatDate(new Date()), userId), 1, 1, TimeUnit.DAYS);
        HotcakeUserEntity userEntity = hotcakeUserService.selectById(userId);
        if (Objects.isNull(userEntity)) {
            return;
        }
        String channelCode = userEntity.getChannelCode();
        if (StringUtils.isBlank(channelCode)) {
            return;
        }
        HotcakeChannelEntity channel = getChannelCacheByCode(channelCode);
        if (Objects.isNull(channel)) {
            return;
        }
        //查询用户的clickid等，没有则不处理
        //如果分享ipu=0，则判断点击ipu
        if (NumberUtils.isNullOrLteZero(channel.getShareIpu())) {
            ReportBO reportBO = redisCache.getCacheObject(RedisKeyFactory.K074.join(userEntity.getOpenid()));
            if(Objects.isNull(reportBO) || StringUtils.isBlank(reportBO.getClick_id())){
                return;
            }
            Integer clickAdIpu = channel.getClickAdIpu();
            if (time >= clickAdIpu) {
                reportGdt(userEntity.getId(), userEntity.getOpenid(), userEntity.getAppId(), "game_addiction", reportBO.getClick_id(), reportBO.getCallback(), reportBO.getAccount_id());
            }
            return;
        }
        //分享ipu上报
        //查询当前用户的邀请者,如果邀请者不存在或者click_id 未空，或者已经上报过则不处理
        HotcakeUserEntity shareUser = hotcakeUserService.selectById(userEntity.getShareUserId());
        if (Objects.isNull(shareUser) || StringUtils.isBlank("") || Objects.nonNull(redisCache.getCacheObject(RedisKeyFactory.K073.join(shareUser.getId())))) {
            return;
        }
        //判断邀请人数是否满足ipu
        int shareCount = hotcakeShareService.countByUserIdAndDate(shareUser.getId(), DateUtils.addHours(new Date(), -24));
        if (shareCount < channel.getShareIpu()) {
            return;
        }
        //上报。redis记录
        ReportBO reportBO = redisCache.getCacheObject(RedisKeyFactory.K074.join(shareUser.getOpenid()));
        if(Objects.isNull(reportBO) || StringUtils.isBlank(reportBO.getClick_id())){
            return;
        }
        String actionType = sysConfigService.selectConfigCacheByKey(SystemConfigEnum.ADQ_SHARE_REPORT.getConfigKey());
        actionType = StringUtils.defaultString(actionType,"PURCHASE");
        reportGdt(shareUser.getId(), shareUser.getOpenid(), shareUser.getAppId(), actionType, reportBO.getClick_id(), reportBO.getCallback(), reportBO.getAccount_id());

    }

    /**
     * 分享上报
     */
    @GetMapping("shareReport")
    public void shareReport(Long userId){
        HotcakeUserEntity userEntity = hotcakeUserService.selectById(userId);
        if (Objects.isNull(userEntity)) {
            return;
        }
        ReportBO reportBO = redisCache.getCacheObject(RedisKeyFactory.K074.join(userEntity.getOpenid()));
        if(Objects.isNull(reportBO) || StringUtils.isBlank(reportBO.getClick_id())){
            return;
        }
        reportGdt(userEntity.getId(), userEntity.getOpenid(), userEntity.getAppId(), "REGISTER", reportBO.getClick_id(), reportBO.getCallback(), reportBO.getAccount_id());
    }

    /**
     * 上报广点通
     * @param userId
     * @param openId
     * @param appId
     * @param actionType
     * @param clickId
     * @param callback
     * @param accountId
     */
    private void reportGdt(Long userId,String openId, String appId, String actionType, String clickId,String callback,String accountId) {
        Boolean success = commonReportService.reportGdt(openId, appId, actionType, clickId, callback, accountId);
        if(success){
            redisCache.setCacheObject(RedisKeyFactory.K073.join(userId), true, 12, TimeUnit.HOURS);
        }
    }

    /**
     * 查询小程序信息
     */
    @GetMapping("/getMiniappInfo")
    public Result<HotcakeWebMiniappVO> getMiniappInfo(String appId) {
        HotcakeWebMiniappVO vo = null;
        try {
            vo = MINIAPP_INFO_CACHE.get(StringUtils.defaultString(appId));
        } catch (Exception e) {
            log.error("getMiniappInfoCache error, appId={}", appId, e);
        }
        return ResultBuilder.success(vo);
    }

    /**
     * 查询视频
     */
    @GetMapping("/getVideo")
    public Result<HotcakeWebVideoVO> getVideo(String channelCode) {
        if (StringUtils.isBlank(channelCode)) {
            return ResultBuilder.success();
        }
        HotcakeWebVideoVO video = getVideoByChannelCode(channelCode);
        return ResultBuilder.success(video);
    }

    /**
     * 列表页视频列表
     */
    @GetMapping("/feedPageVideoList")
    public PageResult<HotcakeWebVideoVO> feedPageVideoList(HotcakeWebVideoPageReq req) {
        HotcakeChannelEntity channel = hotcakeChannelService.selectByChanelCode(req.getChannelCode());
        List<Long> videoIds = new ArrayList<>();
        if (null != channel) {
            videoIds.addAll(StrListUtils.strToList(channel.getFeedPageVideoIds(), Long.class));
        }
        if (Objects.equals(req.getPageNum(), 0)) {
            if (null == channel || CollectionUtils.isEmpty(videoIds)) {
                return PageResult.emptyPageResult();
            }
            Map<Long, Integer> videoSortMap = getVideoSortMap(videoIds);

            HotcakeWebVideoListReq videoReq = new HotcakeWebVideoListReq();
            videoReq.setIds(videoIds);
            List<HotcakeVideoEntity> videos = hotcakeVideoService.selectListForWeb(videoReq);

            PageResult<HotcakeWebVideoVO> page = PageResult.init(0, videoIds.size());
            page.setTotal(videoIds.size());
            page.setHasNext(true);
            page.setList(videos.stream().map(s -> convertTo(s, 1)).sorted(Comparator.comparing(o -> videoSortMap.get(o.getId()))).collect(Collectors.toList()));
            return page;
        }

        TableSupport.startPage();
        HotcakeWebVideoListReq videoReq = new HotcakeWebVideoListReq();
        videoReq.setExcludeIds(videoIds);
        videoReq.setTags(Collections.singletonList("常规")); // TODO 只出常规
        List<HotcakeVideoEntity> list = hotcakeVideoService.selectListForWeb(videoReq);
        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(list, s -> convertTo(s, 0)));
    }

    /**
     * 播放页视频列表
     */
    @GetMapping("/playPageVideoList")
    public PageResult<HotcakeWebVideoVO> playPageVideoList(HotcakeWebVideoPageReq req) {
        HotcakeChannelEntity channel = hotcakeChannelService.selectByChanelCode(req.getChannelCode());
        List<Long> videoIds = new ArrayList<>();
        if (null != channel) {
            videoIds.addAll(StrListUtils.strToList(channel.getPlayPageVideoIds(), Long.class));
        }
        if (Objects.equals(req.getPageNum(), 0)) {
            if (null == channel || CollectionUtils.isEmpty(videoIds)) {
                return PageResult.emptyPageResult();
            }
            Map<Long, Integer> videoSortMap = getVideoSortMap(videoIds);

            HotcakeWebVideoListReq videoReq = new HotcakeWebVideoListReq();
            videoReq.setIds(videoIds);
            List<HotcakeVideoEntity> videos = hotcakeVideoService.selectListForWeb(videoReq);

            PageResult<HotcakeWebVideoVO> page = PageResult.init(0, videoIds.size());
            page.setTotal(videoIds.size());
            page.setHasNext(true);
            page.setList(videos.stream().map(s -> convertTo(s, 1)).sorted(Comparator.comparing(o -> videoSortMap.get(o.getId()))).collect(Collectors.toList()));
            return page;
        }

        TableSupport.startPage();
        HotcakeWebVideoListReq videoReq = new HotcakeWebVideoListReq();
        videoReq.setExcludeIds(videoIds);
        videoReq.setTags(Collections.singletonList("常规")); // TODO 只出常规
        List<HotcakeVideoEntity> list = hotcakeVideoService.selectListForWeb(videoReq);
        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(list, s -> convertTo(s, 0)));
    }

    /**
     * 埋点
     */
    @GetMapping("stat")
    public Result<Void> stat(HotcakeStatReq req) {
        statAsync(req);
        return ResultBuilder.success();
    }


    /**
     * 广点通监测链接
     *
     * @return
     */
    @GetMapping("gdtStat")
    public Result<Void> gdtStat(GdtStatReq req) {
        if (Objects.isNull(req)) {
            return ResultBuilder.success();
        }
        ReportBO reportBO = BeanUtil.copyProperties(req, ReportBO.class);
        redisCache.setCacheObject(RedisKeyFactory.K074.join(req.getWechat_openid()), reportBO, 2, TimeUnit.DAYS);
        return ResultBuilder.success();
    }

    private HotcakeWebUserVO convertTo(HotcakeUserEntity entity) {
        return BeanUtil.copyProperties(entity, HotcakeWebUserVO.class);
    }

    private HotcakeWebVideoVO convertTo(HotcakeVideoEntity entity, Integer flag) {
        HotcakeWebVideoVO vo = BeanUtil.copyProperties(entity, HotcakeWebVideoVO.class);
        vo.setTags(StrListUtils.strToList(entity.getTags()));
        vo.setFlag(flag);
        return vo;
    }

    private HotcakeWebMiniappVO convertTo(HotcakeMiniappEntity entity) {
        HotcakeWebMiniappVO vo = new HotcakeWebMiniappVO();
        vo.setAppId(entity.getAppId());
        vo.setAppName(entity.getAppName());
        vo.setSlotConfig(JSON.parseObject(entity.getSlotConfig(), MiniappSlotConfigBo.class));
        vo.setBasicConfig(JSON.parseObject(entity.getBasicConfig(), MiniappBasicConfigBo.class));
        return vo;
    }

    private Map<Long, Integer> getVideoSortMap(List<Long> videoIds) {
        Map<Long, Integer> videoSortMap = new HashMap<>(videoIds.size());
        for (int i = 0; i < videoIds.size(); i++) {
            videoSortMap.put(videoIds.get(i), i);
        }
        return videoSortMap;
    }

    /**
     * 缓存sessionKey
     */
    private void cacheSessionKey(String openid, String sessionKey) {
        if (StringUtils.isNotBlank(sessionKey) && StringUtils.isNotBlank(openid)) {
            redisCache.setCacheObject(RedisKeyFactory.K020.join(openid), sessionKey, 1, TimeUnit.DAYS);
        }
    }

    private void loginStat(Integer type, HotcakeUserEntity user) {
        HotcakeStatReq req = new HotcakeStatReq();
        req.setType(type);
        req.setAppId(user.getAppId());
        req.setUserId(String.valueOf(user.getId()));
        req.setChannelCode(user.getChannelCode());
        statAsync(req);
    }

    private HotcakeWebVideoVO getVideoByChannelCode(String channelCode) {
        if(StringUtils.isBlank(channelCode)){
            return null;
        }
        HotcakeWebVideoVO vo = null;
        try {
            vo = VIDEO_CACHE.get(StringUtils.defaultString(channelCode));
        } catch (Exception e) {
            log.error("getVideoByChannelCode error, channelCode={}", channelCode, e);
        }
        return vo;
    }

    private void statAsync(HotcakeStatReq req) {
        String uid = StringUtils.defaultString(req.getUserId());
        Date curDate = DateUtil.beginOfDay(new Date());
        String today = DateUtil.formatDate(curDate);

        statExecutorService.submit(() -> {
            if (StringUtils.isNotBlank(req.getAppId())) {
                try {
                    Long dataId = hotcakeDayDataService.selectIdBy(curDate, req.getAppId(), req.getChannelCode());
                    if (null == dataId) {
                        hotcakeDayDataService.insert(curDate, req.getAppId(), req.getChannelCode());
                        dataId = hotcakeDayDataService.selectIdBy(curDate, req.getAppId(), req.getChannelCode());
                    }
                    HotcakeDayDataUpdateReq updateData = new HotcakeDayDataUpdateReq();
                    updateData.setId(dataId);
                    if (Objects.equals(req.getType(), 1)) {
                        updateData.setRegisterPvAdd(1);
                        updateData.setRegisterUvAdd(countUv(RedisKeyFactory.K061.join(today, req.getType()), uid, 2));
                        updateData.setLoginPvAdd(1);
                        updateData.setLoginUvAdd(countUv(RedisKeyFactory.K061.join(today, 2), uid, 1));
                    } else if (Objects.equals(req.getType(), 2)) {
                        updateData.setLoginPvAdd(1);
                        updateData.setLoginUvAdd(countUv(RedisKeyFactory.K061.join(today, 2), uid, 1));
                        // 次留
                        if (BooleanUtil.isTrue(redisCache.isSetMember(RedisKeyFactory.K061.join(DateUtil.formatDate(DateUtil.yesterday()), 1), uid))) {
                            updateData.setRetentionDay1UvAdd(countUv(RedisKeyFactory.K061.join(today, 2, "次留"), uid, 1));
                        }
                    } else if (Objects.equals(req.getType(), 4)) {
                        // 1.首屏,2.插屏,3.列表页,4.播放页,5.视频解锁,6.左上角返回,7.物理返回,8.关闭
                        int uv = countUv(RedisKeyFactory.K061.join(today, req.getType(), req.getPosition()), uid, 1);
                        switch (req.getPosition()) {
                            case "1":
                                updateData.setSplashExposurePvAdd(1);
                                updateData.setSplashExposureUvAdd(uv);
                                break;
                            case "2":
                                updateData.setInterstitialExposurePvAdd(1);
                                updateData.setInterstitialExposureUvAdd(uv);
                                break;
                            case "3":
                                updateData.setFeedPageExposurePvAdd(1);
                                updateData.setFeedPageExposureUvAdd(uv);
                                break;
                            case "4":
                                updateData.setPlayPageExposurePvAdd(1);
                                updateData.setPlayPageExposureUvAdd(uv);
                                break;
                            case "5":
                                updateData.setVideoUnlockExposurePvAdd(1);
                                updateData.setVideoUnlockExposureUvAdd(uv);
                                break;
                            case "6":
                                updateData.setLeftRetExposurePvAdd(1);
                                updateData.setLeftRetExposureUvAdd(uv);
                                break;
                            case "7":
                                updateData.setPhysicalRetExposurePvAdd(1);
                                updateData.setPhysicalRetExposureUvAdd(uv);
                                break;
                            case "8":
                                updateData.setCloseExposurePvAdd(1);
                                updateData.setCloseExposureUvAdd(uv);
                                break;
                            default:
                                updateData.setId(null); // 不更新
                                break;
                        }
                    } else if (Objects.equals(req.getType(), 5)) {
                        // 1.首屏,2.插屏,3.列表页,4.播放页,5.视频解锁,6.左上角返回,7.物理返回,8.关闭
                        int uv = countUv(RedisKeyFactory.K061.join(today, req.getType(), req.getPosition()), uid, 1);
                        switch (req.getPosition()) {
                            case "1":
                                updateData.setSplashClickPvAdd(1);
                                updateData.setSplashClickUvAdd(uv);
                                break;
                            case "2":
                                updateData.setInterstitialClickPvAdd(1);
                                updateData.setInterstitialClickUvAdd(uv);
                                break;
                            case "3":
                                updateData.setFeedPageClickPvAdd(1);
                                updateData.setFeedPageClickUvAdd(uv);
                                break;
                            case "4":
                                updateData.setPlayPageClickPvAdd(1);
                                updateData.setPlayPageClickUvAdd(uv);
                                break;
                            case "5":
                                updateData.setVideoUnlockClickPvAdd(1);
                                updateData.setVideoUnlockClickUvAdd(uv);
                                break;
                            case "6":
                                updateData.setLeftRetClickPvAdd(1);
                                updateData.setLeftRetClickUvAdd(uv);
                                break;
                            case "7":
                                updateData.setPhysicalRetClickPvAdd(1);
                                updateData.setPhysicalRetClickUvAdd(uv);
                                break;
                            case "8":
                                updateData.setCloseClickPvAdd(1);
                                updateData.setCloseClickUvAdd(uv);
                                break;
                            default:
                                updateData.setId(null); // 不更新
                                break;
                        }
                    } else {
                        updateData.setId(null); // 不更新
                    }
                    hotcakeDayDataService.updateHotcakeDayData(updateData);
                } catch (Exception e) {
                    log.error("爆款小程序埋点异常, req={}", JSON.toJSONString(req), e);
                }
            }

//            if (SpringEnvironmentUtils.isProd() && statSend) {
//                try {
//                    // action: type.appId.channelCode.position.videoId
//                    String action = req.getType() + "." + Convert.toStr(req.getAppId(), "") + "." + Convert.toStr(req.getChannelCode(), "") + "." + Convert.toStr(req.getPage(), "") + "." + Convert.toStr(req.getPosition(), "") + "." + Convert.toStr(req.getVideoId(), "");
//                    HttpUtil.createGet(StrUtil.format("http://172.20.216.118:8905/stat?scene=4&uid={}&action={}", uid, action)).executeAsync();
//                } catch (Exception e) {
//                    log.error("爆款小程序埋点(HTTP)异常, req={}", JSON.toJSONString(req), e);
//                }
//            }
        });
    }

    /**
     * 计算uv
     *
     * @param key   Redis缓存Key
     * @param value 去重的value
     * @return 新增的uv
     */
    private int countUv(String key, String value, Integer expireDays) {
        if (StringUtils.isBlank(key)) {
            return 0;
        }

        Boolean isExist = redisCache.isSetMember(key, value);
        if (Objects.equals(isExist, true)) {
            return 0;
        }
        redisCache.sAdd(key, value, expireDays, TimeUnit.DAYS);
        return 1;
    }
}
