package com.ruoyi.web.controller.playlet.metrics;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.metrics.AgtDataMaterialMetricsEntity;
import com.ruoyi.system.service.metrics.AgtDataMaterialMetricsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 素材指标Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agtdata/metrics")
public class AgtDataMaterialMetricsController {
    @Autowired
    private AgtDataMaterialMetricsService agtDataMaterialMetricsService;

    /**
     * 查询素材指标列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:metrics:list')")
    @GetMapping("/list")
    public PageResult<AgtDataMaterialMetricsEntity> list(AgtDataMaterialMetricsEntity agtDataMaterialMetrics) {
        TableSupport.startPage();
        List<AgtDataMaterialMetricsEntity> list = agtDataMaterialMetricsService.selectAgtDataMaterialMetricsList(agtDataMaterialMetrics);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出素材指标列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:metrics:export')")
    @Log(title = "素材指标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtDataMaterialMetricsEntity agtDataMaterialMetrics) {
        List<AgtDataMaterialMetricsEntity> list = agtDataMaterialMetricsService.selectAgtDataMaterialMetricsList(agtDataMaterialMetrics);
        ExcelUtil<AgtDataMaterialMetricsEntity> util = new ExcelUtil<AgtDataMaterialMetricsEntity>(AgtDataMaterialMetricsEntity.class);
        util.exportExcel(response, list, "素材指标数据");
    }

    /**
     * 获取素材指标详细信息
     */
    @PreAuthorize("@ss.hasPermi('agtdata:metrics:query')")
    @GetMapping(value = "/{id}")
    public Result<AgtDataMaterialMetricsEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(agtDataMaterialMetricsService.selectAgtDataMaterialMetricsById(id));
    }

    /**
     * 新增素材指标
     */
    @PreAuthorize("@ss.hasPermi('agtdata:metrics:add')")
    @Log(title = "素材指标", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody AgtDataMaterialMetricsEntity agtDataMaterialMetrics) {
        return ResultBuilder.success(agtDataMaterialMetricsService.insertAgtDataMaterialMetrics(agtDataMaterialMetrics));
    }

    /**
     * 修改素材指标
     */
    @PreAuthorize("@ss.hasPermi('agtdata:metrics:edit')")
    @Log(title = "素材指标", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody AgtDataMaterialMetricsEntity agtDataMaterialMetrics) {
        return ResultBuilder.success(agtDataMaterialMetricsService.updateAgtDataMaterialMetrics(agtDataMaterialMetrics));
    }

    /**
     * 删除素材指标
     */
    @PreAuthorize("@ss.hasPermi('agtdata:metrics:remove')")
    @Log(title = "素材指标", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(agtDataMaterialMetricsService.deleteAgtDataMaterialMetricsByIds(ids));
    }
}
