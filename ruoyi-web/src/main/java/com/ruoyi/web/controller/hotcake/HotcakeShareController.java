package com.ruoyi.web.controller.hotcake;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.hotcake.HotcakeShareEntity;
import com.ruoyi.system.service.hotcake.HotcakeShareService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 分享裂变Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hotcake/hotcakeShare")
public class HotcakeShareController {
    @Autowired
    private HotcakeShareService hotcakeShareService;

    /**
     * 查询分享裂变列表
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeShare:list')")
    @GetMapping("/list")
    public PageResult<HotcakeShareEntity> list(HotcakeShareEntity hotcakeShare) {
        TableSupport.startPage();
        List<HotcakeShareEntity> list = hotcakeShareService.selectHotcakeShareList(hotcakeShare);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出分享裂变列表
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeShare:export')")
    @Log(title = "分享裂变", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HotcakeShareEntity hotcakeShare) {
        List<HotcakeShareEntity> list = hotcakeShareService.selectHotcakeShareList(hotcakeShare);
        ExcelUtil<HotcakeShareEntity> util = new ExcelUtil<HotcakeShareEntity>(HotcakeShareEntity.class);
        util.exportExcel(response, list, "分享裂变数据");
    }

    /**
     * 获取分享裂变详细信息
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeShare:query')")
    @GetMapping(value = "/{id}")
    public Result<HotcakeShareEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(hotcakeShareService.selectHotcakeShareById(id));
    }

    /**
     * 新增分享裂变
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeShare:add')")
    @Log(title = "分享裂变", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody HotcakeShareEntity hotcakeShare) {
        return ResultBuilder.success(hotcakeShareService.insertHotcakeShare(hotcakeShare));
    }

    /**
     * 修改分享裂变
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeShare:edit')")
    @Log(title = "分享裂变", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody HotcakeShareEntity hotcakeShare) {
        return ResultBuilder.success(hotcakeShareService.updateHotcakeShare(hotcakeShare));
    }

    /**
     * 删除分享裂变
     */
    @PreAuthorize("@ss.hasPermi('hotcake:hotcakeShare:remove')")
    @Log(title = "分享裂变", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(hotcakeShareService.deleteHotcakeShareByIds(ids));
    }
}
