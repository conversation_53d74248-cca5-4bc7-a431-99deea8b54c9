package com.ruoyi.web.controller.shortplay;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.exception.BizRuntimeException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.req.shortplay.CsjResourceLibraryReq;
import com.ruoyi.system.req.shortplay.PathBindReq;
import com.ruoyi.system.service.pangolin.PangolinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 抖音接口转发
 */
@RestController
@RequestMapping("/shortplay/relay")
public class ShortplayRelayController {

    @Autowired
    private PangolinService pangolinService;

    @Anonymous
    @CrossOrigin
    @RequestMapping("/resourceQuery")
    public String resourceQuery(@RequestBody CsjResourceLibraryReq req) {
        JSONObject body = new JSONObject();
        body.put("resource_id", req.getResourceId());

        String accessToken = pangolinService.getAuthorizerAccessToken(req.getAppId());
        return HttpUtil.createPost("https://open.douyin.com/api/resource_library/v1/resource/query/")
                .header("access-token", accessToken)
                .header("Content-Type", "application/json")
                .body(body.toString())
                .execute().body();
    }

    @Anonymous
    @CrossOrigin
    @RequestMapping("/pathBind")
    public String pathBind(@RequestBody PathBindReq req) {
        if (StringUtils.isBlank(req.getAppId()) || null == req.getPath_info()) {
            throw new BizRuntimeException("参数错误");
        }

        JSONObject body = new JSONObject();
        body.put("path_info", req.getPath_info());

        String accessToken = pangolinService.getAuthorizerAccessToken(req.getAppId());
        return HttpUtil.createPost("https://open.douyin.com/api/resource_library/v1/app/path/bind/")
                .header("access-token", accessToken)
                .header("Content-Type", "application/json")
                .body(body.toString())
                .execute().body();
    }

    @Anonymous
    @CrossOrigin
    @RequestMapping("/generateUrlLink")
    public String generateUrlLink(@RequestBody JSONObject body) {
        String appId = body.getString("appid");
        if (StringUtils.isBlank(appId)) {
            throw new BizRuntimeException("参数错误");
        }
        body.put("app_id", appId);
        body.put("query", body.getJSONObject("query").toString());

        String accessToken = pangolinService.getAuthorizerAccessToken(appId);
        return HttpUtil.createPost("https://open.douyin.com/api/apps/v1/url_link/generate/")
                .header("access-token", accessToken)
                .header("Content-Type", "application/json")
                .body(body.toString())
                .execute().body();
    }

    @Anonymous
    @CrossOrigin
    @RequestMapping("/rollback")
    public String rollback(@RequestBody JSONObject body) {
        String appId = body.getString("appid");
        if (StringUtils.isBlank(appId)) {
            throw new BizRuntimeException("参数错误");
        }

        String accessToken = pangolinService.getAuthorizerAccessToken(appId);
        return HttpUtil.createPost("https://open.douyin.com/api/apps/v1/package_version/rollback/")
                .header("access-token", accessToken)
                .header("Content-Type", "application/json")
                .execute().body();
    }
}
