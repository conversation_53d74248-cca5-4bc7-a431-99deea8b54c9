package com.ruoyi.web.controller.stat;

import java.util.List;
import java.util.Objects;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.exception.BizRuntimeException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.stat.StatTypeEntity;
import com.ruoyi.system.service.stat.StatTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 埋点数值标识Controller
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@RestController
@RequestMapping("/stat/stat")
public class StatTypeController {
    @Autowired
    private StatTypeService statTypeService;

    /**
     * 查询埋点数值标识列表
     */
    @PreAuthorize("@ss.hasPermi('stat:stat:list')")
    @GetMapping("/list")
    public PageResult<StatTypeEntity> list(StatTypeEntity statType) {
        TableSupport.startPage();
        List<StatTypeEntity> list = statTypeService.selectStatTypeList(statType);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出埋点数值标识列表
     */
    @PreAuthorize("@ss.hasPermi('stat:stat:export')")
    @Log(title = "埋点数值标识", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatTypeEntity statType) {
        List<StatTypeEntity> list = statTypeService.selectStatTypeList(statType);
        ExcelUtil<StatTypeEntity> util = new ExcelUtil<StatTypeEntity>(StatTypeEntity.class);
        util.exportExcel(response, list, "埋点数值标识数据");
    }

    /**
     * 获取埋点数值标识详细信息
     */
    @PreAuthorize("@ss.hasPermi('stat:stat:query')")
    @GetMapping(value = "/{id}")
    public Result<StatTypeEntity> getInfo(@PathVariable("id") String id) {
        Integer idInt = Integer.valueOf(id);
        return ResultBuilder.success(statTypeService.selectStatTypeById(idInt));
    }

    /**
     * 新增埋点数值标识
     */
    @PreAuthorize("@ss.hasPermi('stat:stat:add')")
    @Log(title = "埋点数值标识", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody StatTypeEntity statType) {
        //埋点 含义 4.401.1001.201
        // 4 代表应用，401代表一级页面，1001代表二级场景，201代表行为

        StatTypeEntity entity = statTypeService.selectByRemark(statType.getRemark());
        if(Objects.nonNull(entity)){
            throw new BizRuntimeException("已存在对应埋点，请勿重复添加！");
        }
        Integer maxId = statTypeService.selectMaxIdByLevel(statType.getTypeLevel());
        if(NumberUtils.isNullOrLteZero(maxId)){
            maxId = 1;
        }else{
            maxId++;
        }
        statType.setTypeId(maxId);

        return ResultBuilder.success(statTypeService.insertStatType(statType));
    }

    /**
     * 修改埋点数值标识
     */
    @PreAuthorize("@ss.hasPermi('stat:stat:edit')")
    @Log(title = "埋点数值标识", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody StatTypeEntity statType) {
        StatTypeEntity entity = statTypeService.selectByRemark(statType.getRemark());
        if(Objects.nonNull(entity) && !Objects.equals(statType.getId(),entity.getId())){
            throw new BizRuntimeException("已存在对应埋点，修改失败！");
        }
        return ResultBuilder.success(statTypeService.updateStatType(statType));
    }

    /**
     * 删除埋点数值标识
     */
    @PreAuthorize("@ss.hasPermi('stat:stat:remove')")
    @Log(title = "埋点数值标识", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Integer[] ids) {
        return ResultBuilder.success(statTypeService.deleteStatTypeByIds(ids));
    }
}
