package com.ruoyi.web.controller.pangolin;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.system.req.pangolin.PangolinSlotAddReq;
import com.ruoyi.system.req.pangolin.PangolinSlotQueryReq;
import com.ruoyi.system.req.pangolin.PangolinSlotUpdateReq;
import com.ruoyi.system.service.pangolin.PangolinSlotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 抖音授权小程序广告位接口Controller
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/pangolin/slot")
public class PangolinSlotController {

    @Autowired
    private PangolinSlotService pangolinSlotService;

    /**
     * 查询广告位列表
     */
    @CrossOrigin
    @PostMapping("/query")
    public Result<JSONObject> slotQuery(@RequestBody PangolinSlotQueryReq req) {
        if (StringUtils.isBlank(req.getAppId())) {
            return ResultBuilder.fail("appId不能为空");
        }
        JSONObject result = pangolinSlotService.slotQuery(req);
        return ResultBuilder.success(result);
    }

    /**
     * 新增广告位
     */
    @CrossOrigin
    @PostMapping("/add")
    public Result<JSONObject> slotAdd(@RequestBody PangolinSlotAddReq req) {
        if (StringUtils.isBlank(req.getAppId())) {
            return ResultBuilder.fail("appId不能为空");
        }
        JSONObject result = pangolinSlotService.slotAdd(req);
        return ResultBuilder.success(result);
    }

    /**
     * 更新广告位状态
     */
    @CrossOrigin
    @PostMapping("/update")
    public Result<JSONObject> slotUpdate(@RequestBody PangolinSlotUpdateReq req) {
        if (StringUtils.isBlank(req.getAppId())) {
            return ResultBuilder.fail("appId不能为空");
        }
        JSONObject result = pangolinSlotService.slotUpdate(req);
        return ResultBuilder.success(result);
    }
}
