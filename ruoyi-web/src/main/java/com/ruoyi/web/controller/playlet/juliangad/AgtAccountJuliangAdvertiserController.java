package com.ruoyi.web.controller.playlet.juliangad;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.juliang.AgtAccountJuliangEntity;
import com.ruoyi.system.service.juliang.AgtAccountJuliangService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.juliangad.AgtAccountJuliangAdvertiserEntity;
import com.ruoyi.system.service.juliangad.AgtAccountJuliangAdvertiserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 巨量账户Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agtaccount/juliangad")
public class AgtAccountJuliangAdvertiserController {
    @Autowired
    private AgtAccountJuliangAdvertiserService agtAccountJuliangAdvertiserService;
    @Autowired
    private AgtAccountJuliangService agtAccountJuliangService;

    /**
     * 查询巨量账户列表
     */
    @PreAuthorize("@ss.hasPermi('agtaccount:juliangad:list')")
    @GetMapping("/list")
    public PageResult<AgtAccountJuliangAdvertiserEntity> list(AgtAccountJuliangAdvertiserEntity agtAccountJuliangAdvertiser) {
        TableSupport.startPage();
        List<AgtAccountJuliangAdvertiserEntity> list = agtAccountJuliangAdvertiserService.selectAgtAccountJuliangAdvertiserList(agtAccountJuliangAdvertiser);

        if (CollectionUtils.isNotEmpty(list)) {
            AgtAccountJuliangEntity entity = new AgtAccountJuliangEntity();
            entity.setAccountId(list.get(0).getAccountId());
            List<AgtAccountJuliangEntity> agtAccountJuliangEntities = agtAccountJuliangService.selectAgtAccountJuliangList(entity);
            if (CollectionUtils.isNotEmpty(agtAccountJuliangEntities)) {
                AgtAccountJuliangEntity juliangEntity = agtAccountJuliangEntities.get(0);
                for (AgtAccountJuliangAdvertiserEntity agtAccountJuliangAdvertiserEntity : list) {
                    agtAccountJuliangAdvertiserEntity.setAccountName(juliangEntity.getAccountName());
                }
            }
        }
        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出巨量账户列表
     */
    @PreAuthorize("@ss.hasPermi('agtaccount:juliangad:export')")
    @Log(title = "巨量账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtAccountJuliangAdvertiserEntity agtAccountJuliangAdvertiser) {
        List<AgtAccountJuliangAdvertiserEntity> list = agtAccountJuliangAdvertiserService.selectAgtAccountJuliangAdvertiserList(agtAccountJuliangAdvertiser);
        ExcelUtil<AgtAccountJuliangAdvertiserEntity> util = new ExcelUtil<AgtAccountJuliangAdvertiserEntity>(AgtAccountJuliangAdvertiserEntity.class);
        util.exportExcel(response, list, "巨量账户数据");
    }

    /**
     * 获取巨量账户详细信息
     */
    @PreAuthorize("@ss.hasPermi('agtaccount:juliangad:query')")
    @GetMapping(value = "/{id}")
    public Result<AgtAccountJuliangAdvertiserEntity> getInfo(@PathVariable("id") String id) {
        return ResultBuilder.success(agtAccountJuliangAdvertiserService.selectAgtAccountJuliangAdvertiserById(id));
    }

    /**
     * 新增巨量账户
     */
    @PreAuthorize("@ss.hasPermi('agtaccount:juliangad:add')")
    @Log(title = "巨量账户", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody AgtAccountJuliangAdvertiserEntity agtAccountJuliangAdvertiser) {
        return ResultBuilder.success(agtAccountJuliangAdvertiserService.insertAgtAccountJuliangAdvertiser(agtAccountJuliangAdvertiser));
    }

    /**
     * 修改巨量账户
     */
    @PreAuthorize("@ss.hasPermi('agtaccount:juliangad:edit')")
    @Log(title = "巨量账户", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody AgtAccountJuliangAdvertiserEntity agtAccountJuliangAdvertiser) {
        return ResultBuilder.success(agtAccountJuliangAdvertiserService.updateAgtAccountJuliangAdvertiser(agtAccountJuliangAdvertiser));
    }

    /**
     * 删除巨量账户
     */
    @PreAuthorize("@ss.hasPermi('agtaccount:juliangad:remove')")
    @Log(title = "巨量账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable String[] ids) {
        return ResultBuilder.success(agtAccountJuliangAdvertiserService.deleteAgtAccountJuliangAdvertiserByIds(ids));
    }
}
