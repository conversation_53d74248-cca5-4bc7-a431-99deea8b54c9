package com.ruoyi.web.controller.playlet.rank;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.bo.rank.PlayletConsumeRankParamBo;
import com.ruoyi.system.entity.rank.PlayletConsumeRankEntity;
import com.ruoyi.system.req.playlet.rank.PlayletConsumeRankImportReq;
import com.ruoyi.system.req.playlet.rank.PlayletConsumeRankListReq;
import com.ruoyi.system.req.playlet.rank.PlayletConsumeRankUpdateReq;
import com.ruoyi.system.service.rank.PlayletConsumeRankService;
import com.ruoyi.system.vo.playlet.rank.PlayletConsumeRankImportErrorVo;
import com.ruoyi.system.vo.playlet.rank.PlayletConsumeRankVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.core.page.TableSupport.PAGE_NUM;
import static com.ruoyi.common.core.page.TableSupport.PAGE_SIZE;

/**
 * 短剧热力榜Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/agtdata/rank")
public class PlayletConsumeRankController {

    @Autowired
    private PlayletConsumeRankService playletConsumeRankService;

    /**
     * 查询短剧热力榜列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:rank:list')")
    @GetMapping("/list")
    public PageResult<PlayletConsumeRankVo> list(PlayletConsumeRankListReq req) {
        if (!Objects.equals(req.getType(), 4) && StringUtils.isBlank(req.getDateStr())) {
            return ResultBuilder.successPage(PageInfo.EMPTY);
        }
        Date curDate = StringUtils.isNotBlank(req.getDateStr()) ? DateUtil.parseDate(req.getDateStr()) : null;
        PlayletConsumeRankParamBo param = BeanUtil.copyProperties(req, PlayletConsumeRankParamBo.class);
        switch (req.getType()) {
            case 1:
                param.setStartDate(curDate);
                param.setEndDate(curDate);
                break;
            case 2:
                param.setStartDate(curDate);
                param.setEndDate(DateUtil.offsetDay(curDate, 6));
                break;
            case 3:
                param.setStartDate(curDate);
                param.setEndDate(DateUtil.endOfMonth(curDate));
                break;
            case 4:
            default:
                break;
        }

        List<PlayletConsumeRankVo> list = playletConsumeRankService.selectList(param);
        // 构造分页数据
        int total = list.size();
        int pageNum = ServletUtils.getParameterToInt(PAGE_NUM);
        int pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
        list = ListUtil.page(pageNum - 1, pageSize, list);
        Page<PlayletConsumeRankVo> page = new Page<>(pageNum, pageSize);
        page.setTotal(total);
        PageInfo<PlayletConsumeRankVo> result = new PageInfo<>(page);
        result.setList(list);
        return ResultBuilder.successPage(result);
    }

    /**
     * 导出短剧热力榜列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:rank:export')")
    @Log(title = "短剧热力榜日榜", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlayletConsumeRankListReq req) {
        if (!Objects.equals(req.getType(), 4) && StringUtils.isBlank(req.getDateStr())) {
            ExcelUtil<PlayletConsumeRankVo> util = new ExcelUtil<>(PlayletConsumeRankVo.class);
            util.exportExcel(response, Collections.emptyList(), "短剧热力榜");
            return;
        }
        Date curDate = StringUtils.isNotBlank(req.getDateStr()) ? DateUtil.parseDate(req.getDateStr()) : null;
        PlayletConsumeRankParamBo param = BeanUtil.copyProperties(req, PlayletConsumeRankParamBo.class);
        switch (req.getType()) {
            case 1:
                param.setStartDate(curDate);
                param.setEndDate(curDate);
                break;
            case 2:
                param.setStartDate(curDate);
                param.setEndDate(DateUtil.offsetDay(curDate, 6));
                break;
            case 3:
                param.setStartDate(curDate);
                param.setEndDate(DateUtil.endOfMonth(curDate));
                break;
            case 4:
            default:
                break;
        }

        List<PlayletConsumeRankVo> list = playletConsumeRankService.selectList(param);
        ExcelUtil<PlayletConsumeRankVo> util = new ExcelUtil<>(PlayletConsumeRankVo.class);
        util.exportExcel(response, list, "短剧热力榜");
    }

    /**
     * 短剧热力榜导入
     */
    @PreAuthorize("@ss.hasPermi('agtdata:rank:import')")
    @Log(title = "短剧热力榜日榜", businessType = BusinessType.INSERT)
    @PostMapping("import")
    public Result<List<PlayletConsumeRankImportErrorVo>> rankImport(PlayletConsumeRankImportReq req) {
        if (Objects.isNull(req.getFile())) {
            return ResultBuilder.fail(ErrorCode.ARGS);
        }
        if (StringUtils.isBlank(req.getDateStr())) {
            req.setDateStr(ReUtil.getGroup0("\\d{4}-\\d{2}-\\d{2}", req.getFile().getOriginalFilename()));
        }

        Date curDate = DateUtil.parseDate(req.getDateStr());
        List<PlayletConsumeRankEntity> list = new ArrayList<>();
        List<PlayletConsumeRankImportErrorVo> errorList = new ArrayList<>();
        try {
            cn.hutool.poi.excel.ExcelUtil.readBySax(req.getFile().getInputStream(), -1, (sheetIndex, rowIndex, rows) -> {
                if (Objects.equals(rowIndex, 0L) || Objects.isNull(rows.get(0))) {
                    return;
                }
                PlayletConsumeRankEntity param = new PlayletConsumeRankEntity();
                param.setCurDate(curDate);
                param.setPlayletName(Convert.toStr(rows.get(1)));
                param.setTag(Convert.toStr(rows.get(2)));
                param.setRelatedParty(Convert.toStr(rows.get(3)));
                param.setContractor(Convert.toStr(rows.get(4)));
                param.setMaterialCount(Convert.toInt(Convert.toDouble(rows.get(5))));
                param.setConsumeNum(Convert.toInt(Convert.toDouble(rows.get(6))));
                param.setHint(Convert.toStr(rows.get(8)));
                if (rows.size() > 9) {
                    param.setPromotionCount(Convert.toInt(Convert.toDouble(rows.get(9))));
                }
                if (StringUtils.isBlank(param.getPlayletName())) {
                    errorList.add(new PlayletConsumeRankImportErrorVo(rowIndex, "", "短剧名称不能为空"));
                } else if (null == param.getConsumeNum()) {
                    errorList.add(new PlayletConsumeRankImportErrorVo(rowIndex, param.getPlayletName(), "热力值不能为空"));
                } else if (null == param.getMaterialCount()) {
                    errorList.add(new PlayletConsumeRankImportErrorVo(rowIndex, param.getPlayletName(), "素材数不能为空"));
                } else {
                    list.add(param);
                }
            });
        } catch (Exception e) {
            log.error("短剧热力榜解析excel异常,e:", e);
            return ResultBuilder.fail("Excel解析异常");
        }
        if (CollectionUtils.isEmpty(errorList) && CollectionUtils.isNotEmpty(list)) {
            playletConsumeRankService.batchInsertOrUpdate(list);
        }
        return ResultBuilder.success(errorList);
    }

    @Anonymous
    @PostMapping("update")
    public void update(@RequestBody PlayletConsumeRankUpdateReq req) {
        log.info("更新短剧热力榜: {}", JSON.toJSONString(req));
        if (StringUtils.isBlank(req.getDate()) || StringUtils.isBlank(req.getName())) {
            return;
        }

        try{
            Date curDate = DateUtil.parseDate(req.getDate());
            String playletName = StringUtils.replace(req.getName(), ",", "，").trim();
            PlayletConsumeRankEntity entity = playletConsumeRankService.selectBy(curDate, playletName);
            if (null == entity) {
                entity = new PlayletConsumeRankEntity();
            }
            entity.setCurDate(curDate);
            entity.setPlayletName(playletName);
            entity.setMaterialCount(Convert.toInt(req.getMaterial()));
            if (Convert.toDouble(req.getHeat()) != null) {
                entity.setConsumeNum((int) (Convert.toDouble(req.getHeat()) * 10000));
            }
            entity.setTag(StringUtils.replace(req.getTag(), "|", ","));
            entity.setRelatedParty(StringUtils.replace(req.getPublisher(), "欢迎认领", ""));
            entity.setContractor(StringUtils.replace(req.getMaker(), "欢迎认领", ""));
            if (null != entity.getId()) {
                playletConsumeRankService.updatePlayletConsumeRank(entity);
            } else {
                playletConsumeRankService.insert(entity);
            }
        }catch (Exception e){
            log.error("更新短剧热力榜异常, req:{}", JSON.toJSONString(req), e);
        }
    }
}
