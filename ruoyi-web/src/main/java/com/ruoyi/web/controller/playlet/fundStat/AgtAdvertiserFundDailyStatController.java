package com.ruoyi.web.controller.playlet.fundStat;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.fundStat.AgtAdvertiserFundDailyStatEntity;
import com.ruoyi.system.service.fundStat.AgtAdvertiserFundDailyStatService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 广告账户日流水Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agtdata/fundStat")
public class AgtAdvertiserFundDailyStatController {
    @Autowired
    private AgtAdvertiserFundDailyStatService agtAdvertiserFundDailyStatService;

    /**
     * 查询广告账户日流水列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fundStat:list')")
    @GetMapping("/list")
    public PageResult<AgtAdvertiserFundDailyStatEntity> list(AgtAdvertiserFundDailyStatEntity agtAdvertiserFundDailyStat) {
        TableSupport.startPage();
        List<AgtAdvertiserFundDailyStatEntity> list = agtAdvertiserFundDailyStatService.selectAgtAdvertiserFundDailyStatList(agtAdvertiserFundDailyStat);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出广告账户日流水列表
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fundStat:export')")
    @Log(title = "广告账户日流水", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtAdvertiserFundDailyStatEntity agtAdvertiserFundDailyStat) {
        List<AgtAdvertiserFundDailyStatEntity> list = agtAdvertiserFundDailyStatService.selectAgtAdvertiserFundDailyStatList(agtAdvertiserFundDailyStat);
        ExcelUtil<AgtAdvertiserFundDailyStatEntity> util = new ExcelUtil<AgtAdvertiserFundDailyStatEntity>(AgtAdvertiserFundDailyStatEntity.class);
        util.exportExcel(response, list, "广告账户日流水数据");
    }

    /**
     * 获取广告账户日流水详细信息
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fundStat:query')")
    @GetMapping(value = "/{id}")
    public Result<AgtAdvertiserFundDailyStatEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(agtAdvertiserFundDailyStatService.selectAgtAdvertiserFundDailyStatById(id));
    }

    /**
     * 新增广告账户日流水
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fundStat:add')")
    @Log(title = "广告账户日流水", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody AgtAdvertiserFundDailyStatEntity agtAdvertiserFundDailyStat) {
        return ResultBuilder.success(agtAdvertiserFundDailyStatService.insertAgtAdvertiserFundDailyStat(agtAdvertiserFundDailyStat));
    }

    /**
     * 修改广告账户日流水
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fundStat:edit')")
    @Log(title = "广告账户日流水", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody AgtAdvertiserFundDailyStatEntity agtAdvertiserFundDailyStat) {
        return ResultBuilder.success(agtAdvertiserFundDailyStatService.updateAgtAdvertiserFundDailyStat(agtAdvertiserFundDailyStat));
    }

    /**
     * 删除广告账户日流水
     */
    @PreAuthorize("@ss.hasPermi('agtdata:fundStat:remove')")
    @Log(title = "广告账户日流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(agtAdvertiserFundDailyStatService.deleteAgtAdvertiserFundDailyStatByIds(ids));
    }
}
