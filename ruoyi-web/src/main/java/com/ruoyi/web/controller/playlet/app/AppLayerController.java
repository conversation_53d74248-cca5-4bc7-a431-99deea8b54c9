package com.ruoyi.web.controller.playlet.app;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.domain.PageResult;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.collect.Lists;
import com.ruoyi.system.bo.playlet.AnalyzePhoneBo;
import com.ruoyi.system.bo.playlet.ExportPhoneBo;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.AppLayerStatusEnum;
import com.ruoyi.common.exception.BizRuntimeException;
import com.ruoyi.common.param.IdParam;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.layer.AppLayerConfigEntity;
import com.ruoyi.system.entity.layer.AppLayerUserRelationEntity;
import com.ruoyi.system.entity.user.UserEntity;
import com.ruoyi.system.entity.video.VideoGroupEntity;
import com.ruoyi.system.service.layer.AppLayerConfigService;
import com.ruoyi.system.service.layer.AppLayerUserRelationService;
import com.ruoyi.system.service.user.UserService;
import com.ruoyi.system.service.video.VideoGroupService;
import com.ruoyi.system.req.playlet.app.LayerEditReq;
import com.ruoyi.system.req.playlet.app.LayerReq;
import com.ruoyi.system.req.playlet.app.LayerSortUpdateReq;
import com.ruoyi.system.req.playlet.app.LayerUpdateStatusReq;
import com.ruoyi.system.req.playlet.video.VideoGroupSearchReq;
import com.ruoyi.system.vo.playlet.app.AnalyzePhoneVo;
import com.ruoyi.system.vo.playlet.app.LayerListVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * app弹窗
 *
 * <AUTHOR>
 * @date 2023/10/23 13:59
 */
@RestController
@RequestMapping("/app/layer")
public class AppLayerController {

    @Autowired
    private AppLayerConfigService appLayerConfigService;

    @Autowired
    private AppLayerUserRelationService appLayerUserRelationService;

    @Autowired
    private UserService userService;
    @Autowired
    private VideoGroupService videoGroupService;

    /**
     * 弹窗列表
     */
    @GetMapping("list")
    public PageResult<LayerListVo> list(LayerReq req) {
        if (StringUtils.isNotBlank(req.getVideoGroupName()) || NumberUtils.isNotNullOrLteZero(req.getVideoGroupId())) {
            VideoGroupSearchReq videoGroupSearchReq = new VideoGroupSearchReq();
            videoGroupSearchReq.setTitle(req.getVideoGroupName());
            videoGroupSearchReq.setId(req.getVideoGroupId());
            List<VideoGroupEntity> groupEntities = videoGroupService.selectListByReq(videoGroupSearchReq);
            if (CollectionUtils.isEmpty(groupEntities)) {
                return ResultBuilder.successPage(PageInfoUtils.buildReturnList(Collections.EMPTY_LIST));
            }
            req.setVideoGroupIds(groupEntities.stream().map(VideoGroupEntity::getId).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(req.getPhone())) {
            UserEntity userEntity = userService.selectByPhone(req.getPhone());
            if (Objects.isNull(userEntity)) {
                return ResultBuilder.successPage(PageInfoUtils.buildReturnList(Collections.EMPTY_LIST));
            }
            List<Long> layerConfigIds = appLayerUserRelationService.selectIdsByUserId(userEntity.getId());
            if (CollectionUtils.isEmpty(layerConfigIds)) {
                return ResultBuilder.successPage(PageInfoUtils.buildReturnList(Collections.EMPTY_LIST));
            }
            req.setLayerConfigIds(layerConfigIds);
        }
        TableSupport.startPage();
        List<AppLayerConfigEntity> resultList = appLayerConfigService.selectListByReq(req).stream().collect(Collectors.toList());
        List<Long> videoGroupIds = resultList.stream().map(AppLayerConfigEntity::getVideoGroupId).collect(Collectors.toList());
        Map<Long, String> videoGroupTitleMap = videoGroupService.selectVideoGroupTitleMap(videoGroupIds);

        return ResultBuilder.successPage(PageInfoUtils.dto2Vo(resultList, result -> {
            LayerListVo layerListVo = BeanUtil.copyProperties(result, LayerListVo.class);
            layerListVo.setVideoGroupName(videoGroupTitleMap.get(result.getVideoGroupId()));
            return layerListVo;
        }));
    }

    /**
     * 上/下线
     */
    @PostMapping("updateStatus")
    public Result<Boolean> updateStatus(@RequestBody @Validated LayerUpdateStatusReq req) {
        AppLayerConfigEntity config = BeanUtil.copyProperties(req, AppLayerConfigEntity.class);
        if (Objects.equals(req.getStatus(), AppLayerStatusEnum.ONLINE.getStatus())) {
            //新上线的默认排在最后
            //获取上线的配置最大排序顺序
            int sort = appLayerConfigService.selectMaxSortByStatus(AppLayerStatusEnum.ONLINE.getStatus());
            config.setSort(sort + 1);
        } else {
            config.setSort(0);
        }
        return ResultBuilder.success(appLayerConfigService.updateById(config));
    }

    /**
     * 新增/编辑
     */
    @PostMapping("saveOrUpdate")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveOrUpdate(@RequestBody @Valid LayerEditReq req) {
        AppLayerConfigEntity config = BeanUtil.copyProperties(req, AppLayerConfigEntity.class);

        List<UserEntity> userEntities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(req.getPhones())) {
            Lists.partition(req.getPhones(), 1000).stream().forEach(phones -> {
                List<UserEntity> list = userService.selectListByPhones(req.getPhones());
                if (CollectionUtils.isNotEmpty(list)) {
                    userEntities.addAll(list);
                }
            });
        }
        List<Long> userIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userEntities)) {
            userIds = userEntities.stream().map(UserEntity::getId).collect(Collectors.toList());
        }

        if (NumberUtils.isNullOrLteZero(req.getId()) && CollectionUtils.isEmpty(userEntities)) {
            //无效的号码列表,新增时必须要有号码
            throw new BizRuntimeException(ErrorCode.INVALID_PHONE);
        }
        boolean success;
        if (NumberUtils.isNullOrLteZero(config.getId())) {
            success = appLayerConfigService.insert(config);
        } else {
            success = appLayerConfigService.updateById(config);
            if (Objects.nonNull(req.getPhones()) && req.getPhones().size() == 0) {
                //上传了空数组，则清空之前的号码
                appLayerUserRelationService.deleteByLayerConfigId(config.getId());
            }
            //删除关联
            appLayerUserRelationService.deleteByConfigIdAndUserIds(config.getId(), userIds);
        }
        //绑定新关联关系
        List<AppLayerUserRelationEntity> insertList = userIds.stream().map(userId -> {
            AppLayerUserRelationEntity entity = new AppLayerUserRelationEntity();
            entity.setUserId(userId);
            entity.setLayerConfigId(config.getId());
            return entity;
        }).collect(Collectors.toList());
        appLayerUserRelationService.batchInsert(insertList);
        return ResultBuilder.success(success);
    }

    /**
     * 解析手机号
     */
    @PostMapping("analyzePhone")
    public Result<AnalyzePhoneVo> analyzePhone(MultipartFile file) throws IOException {
        if (Objects.isNull(file)) {
            throw new BizRuntimeException(ErrorCode.INVALID_FILE);
        }
        AnalyzePhoneVo vo = new AnalyzePhoneVo();
        List<String> phones = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), AnalyzePhoneBo.class, new ReadListener<AnalyzePhoneBo>() {

            @Override
            public void invoke(AnalyzePhoneBo data, AnalysisContext context) {
                phones.add(data.getPhone());
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();
        if (CollectionUtils.isEmpty(phones)) {
            return ResultBuilder.success(vo);
        }
        List<UserEntity> userEntities = new ArrayList<>();
        Lists.partition(phones, 1000).stream().forEach(phoneList -> {
            List<UserEntity> list = userService.selectListByPhones(phoneList);
            if (CollectionUtils.isNotEmpty(list)) {
                userEntities.addAll(list);
            }
        });
        List<String> successList = userEntities.stream().map(UserEntity::getChannelUserId).collect(Collectors.toList());
        vo.setValidList(successList);
        phones.removeAll(successList);
        vo.setInvalidList(phones);
        return ResultBuilder.success(vo);
    }


    /**
     * 删除
     */
    @PostMapping("delete")
    public Result<Boolean> delete(@RequestBody @Valid IdParam param) {
        boolean success = appLayerConfigService.deleteById(param.getId());
        //删除关联了该弹窗的用户关联表
        appLayerUserRelationService.deleteByLayerConfigId(param.getId());
        return ResultBuilder.success(success);
    }

    /**
     * 更新排序
     *
     * @param req
     * @return
     */
    @PostMapping("updateSort")
    public Result<Boolean> updateSort(@RequestBody @Valid LayerSortUpdateReq req) {
        Long moveConfigId = req.getMoveId();
        List<AppLayerConfigEntity> entities = appLayerConfigService.selectAll();
        List<Long> oldIdsSort = entities.stream().map(AppLayerConfigEntity::getId).collect(Collectors.toList());
        List<Long> newIdsSort = req.getIds();
        if (newIdsSort.size() != oldIdsSort.size()) {
            //按照新的排序，把未筛选出的id根据之前的位置插入进去，比如原本是12345，筛选出1245，5挪动到4前面，展示就是1254，取消筛选后展示12354
            int index = newIdsSort.indexOf(moveConfigId);
            oldIdsSort.remove(moveConfigId);
            if (index != newIdsSort.size() - 1) {
                //以移动的元素的后面一个为参照物，把移动的元素插入到参照物的前面
                oldIdsSort.add(oldIdsSort.indexOf(newIdsSort.get(index + 1)), moveConfigId);
            } else {
                //如果是最后一个，直接插入到最后
                oldIdsSort.add(moveConfigId);
            }
        } else {
            oldIdsSort = newIdsSort;
        }
        List<Long> finalOldIdsSort = oldIdsSort;
        List<AppLayerConfigEntity> updateSortList = oldIdsSort.stream().map(id -> {
            AppLayerConfigEntity entity = new AppLayerConfigEntity();
            entity.setId(id);
            entity.setSort(finalOldIdsSort.indexOf(id) + 1);
            return entity;
        }).collect(Collectors.toList());
        appLayerConfigService.batchUpdateSort(updateSortList);
        //更新排序
        return ResultBuilder.success(true);
    }

    /**
     * 下载手机号
     */
    @GetMapping("downloadPhone")
    public void downloadPhone(Long id, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("弹窗用户号码-" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<ExportPhoneBo> phones = new ArrayList<>();
        Integer pageSize = 1000;
        List<AppLayerUserRelationEntity> list;
        Long lastId = 0L;
        while (CollectionUtils.isNotEmpty(list = appLayerUserRelationService.selectUserIdsByConfigId(id, pageSize, lastId))) {
            lastId = list.get(list.size() - 1).getId();
            List<Long> userIds = list.stream().map(AppLayerUserRelationEntity::getUserId).collect(Collectors.toList());
            List<UserEntity> userEntities = userService.selectListByIds(userIds);
            List<String> phoneList = userEntities.stream().map(UserEntity::getChannelUserId).collect(Collectors.toList());
            List<ExportPhoneBo> phoneBos = phoneList.stream().map(phone -> new ExportPhoneBo().setPhone(phone)).collect(Collectors.toList());
            phones.addAll(phoneBos);
        }
        EasyExcel.write(response.getOutputStream(), ExportPhoneBo.class).sheet("号码").doWrite(phones);
    }

}
