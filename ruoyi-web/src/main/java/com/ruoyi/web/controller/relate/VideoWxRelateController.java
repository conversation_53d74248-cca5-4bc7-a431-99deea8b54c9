package com.ruoyi.web.controller.relate;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.relate.VideoWxRelateEntity;
import com.ruoyi.system.service.relate.VideoWxRelateService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 短剧剧集和微信小程序关联Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/relate/videoWxRelate")
public class VideoWxRelateController {
    @Autowired
    private VideoWxRelateService videoWxRelateService;

    /**
     * 查询短剧剧集和微信小程序关联列表
     */
    @PreAuthorize("@ss.hasPermi('relate:videoWxRelate:list')")
    @GetMapping("/list")
    public PageResult<VideoWxRelateEntity> list(VideoWxRelateEntity videoWxRelate) {
        TableSupport.startPage();
        List<VideoWxRelateEntity> list = videoWxRelateService.selectVideoWxRelateList(videoWxRelate);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出短剧剧集和微信小程序关联列表
     */
    @PreAuthorize("@ss.hasPermi('relate:videoWxRelate:export')")
    @Log(title = "短剧剧集和微信小程序关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VideoWxRelateEntity videoWxRelate) {
        List<VideoWxRelateEntity> list = videoWxRelateService.selectVideoWxRelateList(videoWxRelate);
        ExcelUtil<VideoWxRelateEntity> util = new ExcelUtil<VideoWxRelateEntity>(VideoWxRelateEntity.class);
        util.exportExcel(response, list, "短剧剧集和微信小程序关联数据");
    }

    /**
     * 获取短剧剧集和微信小程序关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('relate:videoWxRelate:query')")
    @GetMapping(value = "/{id}")
    public Result<VideoWxRelateEntity> getInfo(@PathVariable("id") Integer id) {
        return ResultBuilder.success(videoWxRelateService.selectVideoWxRelateById(id));
    }

    /**
     * 新增短剧剧集和微信小程序关联
     */
    @PreAuthorize("@ss.hasPermi('relate:videoWxRelate:add')")
    @Log(title = "短剧剧集和微信小程序关联", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody VideoWxRelateEntity videoWxRelate) {
        return ResultBuilder.success(videoWxRelateService.insertVideoWxRelate(videoWxRelate));
    }

    /**
     * 修改短剧剧集和微信小程序关联
     */
    @PreAuthorize("@ss.hasPermi('relate:videoWxRelate:edit')")
    @Log(title = "短剧剧集和微信小程序关联", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody VideoWxRelateEntity videoWxRelate) {
        return ResultBuilder.success(videoWxRelateService.updateVideoWxRelate(videoWxRelate));
    }

    /**
     * 删除短剧剧集和微信小程序关联
     */
    @PreAuthorize("@ss.hasPermi('relate:videoWxRelate:remove')")
    @Log(title = "短剧剧集和微信小程序关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Integer[] ids) {
        return ResultBuilder.success(videoWxRelateService.deleteVideoWxRelateByIds(ids));
    }
}
