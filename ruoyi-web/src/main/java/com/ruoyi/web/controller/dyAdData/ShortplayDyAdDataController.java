package com.ruoyi.web.controller.dyAdData;

import java.util.List;

import com.ruoyi.common.core.page.TableSupport;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.PageInfoUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.dyAdData.ShortplayDyAdDataEntity;
import com.ruoyi.system.service.dyAdData.ShortplayDyAdDataService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.PageResult;

/**
 * 抖音短剧小程序广告埋点Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dyAdData/shortplayDyAdData")
public class ShortplayDyAdDataController {
    @Autowired
    private ShortplayDyAdDataService shortplayDyAdDataService;

    /**
     * 查询抖音短剧小程序广告埋点列表
     */
    @PreAuthorize("@ss.hasPermi('dyAdData:shortplayDyAdData:list')")
    @GetMapping("/list")
    public PageResult<ShortplayDyAdDataEntity> list(ShortplayDyAdDataEntity shortplayDyAdData) {
        TableSupport.startPage();
        List<ShortplayDyAdDataEntity> list = shortplayDyAdDataService.selectShortplayDyAdDataList(shortplayDyAdData);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 导出抖音短剧小程序广告埋点列表
     */
    @PreAuthorize("@ss.hasPermi('dyAdData:shortplayDyAdData:export')")
    @Log(title = "抖音短剧小程序广告埋点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortplayDyAdDataEntity shortplayDyAdData) {
        List<ShortplayDyAdDataEntity> list = shortplayDyAdDataService.selectShortplayDyAdDataList(shortplayDyAdData);
        ExcelUtil<ShortplayDyAdDataEntity> util = new ExcelUtil<ShortplayDyAdDataEntity>(ShortplayDyAdDataEntity.class);
        util.exportExcel(response, list, "抖音短剧小程序广告埋点数据");
    }

    /**
     * 获取抖音短剧小程序广告埋点详细信息
     */
    @PreAuthorize("@ss.hasPermi('dyAdData:shortplayDyAdData:query')")
    @GetMapping(value = "/{id}")
    public Result<ShortplayDyAdDataEntity> getInfo(@PathVariable("id") Long id) {
        return ResultBuilder.success(shortplayDyAdDataService.selectShortplayDyAdDataById(id));
    }

    /**
     * 新增抖音短剧小程序广告埋点
     */
    @PreAuthorize("@ss.hasPermi('dyAdData:shortplayDyAdData:add')")
    @Log(title = "抖音短剧小程序广告埋点", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody ShortplayDyAdDataEntity shortplayDyAdData) {
        return ResultBuilder.success(shortplayDyAdDataService.insertShortplayDyAdData(shortplayDyAdData));
    }

    /**
     * 修改抖音短剧小程序广告埋点
     */
    @PreAuthorize("@ss.hasPermi('dyAdData:shortplayDyAdData:edit')")
    @Log(title = "抖音短剧小程序广告埋点", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> edit(@RequestBody ShortplayDyAdDataEntity shortplayDyAdData) {
        return ResultBuilder.success(shortplayDyAdDataService.updateShortplayDyAdData(shortplayDyAdData));
    }

    /**
     * 删除抖音短剧小程序广告埋点
     */
    @PreAuthorize("@ss.hasPermi('dyAdData:shortplayDyAdData:remove')")
    @Log(title = "抖音短剧小程序广告埋点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@PathVariable Long[] ids) {
        return ResultBuilder.success(shortplayDyAdDataService.deleteShortplayDyAdDataByIds(ids));
    }
}
