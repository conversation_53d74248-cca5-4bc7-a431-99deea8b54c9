package com.ruoyi.web.controller.playlet.aiaa;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.system.bo.playlet.JlClickAppCallBackData;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.UAUtils;
import com.ruoyi.system.constant.enums.PlatformType;
import com.ruoyi.system.entity.aiaa.AdPlatformDataEntity;
import com.ruoyi.system.service.aiaa.AdPlatformDataService;
import com.ruoyi.system.req.playlet.aiaa.JlAppCallBackReq;
import com.ruoyi.system.req.playlet.aiaa.JlClickAppCallBackReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

/**
 * 广告平台对接接口
 *
 * <AUTHOR> Yang
 * @Date 2023/7/31 13:47
 * @Description 广告平台对接接口
 */

@RestController
@RequestMapping("/appCallBack")
@Slf4j
public class AppCallBackController {


    @Autowired
    private AdPlatformDataService adPlatformDataService;


    @Autowired
    private RedisCache redisCacheClient;


    /**
     * 广告平台调用接口
     *
     * @param req 广告平台数据
     * @return 是否成功
     */
    @PostMapping("/data")
    public Result<Boolean> fetchData(@RequestBody @Valid JlAppCallBackReq req, HttpServletRequest request) {
        log.info("巨量传输数据:{}", req.toString());
        String clientIP = ServletUtil.getClientIP(request);
        String userAgent = ServletUtil.getHeader(request, "User-Agent", "UTF-8");
        String ua = UAUtils.handleUserAgent(userAgent);
        AdPlatformDataEntity adPlatformDataEntity = BeanUtil.copyProperties(req, AdPlatformDataEntity.class);
        adPlatformDataEntity.setIp(clientIP);
        adPlatformDataEntity.setUserAgent(ua);
        boolean insert = adPlatformDataService.insert(adPlatformDataEntity);
        return ResultBuilder.success(insert);
    }

    /**
     * 巨量点击检测链接
     *
     * @param req 数据
     * @return 是否成功
     */
    @GetMapping("/nh/data")
    public Result<Boolean> fetchDataFromNh(JlClickAppCallBackReq req) {
        log.info("巨量点击检测传输数据:{}", req.toString());
        if (req.getPlatformType() == null) {
            req.setPlatformType(PlatformType.JULIANG.getCode());
        }
        AdPlatformDataEntity adPlatformDataEntity = BeanUtil.copyProperties(req, AdPlatformDataEntity.class);
        adPlatformDataEntity.setIp(req.getIp());
        String userAgent = req.getUserAgent();
        String ua = UAUtils.handleUserAgent(userAgent);
        adPlatformDataEntity.setUserAgent(ua);
        boolean insert = adPlatformDataService.insert(adPlatformDataEntity);
        String ipuaKey = EngineRedisKeyFactory.IA002.join(req.getIp(), ua);
        req.setViewAdCount(0);
        JlClickAppCallBackData jlClickAppCallBackData = BeanUtil.copyProperties(req, JlClickAppCallBackData.class);
        redisCacheClient.setCacheObject(ipuaKey, JSON.toJSONString(jlClickAppCallBackData), 2, TimeUnit.DAYS);
        return ResultBuilder.success(insert);
    }


}
