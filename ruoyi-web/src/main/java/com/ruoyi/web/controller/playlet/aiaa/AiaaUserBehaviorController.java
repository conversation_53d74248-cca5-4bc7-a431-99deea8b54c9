package com.ruoyi.web.controller.playlet.aiaa;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.entity.aiaa.UserBehaviorEntity;
import com.ruoyi.system.service.aiaa.UserBehaviorService;
import com.ruoyi.system.req.playlet.aiaa.UserBehaviorReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户行为记录Controller
 *
 * <AUTHOR> Yang
 * @Date 2023/8/15 18:20
 * @Description
 */
@RestController
@RequestMapping("/aiaa/behavior")
public class AiaaUserBehaviorController {
    @Autowired
    private UserBehaviorService userBehaviorService;

    /**
     * 用户行为记录埋点
     *
     * @param req
     * @return
     */
    @PostMapping("log")
    public Result<Boolean> logUserBehavior(@RequestBody @Validated UserBehaviorReq req, HttpServletRequest request) {
        if (req.getUserId() < 0) {
            return ResultBuilder.success(false);
        }
        UserBehaviorEntity userBehaviorEntity = BeanUtil.copyProperties(req, UserBehaviorEntity.class);
        String ipAddr = IpUtils.getIpAddr(request);
        userBehaviorEntity.setUserIp(ipAddr);
        boolean save = userBehaviorService.insert(userBehaviorEntity);
        return ResultBuilder.success(save);
    }
}
