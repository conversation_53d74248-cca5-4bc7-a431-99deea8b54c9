package com.ruoyi.web.controller.shortplay;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.shortplay.GdtSiteSetEnum;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.vo.shortplay.GdtSiteSetOptionVo;
import com.ruoyi.system.service.shortplay.stat.GdtSiteSetStatService;
import com.ruoyi.system.service.shortplay.app.ShortplayAppService;
import com.ruoyi.system.entity.shortplay.app.ShortplayAppEntity;
import com.ruoyi.system.vo.shortplay.GdtSiteSetStatVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 广点通版位用户IPU统计 Controller
 */
@RestController
@RequestMapping("/shortplay/gdtSiteSetStat")
public class GdtSiteSetStatController extends BaseController {
    
    @Autowired
    private GdtSiteSetStatService gdtSiteSetStatService;

    @Autowired
    private ShortplayAppService shortplayAppService;
    
    /**
     * 查询广点通IPU统计数据
     *
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @param appIds 小程序ID列表，多个以逗号分隔
     * @param siteSetCodes 版位代码列表，多个以逗号分隔
     * @param withSiteSetDimension 是否按版位维度展示，true-按版位维度，false-按应用维度聚合
     * @param withDateDimension 是否按日期维度展示，true-按日期维度，false-不按日期维度聚合
     * @return 统计数据
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtSiteSetStat:query')")
    public PageResult<GdtSiteSetStatVO> list(
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestParam(value = "appIds", required = false) String appIds,
            @RequestParam(value = "siteSetCodes", required = false) String siteSetCodes,
            @RequestParam(value = "withSiteSetDimension", required = false, defaultValue = "true") boolean withSiteSetDimension,
            @RequestParam(value = "withDateDimension", required = false, defaultValue = "true") boolean withDateDimension
           ) {

        TableSupport.startPage();
        List<GdtSiteSetStatVO> statVOList = gdtSiteSetStatService.queryGdtSiteSetStatData(
                startDate, endDate, appIds, siteSetCodes, withSiteSetDimension, withDateDimension);

        return ResultBuilder.successPage(PageInfoUtils.buildReturnList(statVOList));
    }

    /**
     * 导出广点通IPU统计数据
     *
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @param appIds 小程序ID列表，多个以逗号分隔
     * @param siteSetCodes 版位代码列表，多个以逗号分隔
     * @param withSiteSetDimension 是否按版位维度展示，true-按版位维度，false-按应用维度聚合
     * @param withDateDimension 是否按日期维度展示，true-按日期维度，false-不按日期维度聚合
     * @param response HTTP响应对象
     */
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('wxiaa:gdtSiteSetStat:export')")
    public void export(
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestParam(value = "appIds", required = false) String appIds,
            @RequestParam(value = "siteSetCodes", required = false) String siteSetCodes,
            @RequestParam(value = "withSiteSetDimension", required = false, defaultValue = "true") boolean withSiteSetDimension,
            @RequestParam(value = "withDateDimension", required = false, defaultValue = "true") boolean withDateDimension,
            HttpServletResponse response) {

        List<GdtSiteSetStatVO> statVOList = gdtSiteSetStatService.queryGdtSiteSetStatData(
                startDate, endDate, appIds, siteSetCodes, withSiteSetDimension, withDateDimension);

        ExcelUtil<GdtSiteSetStatVO> util = new ExcelUtil<>(GdtSiteSetStatVO.class);
        util.exportExcel(response, statVOList, "广点通版位IPU统计数据");
    }

    /**
     * 获取小程序选项列表
     * 用于前端下拉框选择
     * 
     * @return 小程序选项列表
     */
    @GetMapping("/appOptions")
    public Result<List<GdtSiteSetOptionVo>> getAppOptions() {
        // 获取微信小程序选项 - 只查询微信平台的小程序
        List<ShortplayAppEntity> appList = shortplayAppService.selectShortplayAppList(
            new ShortplayAppEntity().setAppplatform("weixin"));

        List<GdtSiteSetOptionVo> appOptions = new ArrayList<>();
        for (ShortplayAppEntity app : appList) {
            if (app.getAppid() != null && app.getAppname() != null) {
                GdtSiteSetOptionVo option = new GdtSiteSetOptionVo()
                    .setValue(app.getAppid())
                    .setLabel(app.getAppname());
                appOptions.add(option);
            }
        }

        return ResultBuilder.success(appOptions);
    }
    
    /**
     * 获取版位代码选项列表
     * 用于前端下拉框选择
     * 
     * @return 版位代码选项列表
     */
    @GetMapping("/siteSetOptions")
    public Result<List<GdtSiteSetOptionVo>> getSiteSetOptions() {
        // 获取版位代码选项 - 使用枚举值
        List<GdtSiteSetOptionVo> siteSetOptions = new ArrayList<>();
        for (GdtSiteSetEnum siteSet : GdtSiteSetEnum.values()) {
            GdtSiteSetOptionVo option = new GdtSiteSetOptionVo()
                .setValue(siteSet.getCode())
                .setLabel(siteSet.getDesc());
            siteSetOptions.add(option);
        }

        return ResultBuilder.success(siteSetOptions);
    }
} 