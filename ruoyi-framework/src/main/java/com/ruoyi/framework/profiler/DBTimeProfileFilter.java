package com.ruoyi.framework.profiler;



import com.ruoyi.common.utils.RequestUtils;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 此Filter用于对超时请求打印warn日志
 */
public class DBTimeProfileFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        //do nothing
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest)servletRequest;

        //不允许浏览器动态嗅探响应内容的类型
//        ((HttpServletResponse)servletResponse).setHeader("x-content-type-options","nosniff");//这个加了对于jsonp请求会有问题。jsonp需要返回类型为text/javascript才行，先去掉

        String uri = RequestUtils.getRequestPath(request);
//        if(uri.equals("/hystrix.stream")){//对于一些特殊请求不进行计时.
//            filterChain.doFilter(servletRequest, servletResponse);
//            return;
//        }

        try {
            DBTimeProfile.start();
            filterChain.doFilter(servletRequest, servletResponse);
        }finally {
            DBTimeProfile.end(uri);
        }
    }

    @Override
    public void destroy() {
        //do nothing
    }

}
