package com.ruoyi.framework.profiler;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * date 2019/10/30-3:18 下午
 */
@Slf4j
@Component
@Aspect
public class DBTimeProfilerAspect {

    @After("@annotation(com.ruoyi.framework.profiler.DBTimeProfilerThreshold)")
    public void setCurrentThreshold(JoinPoint joinPoint){
        MethodSignature methodSignature = (MethodSignature)joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        DBTimeProfilerThreshold threshold =  method.getAnnotation(DBTimeProfilerThreshold.class);
        DBTimeProfile.setCurrentThreshold(threshold.value());
    }
}
