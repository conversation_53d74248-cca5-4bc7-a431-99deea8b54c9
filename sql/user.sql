-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('短剧分销人员', '2070', '1', 'user', 'agtaccount/user/index', 1, 0, 'C', '0', '0', 'agtaccount:user:list', '#', 'admin', sysdate(), '', null, '短剧分销人员菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('短剧分销人员查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'agtaccount:user:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('短剧分销人员新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'agtaccount:user:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('短剧分销人员修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'agtaccount:user:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('短剧分销人员删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'agtaccount:user:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('短剧分销人员导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'agtaccount:user:export',       '#', 'admin', sysdate(), '', null, '');