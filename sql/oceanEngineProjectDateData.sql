-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量广告账号项目维度日数据', '3', '1', 'oceanEngineProjectDateData', 'data/oceanEngineProjectDateData/index', 1, 0, 'C', '0', '0', 'data:oceanEngineProjectDateData:list', '#', 'admin', sysdate(), '', null, '巨量广告账号项目维度日数据菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量广告账号项目维度日数据查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'data:oceanEngineProjectDateData:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量广告账号项目维度日数据新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'data:oceanEngineProjectDateData:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量广告账号项目维度日数据修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'data:oceanEngineProjectDateData:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量广告账号项目维度日数据删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'data:oceanEngineProjectDateData:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量广告账号项目维度日数据导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'data:oceanEngineProjectDateData:export',       '#', 'admin', sysdate(), '', null, '');