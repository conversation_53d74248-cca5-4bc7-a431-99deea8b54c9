-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量账户数据快照', '2071', '1', 'adsnapshot', 'agtdata/adsnapshot/index', 1, 0, 'C', '0', '0', 'agtdata:adsnapshot:list', '#', 'admin', sysdate(), '', null, '巨量账户数据快照菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量账户数据快照查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'agtdata:adsnapshot:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量账户数据快照新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'agtdata:adsnapshot:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量账户数据快照修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'agtdata:adsnapshot:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量账户数据快照删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'agtdata:adsnapshot:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('巨量账户数据快照导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'agtdata:adsnapshot:export',       '#', 'admin', sysdate(), '', null, '');